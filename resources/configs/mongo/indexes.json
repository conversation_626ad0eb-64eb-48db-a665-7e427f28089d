[{"table": "config_info_api", "name": "config_info_api_idx_1", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "status", "index_type": 1}, {"field_name": "type", "index_type": 1}], "unique": false, "background": true}, {"table": "log_request_send_third_party", "name": "log_request_send_third_party_idx_1", "fields": [{"field_name": "log_id", "index_type": 1}, {"field_name": "merchant_type", "index_type": 1}, {"field_name": "type", "index_type": 1}, {"field_name": "action_time", "index_type": 1}], "unique": false, "background": true}, {"table": "log_request_sms_consent", "name": "log_request_sms_consent_idx_1", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "log_id", "index_type": 1}, {"field_name": "action_time", "index_type": 1}], "unique": false, "background": true}, {"table": "log_request_sms_consent", "name": "log_request_sms_consent_idx_2", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "phone_number", "index_type": 1}, {"field_name": "status", "index_type": 1}], "unique": false, "background": true}, {"table": "attachments", "name": "attachments_idx_1", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "log_id", "index_type": 1}], "unique": false, "background": true}, {"table": "log_request_card", "name": "log_request_card_idx_1", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "log_id_request", "index_type": 1}], "unique": false, "background": true}, {"table": "report_cic", "name": "report_cic_idx_1", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "action_time_day", "index_type": 1}, {"field_name": "action_time", "index_type": -1}, {"field_name": "sol_id", "index_type": 1}, {"field_name": "area_code", "index_type": 1}, {"field_name": "account_id", "index_type": 1}], "unique": false, "background": true}, {"table": "log_pre_approved", "name": "log_pre_approved_idx_1", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "session_id", "index_type": 1}], "unique": false, "background": true}, {"table": "log_pre_approved", "name": "log_pre_approved_idx_2", "fields": [{"field_name": "merchant_id", "index_type": 1}, {"field_name": "log_id", "index_type": 1}], "unique": false, "background": true}]