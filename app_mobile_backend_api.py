#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" Company: MobioVN
    Date created: 2024/04/02
"""
from flask_cors import CORS

from src.apis.api_custom_service import api_custom_mod
from src.apis.v1_0 import *

app.register_blueprint(
    blueprint=api_custom_mod,
    url_prefix="/mobilebackend",
    name="mobilebackend_external_{}".format(api_custom_mod.name),
)

app.register_blueprint(
    blueprint=api_custom_mod,
    url_prefix="",
    name="mobilebackend_internal_{}".format(api_custom_mod.name),
)


CORS(app)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=80, debug=True)
