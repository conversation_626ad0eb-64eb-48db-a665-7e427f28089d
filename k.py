import hashlib
import hmac
import json
from ast import Dict


# 1. <PERSON><PERSON> thông thường
def create_sha256_hash(data: str) -> str:
    return hashlib.sha256(data.encode("utf-8")).hexdigest()


# 2. HMAC-SHA256 (<PERSON><PERSON><PERSON><PERSON><PERSON> nghị)
def create_hmac(data: str, secret_key: str) -> str:
    return hmac.new(secret_key.encode("utf-8"), data.encode("utf-8"), hashlib.sha256).hexdigest()


# Hàm sort object
def sort_dict(data):
    if isinstance(data, dict):
        return {k: sort_dict(v) for k, v in sorted(data.items()) if v is not None}
    if isinstance(data, list):
        return [sort_dict(x) for x in data]
    return data


def normalize_string(data: Dict) -> str:
    """Convert sorted dictionary to normalized string, preserving special characters and emoji"""
    sorted_data = sort_dict(data)
    print(f"Sorted data: {sorted_data}")
    # Chỉ loại bỏ các ký tự cấu trúc JSON
    return (
        json.dumps(sorted_data, ensure_ascii=False)
        .replace("{", "")
        .replace("}", "")
        .replace("[", "")
        .replace("]", "")
        .replace('"', "")
        .replace(" ", "")
    )


# Tạo signature cho API
def create_api_signature(request_body: dict, secret_key: str) -> str:
    # Sort và chuẩn hóa dữ liệu
    normalized_string = normalize_string(request_body)
    print(f"Sorted string: {normalized_string}")
    # Tạo HMAC
    return create_hmac(normalized_string, secret_key)


# Ví dụ sử dụng
if __name__ == "__main__":
    # Test data
    data = {
        "uuid": "da7e3220-d072-40b5-a629-************",
        "content": [{"name": "bot"}],
        "boolean": True,
        "integer": 1,
        "float": 1.1,
        "string": "string",
        "arrayInteger": [1, 2, 3],
        "arrayString": ["a", "b", "c"],
        "arrayBoolean": [True, False, True],
        "arrayFloat": [1.1, 2.2, 3.3],
        "arrayObject": [{"key": "value"}, {"key2": "value2"}],
        "arrayArray": [[1, 2, 3], ["a", "b", "c"]],
        "arrayNull": [None, None, None],
    }
    secret_key = "ABCDEF123456"

    # 1. Tạo signature
    signature = create_api_signature(data, secret_key)
    print(f"Signature: {signature}")
