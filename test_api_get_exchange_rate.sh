#!/bin/bash

# ==== Kiểm tra đủ tham số ====
if [ $# -ne 3 ]; then
  echo "Usage: $0 <channel_id> <currency> <sol_id>"
  exit 1
fi

# ==== Nhận tham số ====
CHANNEL_ID="$1"
CURRENCY="$2"
SOL_ID="$3"

# ==== Cấu hình cố định ====
HOST="***********"
PORT="8080"
URI="/FinCore/GetExchangeRateRService/GetExchangeRate"
URL="http://${HOST}:${PORT}${URI}"

AUTH_NAME="EXIM"
AUTH_PASS="Ord"
AUTH_ENCODED=$(echo -n "${AUTH_NAME}:${AUTH_PASS}" | base64)

# ==== Thời gian hiện tại UTC định dạng yyyy-MM-ddTHH:mm:ss.mmm ====
datetime_part=$(date -u +"%Y-%m-%dT%H:%M:%S")
nanoseconds=$(date -u +"%N")
milliseconds=$(printf "%03d" $((10#${nanoseconds:0:3})))
MESSAGE_DATETIME="${datetime_part}.${milliseconds}"

# ==== UUID ====
REQUEST_UUID=$(uuidgen)

# ==== Payload ====
read -r -d '' PAYLOAD << EOM
{
  "RequestUUID": "$REQUEST_UUID",
  "ChannelId": "$CHANNEL_ID",
  "MessageDateTime": "$MESSAGE_DATETIME",
  "CcyCd": "$CURRENCY",
  "SolId": "$SOL_ID"
}
EOM

# ==== Gửi request ====
RESPONSE_FILE="response.json"

curl -X POST "$URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic $AUTH_ENCODED" \
  -d "$PAYLOAD" \
  -o "$RESPONSE_FILE" \
  --connect-timeout 3 \
  --max-time 5