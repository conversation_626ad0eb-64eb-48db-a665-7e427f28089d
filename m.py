import requests


def download_file_from_ibm_ecm(base_url, repository_id, doc_id, output_file, username, password):
    """

    Tải file từ IBM ECM dựa trên docId.

    Args:

        base_url (str): URL cơ sở của API IBM ECM (ví dụ: 'https://ecm.example.com/').

        repository_id (str): ID của repository trong IBM ECM.

        doc_id (str): ID của tài liệu cần tải.

        output_file (str): Đường dẫn lưu file tải về.

        username (str): Tên người dùng để xác thực.

        password (str): Mật khẩu để xác thực.

    """

    # Endpoint để tải tài liệu

    url = f"{base_url}/{repository_id}/ContentStream/{doc_id}"

    # Xác thực cơ bản (Basic Authentication)

    auth = (username, password)

    try:

        # <PERSON><PERSON><PERSON> yêu cầu GET để tải tài liệu

        response = requests.get(url, auth=auth, stream=True)
        print(response.status_code)
        print(response.text)

        response.raise_for_status()  # Kiểm tra nếu có lỗi HTTP

        # Ghi nội dung tải về vào file

        with open(output_file, "wb") as file:

            for chunk in response.iter_content(chunk_size=8192):

                file.write(chunk)

        print(f"File đã được tải về thành công: {output_file}")

    except requests.exceptions.RequestException as e:

        print(f"Lỗi khi tải file: {e}")


if __name__ == "__main__":

    # Ví dụ sử dụng

    base_url = "http://10.128.29.139:9081/fncmis/resources"  # URL cơ sở của hệ thống ECM
    repository_id = "OBJ"  # ID của repository trong IBM ECM
    doc_id = "idd_704E1597-0000-CA18-89B8-E9EA4D217076"  # ID của tài liệu cần tải
    output_file = "document.pdf"  # Đường dẫn lưu file
    username = "cich2h"  # Tên người dùng
    password = "cich2h"

    download_file_from_ibm_ecm(base_url, repository_id, doc_id, output_file, username, password)
