import os

from mobio.libs.kafka_lib.helpers.ensure_kafka_topic import EnsureKafkaTopic

from configs import KafkaReplication
from configs.kafka_config import KAFKA_TOPIC


def get_requires_topic():
    kafka_topic = KAFKA_TOPIC()
    keys_name = [
        attr for attr in dir(kafka_topic) if not callable(getattr(kafka_topic, attr)) and not attr.startswith("__")
    ]
    topic_value = [getattr(kafka_topic, topic_key) for topic_key in keys_name]

    return topic_value


def create_kafka_topics():
    number_partitions = 8
    required_topics = get_requires_topic()
    new_topics = []

    for topic in required_topics:
        new_topics.append(
            {
                EnsureKafkaTopic.TOPIC_NAME: topic,
                EnsureKafkaTopic.NUM_PARTITIONS: number_partitions,
                EnsureKafkaTopic.CONFIG: {"compression.type": "snappy"},
                EnsureKafkaTopic.REPLICATION_ASSIGNMENT: os.getenv(KafkaReplication.DEFAULT_BROKER_ID_ASSIGN),
            }
        )

    if new_topics:
        EnsureKafkaTopic().create_kafka_topics(new_topics)


if __name__ == "__main__":
    create_kafka_topics()
