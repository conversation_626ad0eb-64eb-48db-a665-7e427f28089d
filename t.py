#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 28/05/2024
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 19/05/2024
"""
import datetime
import uuid

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    KeyConfigNotifySDK,
    StatusCodePushSocket,
)
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_card_model import LogRequestCardModel


class SendRequestCheckUniqueId(object):
    @staticmethod
    def _send(log_id, merchant_id, request_body, action_time, account_id, log_id_start, flow_name):
        log_id_push_socket = log_id_start if log_id_start else log_id
        func_visit = "SendRequestCheckUniqueId"
        data, config = SendRequestCheckUniqueId._build(merchant_id, request_body, account_id)

        MobioLogging().info("send_request_check_unique_id :: data: %s, config :: %s" % (data, config))

        data_response, status_code, reasons = ThirdPartyEIB.check_unique_id(
            log_id, config, data, account_id, merchant_id, log_id_start
        )
        MobioLogging().info(
            "{}:: log_id: {}, status_code: {}, reasons: {}".format(
                func_visit, log_id, status_code, reasons, data_response
            )
        )
        cifInformation = None
        if data_response and data_response.get("errorCode") in ["99", "93"]:
            cifInformation = data_response.get("errorDesc")

        log_request_card = LogRequestCardModel().find_by_log_request_id(merchant_id, log_id_start)
        if not log_request_card:
            MobioLogging().info("SendRequestCheckUniqueId :: not log_request_card")
            return

        lst_func_handle = log_request_card.get("lst_func_handle")
        lst_func_handle.append(func_visit)
        is_push_socket = False
        if "SendRequestCheckCustomerExist" in lst_func_handle:
            is_push_socket = True
        log_request_card_id = log_request_card.get("_id")
        cardInformation = log_request_card.get("cardInformation")
        LogRequestCardModel().update_by_set(
            {"_id": log_request_card_id}, {"cif": cifInformation, "lst_func_handle": lst_func_handle}
        )

        if is_push_socket:
            body_send_socket = {
                "flow_name": flow_name,
                "status": StatusCodePushSocket.SUCCESS,
                "cif": cifInformation,
                "isAuthInformation": log_request_card.get("isAuthInformation"),
                "isAuthTemporary": log_request_card.get("isAuthTemporary"),
                "cardInformation": cardInformation,
                "func_visit": func_visit,
                "log_id": log_id_push_socket,
            }
            data_send_socket = {"data": body_send_socket}
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )

    @staticmethod
    def _build(merchant_id, request_body, account_id):
        config_send = ConfigInfoApiModel().get_config_info_api_check_unique_id(merchant_id)
        if not config_send:
            raise CustomError("Not config send id check unique id")

        data_request = request_body.get("body_check_unique_id")
        messageDateTime = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        data_request.update({"requestUUID": str(uuid.uuid1()), "messageDateTime": messageDateTime})
        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: data_request,
                },
            }
        }

        return data, config_send


if __name__ == "__main__":
    x = {
        "merchant_id": "a0d4a74d-b475-4ef3-af7e-2d890ccce00b",
        "account_id": "afc0da24-ce42-40d0-aa21-6c8124669afe",
        "action_time": "2024-05-28 10:45:54",
        "log_id": "7749e142-1cdf-11ef-8ae1-5b74a0ad6540",
        "log_id_start": "47f6798a-1cdf-11ef-86b9-b56f4f19d75a",
        "request_body": {
            "body_customer_exist": {
                "idCard": "************",
                "name": "Qu\u00e1ch Kh\u00f4i Nguy\u00ean",
                "dateOfBirth": "16/12/2000",
                "gender": "N\u1eef",
                "dateOfIssuance": "14/08/2021",
            },
            "body_check_unique_id": {"uniqueidType": "CCUOC", "uniqueid": "************"},
            "body_check_card": {
                "data_decryption": "2TvnvbFaUMRoeqx5mTCHUGd5JvN6tXqF+qavPRcu1kIUMyfJ/MOyRR2KDL3VGyQsy4nGlMxjAaJHmPWSIw6H8ZYKCmJUIo+1h3Eb/mkF/M2N9qmKplmgwJl6uq8F8WJWlv7Q6z4qVlHEjbPaO1ukoWiLCzRRhvU8VSZ7wuCKxKKD7ZvNciY0RLRE6fVI6UzpYi5hD/7NNmomahAhU229Be2lSpBWHqrpm3ono6l5f7NXZbFDb/RYk/sU7Zh4YLzVAGMlQ/rbIBUPPSIoMPZVzGTpaqyieoodXvUrvN+CqAUiPUZe5PavQgzdFldMazfwg83B2Ht9GndHjChQa3BAf3RyZ67gzH6dONcRF/MVLHtUmRvusluV90eGRkzrs7A0SdWtTCRgHaJnwWRT+2HTk43GEbEPzcrNRuBL1fU16lvCfj439NdmjXlbZ6f3wQmChScC3b9ceBvKyHMQ4IuTh/SNqrosD08bv5pgZ5EaoPlDTgKN4xJlhUh7jlSYU+2unz8eLpCKgD6MXPGChvU+AczrZ1NCdcV1uZbaxoWpwaKHlIYybK6lqNUKxjmkIcR8Gej/Ey/GHQWBTVTbIOl3eXfLKnvJCDcgccE+KNF8BRz2cT9TpzNEV0Bz49qaeTR9ov2er0PlXErQ9/FW9hz0xtiS+Vr4ee13hhKuDbXDHcNjJVImusiHBfL6q55wGD+RzR1Ujs4FL7wdBCIMBMVl8PRK5WtlKutvG1AvsqCNevTgXgwUL53Vinb/sGlZN1dXOFgUWOuW5Tnmdb/3j7J0CGNRYaSnJGbhV9rQgJ/XfZso4I8PCAPfEoF+xi4ylQXpHjFz85HTQtH+nY+WB9pls3WvpLF7WYCNBLs3qfT6F20Gh5s0T8PUgXAgWipbA9yLJEHrhMK4+XTcErVUevgws3NWUgspLBZ+Ul+tyNh5Sam7eAjquLtT84TXwWPXKimV9qnjOMHuBuIkgtm/y4ri+85wPNcNRDq+WA3ggFtMrF10LULvPIsp/0KgFomfOz5+ZmbTQqV1LEMXmDWpdgnGTRv1xPUV/qyBAhCpTUY1huNmv2PqXZs0HfeZ80C55EnVEpDZJOg29xGoZjPC6gQNO3IBYk7/RhjvmNKQPzcGy+N2P0Sm9LUN6TSc/1PZSjc9C+T9JXFsUmicthjMV/l1bG5Qr1z2UjCkUUMKXXjpcJDRt2bGthcqrOcidxpOvpLExxR8Q+gqOR9khSlLZMR+8KXahPcwsS9RGU4OUGja1tmdo2pU7+3vQqwr2MEeIRjEbRuGw4iT+z7DgkjMo1eTYNtICbtptERtfxx/tySmJ5WIm9dH+i4Wig2qCREf/8bgJyWwfd5pVycR0GY8Mj6FkFt5O9492keQNTVYBfAdhkcr0SSF0bik+VpzlKzLKBGtWI5xxqrWieFJgEh9cJqd71x6hwW0xTbuPemRpFmyOGGfwqegof2pXGiMi4vinHqQEdaepwnteXSd6oKBe4vGsv0RqNNQmWQCdmTPVuXdKXZRurBBXxX7LMYNGx5seI2SgL8QdFgNbn7/VMazVKRFRgSX9rg2D/WuOKcB06xDvXaCyqT2qFFG5iwX8smIILFSTo+lKY3cvZgprtYWNqQZlTCdb7+nLo67uNy56v4/xNj8lELDO5eUsg4OGSabY1MBNYbhnwnCBgt7bFHx4z5fDhrqauLfYDQJ5aqQc1KI31jRgwUTjarnuz3YpzEHbv/8i62KBAOgtocctnur9DWQM5r39IjBG+Pwizz0NJ51azfYHF0fgEP5GmF0QryBCzuTX07R6rV9ZJcez92yCeb8qV+HhhVWigw1KRIjJljF/aR9s1hmVPX6hXH2P4SDv4yEvhafL86I5AIRY0KwNaHInN1/k+TmuQbE+kcC3K+zO2cAA76OYTbqobW7Y557qxg+S/xNjzEJtSgcQ29JjsC0Tby/RAJ0QMk2YNkBGREclYHiGdQGZesnDfucOLJfi4K1K6ckNGtBocfgWZlS+35PlDarEVnt1yBnfMorTzEYuTzkAL+MUprcS/qSr7PhWME6zH36fYbj8jL90czkx3c1OV3NNyR0Adg8KP4zkr8LxjWNalKu7DhCO6dorMipAfy9BUtKdOaZd6I+AzDZMWVdOqFUt+p7/eufRyNyesehDEIschpXQQJ/isZI8reL0T4uYH5PbPdewzGvWahgsuuDd0UGoAP2q/bZaOdRLfW6+xP9ed5M8uCImbqpcBwdZlxthoG51GIVkQoDFqQR+ggHAF2vI/FWdQQaTwGQBzVwfZRxZY5Yoso6OtaB4pKanAoWt7lWOvgzhtdEw+SAuthFFY4GuxUzhChXgKBXj2g3uQipflPIHgYyigKLF2UePrsKSxT4XmMpaDo3tvwkgsGeglQfIP+AomahMk4v4ldblWMFAUIGUXcQpLsB8SlA9MPXdSMgszaJIHPEjHHlRWgEFrfFn/tC3z7KNcbg41+3hlIIO2w4B4Jz6hAs47hHMQk9e8wckYTLHUNhoyllw6KsDDDZBWBDrUKkkMBAAeJKTZgvM69iPZy6CwUWmJTw774UYzyq+q0Ep9Ar4/QxXIR3KrAMB0D8To9dXql+Ly5sz+mfUKF+ssAzr3PuYDULDIyXKeF9/HNOgK1Rl7tpZKk02AmOAGCGlS9Se2QsicQKwhpzpTzbJo7JYjnK9YyjZ/Gu8t+Z8aiVqeTy1SN081N5QYPvAe7Bx9mEXRIkNggqO3cR0iBXGc8L0GjzY47BEjFknGae97b6En227pYTD++Ctw8heVNcu8rTeBWvKqZpmYaqy3GFde2HoSww8bW07jgLkYh42NHDKFDjEjTbvk6LoDiR1rntQPlhP4rdZd9ipwkfkZg2c3MJMQyo1mRpsDeCOQ2vKJ501QnJ6F0kfBYCEpGRXP/FbBuUN9p7ZMWvWibvSaMNisiITzLg1e5uAquRxIJ9eA6bVsJINGuSOUATZRCvQug5wzUmY7IOzsySt6k0NzqFsUhtCJzBxk07pwOGOcVjmmF5KpMXOXnjE5+erPruaP5MWAPJ/SkO7Ih7HRqj5dKfuoHr2ilnQXYMx2D52jBYMfGCTkNaAXAKTlB6K/ji6On/ns5r9h4j6ooSYfw+vC3lOPV/TTmGoKwT8YeBLEyZNMiEWgNhIS50L0n3PHskHQ7VH8T/rLvJYXI90UR+dTT5Bz1FLXpkG/dTofyGk6h13W25cEJ2aUZ7DuF5LH3hUJzXVH1obCjj1zRml6/0iWlridqxvDSVKPx8Rj6Oguq4RjiuIEiNIexMGUYISvCSvYIOhJlpYN3b7i6DTMfHqjvNGLsoBu8G3l23ZBgKasNa8pr2Kb4nysL4P5FawML8TpxJrI2Xxh4mraffUudNPr8gFj7pWVBu9vE6107HbFtIRzShv6K7W5K/Bb+xjtYnUkedvmdCG/wtmzFtRru1zjwPlOWD5SFQn9LA/mj3C+t5qF3H87nhLUOFztG7iLUoT1v2b3xh8Abf+rILqqqEYQDaSNzRlxaFKhgeFPjO3wnLwpQLQSNBv7jx1P0FCUiS9nOtiCS8RDOXbYy5DXVfC3jOKPN4GN4ePMypDI+K+S/fvyvHF2oAOxNj+VeMfXwsns6RxqeT4Kq6MqVB29chVr+rjTq3x4zaJnEvV19nNj0zwBIhSrgLL1N7w3pUplMGTZhtwtEGXFAuRdj+qrKyzucFyZBP0d9lz50lrwMwZj8ql54C+xz5qGuuwJg6VXnva7tMbogNs6QT8kzpaGpRiAOf32TAM4gIRIUTFlkLCIujVXnqkYEr9c13wPt+akC7fdfiOOl45h4ZOdfXsSNtED4VO+z7O7dWHelSCkY/4j5Qr4ncwkEIiM/0OEPSwwdbOCsoGxXqaGXGA0qireJrT3KMv0CsElMpb3e368a1CEEAqe5cKNOsZ28AxoiE3PkERA+BPJaPaWbqQyT6Gtses/FoUgWVxDHUuxm6kZT0+Yp8pF2FdXfrX4o7qoDxRclsNxobuDRjkZuQKphVmx1zwGJS/R8DfgWmD5bd34XKGpsp1hgWqZzCpYFPDDkJMZ09YWx3MJl+HLASUSFr7tqnuw0BZ7wvX3V/GE7ZXeXtlPCMv9C2Lt70jYQ4roNZHA5GXYF4hVy5bHcBjfPDqyhlVyJKGhPfIxt0afAJ80uPMuBYOcY5P5kaKfGWQUzS43+bxkQ+Ckf4jWe6Ri9dn9lFZiD0M3SAbR77wJKnNTAyhNx3CQZF2YF4CHzTIDt3/VeRBRLGy4j4Vldq81XuLImVT4jMgY+sUzK/YysUBJWsPsxQTEFL1grY/+r6gquwyIqQtMYhX09srVrBksOI6XnTMWRWXifbJ25GAc3XDES8nzffjvp0dSf7YiRAqgmmK+iRQOMYaIWMotTO4YMsHPS6n/MDOZyFvA2Hxa9aPIkM2MHEJmf7em2iZ8Y74RQkD+76EANW33WzQBEwtMIj+0N6P0UgkaorjCVi0paTPUis1crSnhpgszI2Z69khbZB43wT+i3d33I55VKWisejnghjOiiUfC1VYc8USvEy4QHZYRDA7gvn3acsyDuxMYP+yDz7zIQYqFSLUiaaMxwoS3ZIwPFWipAruuJwOFZUu8hhDIvbIFnYG6/Wk8N5tLX0ZYaVUtd86OuZgB5aJPxhV9l116lCws286wSXXov5gros4ncNl85UTMQvUh3yy7gm6uYC6D72mym1M25505jleKxyTZO1vfPkkh4TdS5rwwXf35+VMIJptl2wM7LK7Oga61uj3zy7Uv+7VIjCHlrDLMW4qq4polSZu/BrK2YC+6rThN8NEe+QKXH+d2NEqf1hbZUbvwztllM1iINCTHAUe1wXedYtVVm1LI0Nk/tkRk9Ev5AzH9K/9nzDqKN14btADEkMSn8gGWrMJejufdIAEPjoMAKCy/tC/pBPCeikHY4v6UK9M0Za2wJkKzYvX0kFpBkZXzqFXA578RTRmeuELRoCXZGA1tZA4fNP6nPNevfKR/wvAkgAkvoNUlO9oYmm3JMC5pjXnjrxbH733bs8k3Zx7K9enuhxU8UIx8wXehAHfBDLXkVvK+kI0fYU0rB/PWSlsKlRiPoG6VZpGw5SepwftrahuoGRYekYYVNRxc5M9eSoEG2ebyhrJS2jE9Q2+nSNvdz/ZiqfhvF5rQocYEIjqtVwbioKtHbv03ve5zyURsRWj87kcW/9CEUmbj2CTj18NBKpOFMgSJ8DfNvsShIvkzwS839oDq8UA0VoJm0eCtWNAFCvF0rMKO0zpE0Lfd19TychZCz4pCas7Q+CT/ec7f3wWydKcjhPDGSRQmLT8CyHMcuz/R0OPKKJ6Bz+JCuaBgijQBpkard6n0DFMXdmK3tQNBe+z8SK/pkEWsKrbuNZQKpUn9QM3MnZ1/HAoXpyXnE/LH1eJeDMBWKLqZXsS5aBNwtc4G6M75CauB+VNxepNotSyGpfgzqEWFmmEIo4S9ZPLje+m1N7RB6hgSi1yJD+aJKPWOPFDND/JvEf1e4PEQj5b4kV1CZRHotlocA71r33o7OgMdYrzVRw7egQzznjkv1LjJfEd7rUYzdEZd1wKcoQqDFmqC1GIWxnCxV9FvjBWW9j+nbhAGlDy9OLSKYxLIrzufa1q1lV0nBPDjoo5hjCK7cF1YYGSr/pRiJSX9HrWLQ/nymQWJXIGGvzZOBEwC7gNWXOuRsUn5etkGltCkToITawGAf5U6CZ0ENNqeuGpVwBFaEZRvZebcsYflMsToeN1onFY/wZEfggzveQ2lb9+u8g8lxFtwhvq155rwAKUAeVVluWiNqDN7mU2UVJrtLqzrE3h7XG3BZImZOU7E2NNWjip84Dt6xkp5oJA8zCfeyP0IvFx86jrLeGJ35iogw/k2BnEh4kAXEjdCCEde82Lbf6NpcGfl0UKNKD/btKBXpL0TBIXsSiSSZh53FXFaOuKcWI6/vIVM1Xzy7w9gSp46sD27cN7p6XZsI8XAurfpCsxI76j9MIM/cLVxZZt89J7u8Hj3h684R9zhPCp0wMaKiSa69jI520Dqtu0T8fFiZIQR5igE2LO0vKjxPuYJk2Hd7p/w993gpapivEIAShDD+OedQxNHWIQtF5o1BEZdE1YUR9Gjmo80cd1tbcYpkWg1hWfpSIOwSRtIwUJnBQa9EJePA1hHLunAGlUrBi25RJd2viNh3c1/ezPn3NoPgh6ws//DfWl97M1xZ2SRF8j8xJD6QiBzik6JSnXqFsarnu/zy/ymnN8mfsYL4Zo1FCSMrN75OQifnEGwrej0i1YNfP1WI9guzu1lI71TppnuxC/S7gfi00nHkMXTpDenLSdaEuM2aQpPokURmzv2yQkD5X+xZUdu4WRP7VPCT46DpNClNJuh1xbmikGvDa4aDckGBR1cYI9rfy2f1hNfD19lVy6aKZMZH8GpwdXbqVx+om2mTBrKAxp4gkICFWvP4FuW8fxyzcEvtkxtTPwmQ4NntlPgvtS4iroKU/kNamdfpC2fnvXOPu9xYWdqRAr8X3yXnYKtBA9axgP86UYLsaIeGP9l7yHr19fQf6Pfz2QyinXzPYjTYkTNByy/t6M+XIy/Y+BXwaI67PCdsNQFyisZrBhYIepkWFD1oigLUDNHqlRxiHbYMBkhQSy3wgzW7Wco8bkIxnlSt6joztCPtVupciL0VmytQbTmIhtygGmEr7vQrxEkv0sXs6qxk3krAFqCJGG/aCE4mJ56opefvW2au2Ims0S0qSTI3W0LznJsbgHag8z80XeSkU7rdxvFxAJ91alMRJDD6cYTB9iIzVU9uRrNtS51oo1kPSDnVdJncDj/x8e3k/jFfeBey2rpvRngrrEihayeFyG3gdEmvGW7JleAagXb18/0apCxnM0Cgd1v5N636S5HIxs9hEacjzqC1ri+GxiRiAgDYk/8mL9vvsURmsjFzSVAl1VqZI1YkQDaNGKIlrZaIfjQlH5Dr9vEbqeK+fijFQFgeX23o0WcBhrDnyaMB33lpISNv7i/HkFIscpfUtfLfhilav1K9A1ypumHVuUlnjo6O7hTObYXxMZS3iRi0tofrdHQZE+DKB4AkLGA5WPFlM44ifAl+WXrbl+FkwbcbjV3TpklFP56zwuiWgbAmWmkjYA5DtR8ddmOGALviDlTkMByoUxC0h81CqhsEUs/fbSkcYrJ4EBK66LWqutlZruao8H1rBwIm2yALpl68mCXqL2UgTTAR2/DG6k/QYJHFglL0xCfbUGf0yeBapbnGQx11KhV9rH2m3+r+CwZA8slrxTIutf25nc0R/6EOyi+YwJaKV8LhTINRfy8xlP8VQA7uozkHUTudEFN7NO14bNOYCBi2GWjtG6LAm2LvD2yg2Zmr5XxbNwkaz1v704D/MIb5veTPsBHdve3+0A1XXmkyn89f/JpBs4wr/OpChSaIf1KMFLobFWptG5mufRZEhwZ+C7Vu9AD2F7abp/m2qToc7EcKTjyyFp6YAJlaSOTFw0x4C7VsFOrTE5oAuUieksG5MxLR+VIu6UoVJT15ZkFRfbtRUq3vf3AJ00unFcoer8+Z/bw9I3vua/o+YDH7kXwXne1MgltoBVJzBDexWrIxEBkhYH1c0uREfvaOgqAIfr3s8u6cW/u5Z+A7PIA7TnTdJ56EQOG3EfDG1R/OnztrPVwmXOcHQtrJXSwpU0nLoTLyxkeyM6DrDK5KwOM/qkbf/xyRO7DZCP4yS/abV1wTTA9sh3tBOV+KFDvnEgGDquSd2fvsTA87MKWpUINYmUQlSvZ7KsRqMuLEuUZtCwKhcbmfMmXgLci3uMq5pddK9jCQHJacUXre3fsSwGDjGvOga0YqwY8ds4gSpGqlI0s1K1p/1PAQncw7GvlJut6nwOb4rHmjNDIxBmPdwY5JmDDMU/vV0TAwMdDTIANFx7hz2fkC5XiKe3PLTr/vvrwgGrXbnbO06NPKT34aUGEaUmI9rDNwctU73ENdyYovu4VLG3hMM36tJRZoDLt8xJxIPxjg5uCyKJu61SG2k3nscvBi/42U0FDQIMr2QoO4az2qLbxpX8ce+015F5vcjZE9WffQkoKfg7IBMVIIsdHzrBkr62dLmrY8TYaCZ6ECm4WKNbERqVJd6jWZKXyN94IO/eqfLYY7jqfNIFbYvoL2bftSizaDh0QtsS4jHvkW3zMsKtmuF742mLQzHRDVUARXF23sorluaNY6tjULS00yqcXL0GcU5s9ex+BCTi4ga2wpUA093sNFBmfWsY8l4mGMDJQ6r7vW7yHXKAzY1ihq7ZtXK3nnlKv6YP4NNzXroNkGykLIyGgrfDwrqS1d0BMg/v2IVgSWMIVI/sWeQOJ47n1iiDwah0c/DkfTuD992B1KRRXGAePb8QB5JTwglgULWyHuTLnlNO8j3Ws9YbRT2fh1mcRylIsIgnrq9u9WqnkcfU2EGCOm+lD9yiQswhU4oz0R5d2LTG/QH3wdC4TvuUT6N0AKQa+ytu32WNQsMy16x1Kl8AaAVPvX6aFTbsf2/l953yRtrgiOWcl5nXR/LuZ13g7GANCHm9YVumYPVNN77YUu1vyggwiQYpz/ByAjDsIgnb0sAXrXvNfYOj0lAGIHd6qSgaUAd+dKSbDCg8DT2oTCGlJuavYzuoIwBCUa6TI/qNxxzHUANnvk+StkonPVi7TH2J8kA4cIPM/bzVsvwX3GX0xrmv20pwzf8QfyZetx56FZ0DnD1YUfllU+6ORwi4en0jvctWdM54IAFGVCvDIC4LNGEd95NBjWMPZey6icUwoC2dRhIVTrZzfLHwS4SdVO5myxw6cmSov75HTeb7p6JnxM4XSkzgc9PWC0B5dqn6EBrLgFJwDmgNf5fm0iPjH3SkpqRIK8OxJePus5MjPVjYc6nQO0ptfdKqadLohyShYtQkmps7JFHo+NekXGTDTtcS8eNfhpcnix+N4afmHd1tyDTslln1Bk9PsHc4XeurVtvP0Ja0fMmUvS1HAdKY8VXrpOtLvJ5J/OJnfEY48t5BkeMDWd2NQhdVK0KWbQGgvrUJWRucVOQEDJvCCdfdkkSbgSpLyV4qnIYZsUa6gWRArG9ThJNNHqpIamailzi1V9+okyRGWPumLfzF7VRYBX2dq9BpbUnSKKzEFjVRjyV4nM/ZZHuYk8aZn2SGpQLX495Q4B5l/XEbh3MLpfZLxJGbvPUMOBAsGcfrSBcl/XGTUgQfpHzVxv3/JBHAIekuLPiGJs0X9uLDrVQA7ReakuC9tlFG4riVdP3vpSDoysAI4p0V8Jl7Q3LIW9IJuiEHD2zVzKXOcD6aRo6pMZSNAv4rSqCZZ1K2SH6w4vsZhgZdHT6m1rcMcE2CquS0YsEAMd5IkkrG5A0PXVEbS75wsQTziE9RvujHS681Q3fZXORNPklyZSs4vopCjmjCxq1L7auF5c+vmmzCJCtkJUER3EKGwK8stOAkTMPj2AQT5ulGkm7CEWFcnEfG+8HVBTrLS/IEFlWm8ranr2pO6de3NE4uaGRAKKOUV7LpxQ1tPU5kpFSemUb/hrsK/eKovfn1XSateeXjVzEgWqAhPxL/tUnKlJxd15Zvco3bUpf8Rf76KNnB0w+DiRqr0bcH2uNlempt+0CRAxpO6TUz8Su68OwibQV14sldeCDoc1LOeFVPEkh1nGsg6U4ThktJPbCmkVFuxvUwsxMS00gOZ/U0wHisK6QkbjxuDaYote8nJtG11/W7s09ijsdTLQPLlXFGe9I3Cbr0KI0TVuiw+5azQfER1m3qm5ZDKOnWzPy5wBb6URygnggP57wH42hH7ZMXmclgymhzq1NbNmUQEdZ/q4bW6jAsoWOWbDnfPcvqKFSaAkdPk5J1F43HWGS7vBoSj2+wKGPhilKFwAbi8EdxAFq87mdC4mcPvvTnTMr7acoDf3jv4FiEz42JpNjPLIXH9iEkVVSeLLSRHwauliXwbXaGe2tffxCw9GGAHrpgjlg6mI1yWW6lx6ikAPKJMEymWp0mll04uPTyBue8kxbXYj/URXh5V9SgQjhhGC145jc+2qAjr+DFJJ2yT/LWlD3pQZFlOF4fNsiHH875pDCYwVmPAzDG0qZP7gKMe516Re/gTivrdY7SfXpOb217DgrL5quI7Hcp42tpmhhOy1cLm/5q60xaAOuPzXF7Bd2UDJJN7nHyl1VvdeOqYsVvGXUov9J2F/gE2TaC/6UQKulnYnjOn4SdzUxHZNsSn8NI3PUAku+OoLKszscC5BMTMUUZ3r0BXfstJmpJ8DzXh7/RskkM7YMnIUVDpdg7yzbdmpgDoMj97t4EdB1Ix0Cg7eRhJ8JPzZ1nIi3wmEyl3i2EyN3euYK9UdQaiSGZ0c7ul/ToGDzPdjzlsLmG/Y9ZcoTUotUkC2zPWEyxH/6oOms7ICR0YL4Gn3e+ZmkSSRo73WA3zvQuLYiw+uVCDfZD5H1K61Rn6wDykRxcyCpc0Rrmg5ZRXoJthXaZEUadgcDfZ5uXPytwxV4kKnQJghQC7Nq9sj5z5/WKj18eiNzXbNlIWQHMyAx0sMjE+Z6eGstuMr3d1hqq+83oZ1DPNTwTRa6qbVAXYekCIbYppXreGSG6WoUkctqLlStQhwyPEtf4p8GnQx6IuiYidX/Gm5ETzPKTcRCewLUCkbizu2HP03gdlCEE6WPQGz3Kt1VAn0wki3ZguRPDQ6fYk/Lu9UolDAMd9o7Jc7Nzn66ZSMac71aW0hkhXJ7eDeb5VyiayR+J/eI9nHJPBmXYYf9VCrjGccEhSkK/mxSM0PDaRKm/MFc5C2P7vqW+FnTSzyKOwAZm8AT17TYvu8o/06H8BZ79wb9n5my2xWoMbw8WO99Beo+l9fAJMdJcn4k9AH6Y6vOER/gY7OSx5bLliuDuvAUghgcpWyTQ90faAjkV7Ge604vclfAgSegQc5WcqhjyV4qLQ0+RboSPQDmV0WYeHLcCgHS40lfg6etymYPw+y7jyjwKKYAFlMuFGQNoXy5GLjcL7t5f4WpMoo/RypPpU4PArjhEDOMDVlVF95YcPaCQpJjd6G2gsvZ1ngo39kqtHLKEyzL2P9MHfH84nsk1PZkZg2F7VhsJcP/+q2si0iMjohO0/2Wt+Mb5IocV2Y+SayS8y3DjHLC/YNEQh1oLqN47E0WCsAWpFe78d55HUg2Fqd32mkpCbCikVMSYvqRDcdrijTgMaf9P4WEKM5H4ZF9IRN3ZkQ3QH8JyM1QWx+6t97SIZwRp9xhyPLJ6MRukVSijGl4getCJrtogC+12CPpC3u1iLessSJ9lQ8F3p6SXd96B2G4mg+4UtbvifGWSPUBdrQEwFRqMzRaaBZoFxinWh9pZ4jHp5WstVEEEBcsYDOssiajwhNbWMfa9uqMMLFiAqjOvVb6VIQPKX5Mhy4J0DafziM6gavIaiPfk6S7Kl4HV764Fp/dBvU3d1MaBSiYZqQ2sKs3w63qCepLO3sA1eqeTWO65fN5Z5wgWM+eIFIlX+hdEmZpo/3N0yaD+G5hDApKR9U5nBtigzdapezwJWTJXNCtUxbSmfk3wJSF/OlxieOTEKD6xP0bx3MBYsF/PzjeQgBxiU0yNHkut/axOWwENu88v5NwQUTB7Fvxji7snJAh47oGuMmLiw7ODIrm1U862gVkhoOPZ0m98NDSoSOIPcPWwnhZNnjRNCeSyeOp3cj6yEa2bVhA+fRsz2dYpDhM907UlDHv7VXY3pNS8Ks3NigwpRFrKpKEIZO+TobhwERuP/Vp+7dvZGnUu2prqzeYJbTeTqtNMBe6AVF6lK4M6AFcU65SLJvrYfyIHdUe7+1Pgzw3698x3q299OGhgKfRFTVzE5OEASDSEPU69Byq8ncH9A8zR1pgDW6yEW2Zfb890j8o2dl+25LwWzl3Rg01KfmXZcv++Tyc+WTfsOihKo1Yxo4Y20iN9uQTrOuQels+GQ4Tjd3EHQXcixDbpicHu7JPE/9Ve2NRQZKMlDF918gZ2x6mqn2gJGEh+ahZ+feqplkgsdZEjZ81DV6c8fbcop/DCQ1xjrqzI0ETu/+ylJRgmchfEK3hhOPKUMEJsJOn7eloFZJTAieXJ8pNfsAaf9WGWH/+V6i1RGn00ZnLxe8D7dKG5x1dCosY3s5lLAQmjxBrsg/HZkLvdGCE1Y7LE2kL7HTwMFyGUxOfUr440KSB7QnNmNpNmAvatIQTTypj6SNmEWwBRGKSoziA+BlsYQ1qs+9PQ4nwe/yeDR9oEihqX3D/Mke9OQVP1Xejk3Eq032JmS6cpCBaIva698evqb3tDgei86pNqnTXQrTWRnRWnuzOtdZiHTGxhIE9gDOxApCGvTNDpmfb0THM5eIUPCUFL6JMWZBZNdCeNXL4zQ1do1MP7d6GUvEpq4rmQwEhkTa1V7fbPLBoLJhS/uAgmO1GG5SzqYcmriArm1hGwjhETmlmqcHfmEMQPCG+xYgFe7mIehct/+ZWTnN/OifHjFp+RjNJYuROIOe3BLSvSJkU3zuhGTZLWeiwQRtSo5osNhvYCZwRH2amRzP8FpEGDUXCBDSjsxreyxIHBJKeyVNOxp8ybCJeV/0NQQD0NtZYoxSoO+ZANwa/4ER41c5QvUj8IPSAKPk9mf4pfJIeW5oHNOV+W2Kw3hU8KwWJXVcDoAAIs1RE8UbebUvgUP6BghfuRkVTKG601osRrYkFRmU1mwIpaxpT9NiKwJtsNChyzi0ogaP/9CzaWm9KzT+d/FsfrbxpXpn7eNgdR4YVWM0K6Dwmg9mia7qSZ5kEuu3C0sVuV8/J69CvIFE9fOeS4h9A5FWdnJO/aRj+lkd3M0ElhhxnGhcC3limThZJoarBeBncyT5esRePhUi8YB6W9A0VDOt2Lmjl7xMFXKs0rnoy4/jNZdrcbdEfo0HmXNXJnRs4jCHgqIGuIn51OQkC9y7edFMZ7PP2DZmidPEOU3IquD6WmiOYpDnV//Wc2LylY3mH9y9ooFjXoWcj1xEynr4YPtnDhajLYncatH/eeVW7NHXK/aiRtt1AyUAab3c82QU0VMUul96ps+7+rtK83OFsNYh0ywkWK68SB/kRAFxSPs3dLnAkhMm0838k0sEBq0u187Bwxv2LueKUd5Myf9+bLTZFn3A8PtWZQDw8h+6InA/Cna6hYqxgy+6djKE+8rgZB5m/3QjjayeA2raAaDWuDpSXFKMtq++v2gJGIyYenxscQ9eO/dHy3uhQyyVldrNIJF735ZgS8/Lp/c2BJra+1U9mHI/2S2+YNI2ReH/U38udG1LRBM0k1bnpVEotiNAIWlDYU7ZI2SrBYSSeHRO/cP5t3yWpJlpQkGjOCC+C2C6itVijGjk0G/RXQ4dqnLVgDQM4ciSPal+ea4aSvp3uzdCIWEazLAxWDcAm1cUtWWC+2Xi3VF446G9EHJNYKF1Oa0S1e/nMuKfkOsjwjqzHkO32sWRRjsDntWJnD9/ybEf0sj1sSkzve1qM0/crGXo1jVo72vDR9Hmd1TQeg7bGLzafbuC3TkwbnODnJO2qWUQUGO56lAedrKDyc2HLyTPgHzWTPgkOSzKi/ZQ4BqNNzSnGMGkbQWakis/Nr+2W8646JxjmttFlVWaO16dZf9FcC75l3SUnS69LJcqVcdXteNSXZrd5evpJ2H6krOCvayQoMcezbGmwXWjG6qQ7s8pOFwbmnwWTkkM2VRCdQeIxCgaWNIj4HWDwBZIfk15IFC8TQkDf4FqIyXSaMVhhyeelvpiQXtaZpHI8ZC2ProsYTMxwM+3ZOXJ46qkdnuiv1I+rrIixgrEficA0cOVcHn0Ed/bnIzZmBlmyRt5JPCTdAYS/wbhxN4OTOcXFn5hNldQc19zHhI2ECgBHWbhlAGIyuKYLu0Dz2VT/6GDxOuE9TR5d2HaITC3QP3nQze2LGUOYQm5qXqGSuqJa4U6jJpH/OTc/1ZIx+DWda8zmdUUqOB4LPcMYvjHPY0JXZfe5xNG3EPkCjmc/Hukm/UZhtSdIHXZQawog/673bf6Mln4baMHAW2Q8BYLpOUx7gCu2d2x8P1RHMp6KMDUlv+y6LA2vEpYMtbm6ueadhRSpnG2nKdrbYAnpyKcYs6CCdIHfEQQFMDwuJ98fAXgEpGCHM3rQmkSrMNp711aqdtpSTmvQge+CWINXGMU2c9RV34efSqRoqCFaESzuVqbzGTXXmEYOA2T4IWcOM4OCmqZtXlWOkMps8tGmEerbxWvTeLHuGWpFL8WmO/hbigF7Xj1+61THaXsuuCBfIKBgnmr+Ia0XGTEwv4XWpLFBBs6/KwHnqx7IAfh3Rox3JjmjcHvJk2i2NOkpUDtGUIByRnwqQM83lw30PssDePhVQ+z7ciaOrXQvzKJoo/Bxj48a3iCeOzm0jYKZHslxm9g2raio2vmFWYSswMi5kvIU6EGxq+iL3PeotcXoBKRg32CmIB/vkKieuUR6mvPzg3slHetf1rDKhcVb2w6FOw9B+px2jkCbvTHo5JG7EwqS4f45vYiAT5OjcQu/t2SMWTc0stJiM6/pl+2GNDpx1kae9sYQFFaAydGgej9V1y6BICuWuOThIM3MEA+Ea3WjcoSy/YNX1Ld1Hn0Qz3VKDrSs9SQg8OrSGN2ehTOuk0DhWrcrqI9JB44blPt42A5etwY6jFq/6Nf6yfbW1vNjV4KLTlm+it/7bV7zEyTS3DmfjKMAZLvlJKXGQvtzaVPoBHTpeFmJ8B8P25ZY3pW/p9/i1Z1ZhyO804d2CRLDDiH94oVrPHUhQzF9EBpI6AuNDTrBHNb/NbjO2qK74+Elk1cydt/o3lZj92W5YRSm3ABRcEj5dcHeYPDHFeYdFf5GfQfVKBw04IXkZwXc/oN+ScHaYAOWrRY0gExuUEkxCxobicKxm5XbJE9Fc4dB+G3xib0//flso1d3wji6SYXe3iwL2cA3HNF9HgpfPAPS/DoKbup7qbBuUEP3GuLECKlyMaXV+l6IEaAQNccR5896uV0VGgIkJJ++sb3rQV3gNtKRHkpUXhddC3twahI3g6U7JEBRFh8NVglBZulYMTEoyE+X4G/KLwfRHLKN4CSXuCd7swkGhxtry7j6+YvkVl3BNFWSlS/qWP6UfbfyluCjbTOoA2024-05-28T10:45:57.716569495Z 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:45:57.716569495Z MCqZiTygyPUyEch1G0PvbjQOK4/pzvJx2Xpq8G3MwU5OhQwvA0L3XltWBWW/Mb+AA+NlS9Slp5ofgDvoVLbqXhB1o0VJBXRTiYyGqs0SYkF2tszh08xGge5hrGOtQgfuG5hIL0Zl/4+1xrBK26xZdqN+P7Bp+mjliF01W24a0S4T9pyKNufEQ3lOWIOSysAPA21kOMgqmrRHds4Lgt7Kqs5+AWVmlDW33wX1gDL6APuTFAD+iBeJOfJ2pOawgkGnAvzdR8MK9foqNMNIcadFXPeLaswpaYDX8pU+J4/x2bCuGRWgJvCUbn3r4A0eTPwK7GxAkKU0ia0O3xuu+6DM7xYZEpaH3LR1DCdpxzg7of24ZaZstE6BUPomfSRNR18ni778zj9+VJQTOPra9ixHRK7flH2Z8HNmOFIHkBed73ZhTYUREK2Q7OnN6ZIsELOTxG0UiNSrHEoWz7TDFncBoqXiMwOIgUx1wC3qmgGMR644MEOSUPQfXn+/9u1Y9+7tbg1eWcZ4iCGojw4EFVDWqFmvLqaX2GL/G7uYmQD3sAZkNSNe71tYx417Fx0qwPF0F70zpgBNGpVixsiitaQuosQkSyD8i7YO4qalpKvJeS81DXsOTsOv0kGnIZbzhHrho5hCGfrJufC823TjYsWPePOtiohjfhMiq0iwAUayy9aQvwkoyHBEefnsoRF6+UA+1WiSNi6n+xwNgH67K8FTOf48uNfWJcP004aGzmGUCFXDIwaNQX2x4cLAXQAKN6rh0viT7N/wM27dtTuFwcG8VgJMuAyDZjZp0AyImjD8qHHJkBnzQYvD4Ifa6TznpCEy0jgp9jQZ6dkkIorOZLBOQgOcFOgy0ZZ5l431aCynRBHwI2srOUKUe9DJt+DU1w8MO9rtU+5qg5kTNrIg7te7ClH2X6HggOQp5ZaIqHChPONyw1b6zyb6c4GaEeaBq9i6q+YBUhIlrOWgus5pyU5Z9ymX4oklaIZLG40Z0tvXr3NJmeOYMvMKmMGRO/omtEAO1VJ4TSeCLHT8deS7w+WxpssexSfdUoMcHwCn7OvJJ6fPdqwT44JNtsPgXJOEzaPSFoQc4Rjg9Vptx3s6SN8G2cb6dktlVV2mRKX9nzmxGuSsR05gDPqsvIQa3aSvodzoTnm0vodv95VmW42sXo64DEyCckQYKM+iMSd7yZsImKTtJWFpPpS2IYKZ4kykDRuYMYkWrazi25dVvasVlilz2UnvxxL05xuXaf9iMVO/Jiftm0RLreJGQtoLVm7IpMvlct8L3Gb8QzYfHd/FhyfJx0zxjCbrKH5EmcEGmvt2B69MXB0pQpmc7IEf5sqdzCGrterRGZQhY0Gc2aj/4vlAbgTo0dOe3zdrO8N+Yl/gkjcb3ZoP6PD8gIRCWPvumta7thDR95ECQK7aJv0a7KavY9DBxpBtuCTmrLQKIKI/l4NPhc7/KosWVei6YTVbfbfy/rYbpEdYy6bvAFoHa85Fq8u6g/kKP2uVDoATcJSYEuOlPu23ryc1TIg7s8WsuoLKcka8YhE8Yu8zOlNjl/9vracnXEG66VyHh8KDQaGJIv4Cfuc4+3SiqodoSlj4Eu3te5ocytu1t3SbhQ+Cw2qHOvNx+lFb9YEMgWlapd/gtlw9ANgGm/dAbcA/0B83QfShym7kWjSdHqsl4TiYdnFeLl5p27X6teSJLGYg2OisjKUF4z2pMtWrYWqW8f9wZGZC8aZQyfg9WAjq4KUUEC9Hz8Ufr+WigwxKORYCP6tMryLK5laLPExJFkhBPrLn+tje76MbIn6tbBXgPhtwvMlKov4rz8cJs4J3u9Wh25gzGuJyCyPZAoB3pWifR7FJ03j2pqnCistn9U+Xcn7vQYYXekEWjcwbzgXg/hxWN0Qk81V6BvCakrYrH8HEWITTFV5Ai4s0pQ4Dq7KfKewTZZtQvOXxLFnb28icS+jovAVNDBGmK8B+Z412A==",
                "sdk_request_round": "OUU=",
                "sdk_request_session": "D213D73E-CED4-4911-8ABE-367F0EAD8994",
                "request_timestamp": "*************",
            },
        },
        "request_type": "send_request_check_unique_id",
        "flow_name": "cbbh_add_profile",
    }
    SendRequestCheckUniqueId._send(
        x.get("log_id"),
        x.get("merchant_id"),
        x.get("request_body"),
        x.get("account_id"),
        x.get("action_time"),
        x.get("log_id_start"),
        x.get("flow_name"),
    )