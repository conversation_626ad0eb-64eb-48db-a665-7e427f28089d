#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 24/07/2024
"""
import argparse

from configs import RedisConf
from src.common import lru_redis_cache

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-key_redis", dest="key_redis", type=str)
    args = parser.parse_args()
    key_redis = args.key_redis
    if not key_redis:
        raise Exception("key_redis is empty")
    result_delete = lru_redis_cache.delete_cache_by_pattern(f"*{RedisConf.CACHE_PREFIX}*{key_redis}*")
    print("result_delete:", result_delete)
