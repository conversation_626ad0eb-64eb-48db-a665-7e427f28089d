<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Căn <PERSON></title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background-color: #f8f9fa;
        }

        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .input-with-icon {
            position: relative;
            display: block;
        }

        .input-with-icon i {
            position: absolute;
            left: 12px;
            top: 12px;
            color: #666;
            width: 18px;
            height: 18px;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
            line-height: 1;
            font-style: normal;
        }

        .input-with-icon i svg {
            width: 100%;
            height: 100%;
            fill: none;
            stroke: currentColor;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
            display: block;
            shape-rendering: geometricPrecision;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .input-with-icon input,
        .input-with-icon select {
            width: 100%;
            padding: 12px 12px 12px 42px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            line-height: 1.5;
            transition: border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
            position: relative;
            z-index: 1;
            background-color: #fff;
        }

        .input-with-icon input:focus,
        .input-with-icon select:focus {
            outline: none;
            border-color: #4361ee;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        .input-with-icon:focus-within i {
            color: #4361ee;
        }

        /* Test với các kích thước input khác nhau */
        .large-input input {
            padding: 16px 16px 16px 46px;
            font-size: 1.1rem;
        }

        .large-input i {
            left: 14px;
            top: 16px;
        }

        .small-input input {
            padding: 8px 8px 8px 38px;
            font-size: 0.9rem;
        }

        .small-input i {
            left: 10px;
            top: 8px;
            width: 16px;
            height: 16px;
        }

        .debug-grid {
            background-image:
                linear-gradient(rgba(255, 0, 0, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 0, 0, 0.1) 1px, transparent 1px);
            background-size: 10px 10px;
        }

        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 12px;
            font-family: monospace;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>Test Căn Giữa Icon</h1>

        <div class="form-group">
            <label>Input bình thường:</label>
            <div class="input-with-icon">
                <i class="icon-user">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                </i>
                <input type="text" placeholder="Nhập họ và tên">
            </div>
        </div>

        <div class="form-group large-input">
            <label>Input lớn:</label>
            <div class="input-with-icon">
                <i class="icon-calendar">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                </i>
                <input type="text" placeholder="DD/MM/YYYY">
            </div>
        </div>

        <div class="form-group small-input">
            <label>Input nhỏ:</label>
            <div class="input-with-icon">
                <i class="icon-location">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                        <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                </i>
                <input type="text" placeholder="Địa chỉ">
            </div>
        </div>

        <div class="form-group">
            <label>Select dropdown:</label>
            <div class="input-with-icon">
                <i class="icon-user">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                </i>
                <select>
                    <option value="">Chọn giới tính</option>
                    <option value="Nam">Nam</option>
                    <option value="Nữ">Nữ</option>
                    <option value="Khác">Khác</option>
                </select>
            </div>
        </div>

        <div class="form-group debug-grid">
            <label>Input với lưới debug (để kiểm tra căn chỉnh):</label>
            <div class="input-with-icon">
                <i class="icon-id-card">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <rect x="2" y="4" width="20" height="16" rx="2"></rect>
                        <path d="M8 12h8"></path>
                        <path d="M8 16h8"></path>
                        <circle cx="8" cy="8" r="2"></circle>
                    </svg>
                </i>
                <input type="text" placeholder="Số CCCD">
            </div>
            <div class="debug-info">
                Icon nên được căn giữa theo chiều dọc của input field
            </div>
        </div>

        <button onclick="checkAlignment()"
            style="margin-top: 20px; padding: 10px 20px; background: #4361ee; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Kiểm tra căn chỉnh
        </button>

        <div id="alignment-results" class="debug-info" style="margin-top: 10px; display: none;"></div>
    </div>

    <script>
        function checkAlignment() {
            const containers = document.querySelectorAll('.input-with-icon');
            const results = document.getElementById('alignment-results');
            let output = 'Kết quả kiểm tra căn chỉnh với text:\n\n';

            containers.forEach((container, index) => {
                const icon = container.querySelector('i');
                const input = container.querySelector('input, select');

                const iconRect = icon.getBoundingClientRect();
                const inputRect = input.getBoundingClientRect();

                // Tính toán vị trí text baseline (khoảng 12px từ top của input)
                const inputStyle = window.getComputedStyle(input);
                const paddingTop = parseFloat(inputStyle.paddingTop);
                const textBaseline = inputRect.top + paddingTop;
                const iconTop = iconRect.top;
                const difference = Math.abs(iconTop - textBaseline);

                output += `Input ${index + 1}:\n`;
                output += `  Icon top: ${iconTop.toFixed(1)}px\n`;
                output += `  Text baseline: ${textBaseline.toFixed(1)}px\n`;
                output += `  Chênh lệch: ${difference.toFixed(1)}px\n`;
                output += `  Căn chỉnh: ${difference < 2 ? 'HOÀN HẢO' : difference < 5 ? 'TỐT' : 'CẦN ĐIỀU CHỈNH'}\n\n`;
            });

            results.innerHTML = output.replace(/\n/g, '<br>');
            results.style.display = 'block';
        }
    </script>
</body>

</html>