apiVersion: apps/v1
kind: Deployment
metadata:
 name: mobilebackend-api-external
 labels:
   app: mobilebackend-api-external
spec:
 replicas: 1
 selector:
   matchLabels:
     app: mobilebackend-api-external
 template:
   metadata:
     labels:
       app: mobilebackend-api-external
   spec:
     serviceAccountName: mobio
     containers:
       - name: mobile-backend
         image: {image}
         imagePullPolicy: IfNotPresent
         command: ["/bin/sh", "-c"]
         args: ["cd $MOBILE_BACKEND_HOME; sh prepare_env.sh && uwsgi --http :80 --wsgi-file app_external_mobile_backend_api.py --callable app --master --processes 4 -b 65536 --lazy --enable-threads"]
         envFrom:
           - configMapRef:
               name: mobio-config
           - secretRef:
               name: mobio-secret
         ports:
           - containerPort: 80
         resources:
           requests:
             memory: 70Mi
             cpu: 80m
           limits:
             memory: 1Gi
             cpu: 800m
         volumeMounts:
           - name: mobio-shared-data
             mountPath: /media/data/resources/
           - name: mobio-public-shared-data
             mountPath: /media/data/public_resources/             
         livenessProbe:
           httpGet:
             port: 80
             path: /api/v1.0/ping
           initialDelaySeconds: 120
           periodSeconds: 5
           timeoutSeconds: 4
           successThreshold: 1
           failureThreshold: 3
     initContainers:
       - name: init-mobile-backend
         image: {image}
         command: [ '/bin/sh', '-c', "cd $MOBILE_BACKEND_HOME; sh prepare_env.sh && sh check_image.sh" ]
         envFrom:
           - configMapRef:
               name: mobio-config
           - secretRef:
               name: mobio-secret
         volumeMounts:
           - name: mobio-shared-data
             mountPath: /media/data/resources/
           - name: mobio-public-shared-data
             mountPath: /media/data/public_resources/
     imagePullSecrets:
       - name: registrypullsecret
     volumes:
       - name: mobio-shared-data
         persistentVolumeClaim:
           claimName: mobio-resources-pvc
       - name: mobio-public-shared-data
         persistentVolumeClaim:
           claimName: mobio-public-resources-pvc           
---
apiVersion: v1
kind: Service
metadata:
 name: mobilebackend-api-external-service
 labels:
   app: mobilebackend-api-external
spec:
 ports:
   - port: 80
 selector:
   app: mobilebackend-api-external
