
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mobilebackend-csm-request-to-third-party
  labels:
    app: mobilebackend-csm-request-to-third-party
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mobilebackend-csm-request-to-third-party
  template:
    metadata:
      labels:
        app: mobilebackend-csm-request-to-third-party
    spec:
      serviceAccountName: mobio
      containers:
        - name: mobile-backend
          image: {image}
          imagePullPolicy: IfNotPresent
          command: [ "/bin/sh", "-c" ]
          args: ["touch /media/data/resources/kafka-liveness-pod/$HOSTNAME; cd $MOBILE_BACKEND_HOME; sh prepare_env.sh && python3.11 -u start_consumers.py request-to-third-party"]
          resources:
            requests:
              memory: 30Mi
              cpu: 40m
            limits:
              memory: 1Gi
              cpu: 500m
          envFrom:
            - configMapRef:
                name: mobio-config
            - secretRef:
                name: mobio-secret
          env:
            - name: kafka_topics
              value: "mobilebackend-request-to-third-party"
            - name: kafka_consumer_group_name
              value: "mobilebackend-request-to-third-party"
            - name: kafka_topic_retry
              value: ""
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            - name: mobio-public-shared-data
              mountPath: /media/data/public_resources/
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - "cat /media/data/resources/kafka-liveness-pod/$HOSTNAME"
            timeoutSeconds: 30
            periodSeconds: 30
            initialDelaySeconds: 300
            failureThreshold: 1
      initContainers:
        - name: init-mobile-backend
          image: {image}
          command: ['/bin/sh', '-c', "cd $MOBILE_BACKEND_HOME; sh prepare_env.sh && sh check_image.sh"]
          envFrom:
            - configMapRef:
                name: mobio-config
            - secretRef:
                name: mobio-secret
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            - name: mobio-public-shared-data
              mountPath: /media/data/public_resources/
      imagePullSecrets:
        - name: registrypullsecret
      volumes:
        - name: mobio-shared-data
          persistentVolumeClaim:
            claimName: mobio-resources-pvc
        - name: mobio-public-shared-data
          persistentVolumeClaim:
            claimName: mobio-public-resources-pvc