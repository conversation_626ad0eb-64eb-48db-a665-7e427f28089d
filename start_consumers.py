#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/04/2024
"""

import os
import sys

from configs.kafka_config import KAFKA_TOPIC
from src.message_queue.kafka.consumer.consumer_build_information_related_profile import (
    BuildInformationRelatedProfileConsumer,
)
from src.message_queue.kafka.consumer.consumer_push_socket_notify_mobile import (
    PushSocketNotifyMobileConsumer,
)
from src.message_queue.kafka.consumer.consumer_send_request_to_third_party import (
    SendRequestToThirdPartyConsumer,
)
from src.message_queue.kafka.consumer.csm_quick_sales_read_card import (
    QuickSalesReadCardConsumer,
)
from src.message_queue.kafka.consumer.csm_quick_sales_res_cif import CsmResCifConsumer
from src.message_queue.kafka.consumer.csm_quick_sales_res_create_account_indiv import (
    CsmResCreateAccConsumer,
)
from src.message_queue.kafka.consumer.csm_quick_sales_res_edigi_account import (
    CsmResEdigiAccConsumer,
)
from src.message_queue.kafka.consumer.csm_quick_sales_res_end import CsmResEndConsumer
from src.message_queue.kafka.consumer.csm_quick_sales_res_submit_lp import (
    CsmResSubmitLpConsumer,
)
from src.message_queue.kafka.consumer.csm_quick_sales_upsert_data_to_crm import (
    CsmQuickSalesUpsertDataToCrmConsumer,
)

if __name__ == "__main__":
    name_queue = sys.argv[1] if len(sys.argv) > 1 else None
    consumer_name = sys.argv[2] if len(sys.argv) > 2 else None

    # Get kafka settings from environment variables
    topic_name = os.environ.get("kafka_topics")
    group_id = os.environ.get("kafka_consumer_group_name")

    if name_queue == "push-socket-notify-mobile":
        consumer = PushSocketNotifyMobileConsumer(topic_name=topic_name, group_id=group_id)
    elif name_queue == "request-to-third-party":
        consumer = SendRequestToThirdPartyConsumer(topic_name=topic_name, group_id=group_id)
    elif name_queue == "build-information-related-profile":
        consumer = BuildInformationRelatedProfileConsumer(topic_name=topic_name, group_id=group_id)
    elif name_queue == KAFKA_TOPIC.QUICK_SALES_RES_CIF:
        consumer = CsmResCifConsumer(topic_name=topic_name, group_id=group_id)
    elif name_queue == KAFKA_TOPIC.QUICK_SALES_REGISTER_EDIGI_ACC:
        consumer = CsmResEdigiAccConsumer(topic_name=topic_name, group_id=group_id)
    elif name_queue == KAFKA_TOPIC.QUICK_SALES_CREATE_ACC:
        consumer = CsmResCreateAccConsumer(topic_name=topic_name, group_id=group_id)
    elif name_queue == KAFKA_TOPIC.QUICK_SALES_END:
        consumer = CsmResEndConsumer(topic_name=topic_name, group_id=group_id)
    elif name_queue == KAFKA_TOPIC.QUICK_SALES_SUBMIT_LP:
        consumer = CsmResSubmitLpConsumer(topic_name=topic_name, group_id=group_id)
    elif name_queue == KAFKA_TOPIC.QUICK_SALES_UPDATE_TO_CRM:
        consumer = CsmQuickSalesUpsertDataToCrmConsumer(topic_name=topic_name, group_id=group_id)
    elif name_queue == KAFKA_TOPIC.QUICK_SALES_READ_CARD:
        consumer = QuickSalesReadCardConsumer(topic_name=topic_name, group_id=group_id)
