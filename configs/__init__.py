#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os

from mobio.sdks.base.configs import ApplicationConfig


class MobileBackendApplicationConfig(ApplicationConfig):
    NAME = "MobileBackend"

    ApplicationConfig.WORKING_DIR = str(os.environ.get("MOBILE_BACKEND_HOME"))
    ApplicationConfig.RESOURCE_DIR = os.path.join(ApplicationConfig.WORKING_DIR, "resources")
    ApplicationConfig.CONFIG_DIR = os.path.join(ApplicationConfig.RESOURCE_DIR, "configs")
    ApplicationConfig.LANG_DIR = os.path.join(ApplicationConfig.RESOURCE_DIR, "lang")

    ApplicationConfig.CONFIG_FILE_PATH = os.path.join(ApplicationConfig.CONFIG_DIR, "mobile_backend.conf")
    ApplicationConfig.LOG_CONFIG_FILE_PATH = os.path.join(ApplicationConfig.CONFIG_DIR, "logging.conf")
    ApplicationConfig.LOG_FILE_PATH = os.path.join(ApplicationConfig.APPLICATION_LOGS_DIR)

    MOBILE_BACKEND_FOLDER_NAME = os.environ.get("MOBILE_BACKEND_FOLDER_NAME")

    ADMIN_HOST = os.environ.get("ADMIN_HOST", "")


class RedisConfig:
    REDIS_URI = os.environ.get("REDIS_URI", "redis://redis-server:6379/0")
    REDIS_BASE_URI = os.environ.get("REDIS_BASE_URI")
    REDIS_BASE_TYPE = os.environ.get("REDIS_BASE_TYPE")
    HOST = os.environ.get("REDIS_HOST")
    PORT = os.environ.get("REDIS_PORT")
    CACHE_PREFIX = "mobile_backend_cache_"


class MongoConfig:
    MONGO_URI = os.environ.get("MOBILE_BACKEND_MONGO_URI")


class KafkaReplication:
    DEFAULT_BROKER_ID_ASSIGN = "DEFAULT_BROKER_ID_ASSIGN"
