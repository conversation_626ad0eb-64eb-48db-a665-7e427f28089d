#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 15/04/2024
"""


class CONSUMER_GROUP:
    PROFILE_REPLY_SMS_CONFIRM_CONSENT = "mobilebackend-profile-reply-sms-confirm-consent"
    REQUEST_TO_THIRD_PARTY = "mobilebackend-request-to-third-party"
    PUSH_SOCKET_NOTIFY_MOBILE = "mobilebackend-push-socket-notify-mobile"
    BUILD_FILE_EVIDENT_PROFILE = "mobilebackend-build-file-evident-profile"
    HANDLE_BUILD_INFORMATION_RELATED_PROFILE = "mobilebackend-handle-build-information-related-profile"
    QUICK_SALES_RES_CIF = "mobilebackend-quick-sales-res-cif"
    QUICK_SALES_REGISTER_EDIGI_ACC = "mobilebackend-quick-sales-register-edigi-acc"
    QUICK_SALES_CREATE_ACC = "mobilebackend-quick-sales-create-acc"
    QUICK_SALES_END = "mobilebackend-quick-sales-end"
    QUICK_SALES_SUBMIT_LP = "mobilebackend-quick-sales-submit-lp"
    QUICK_SALES_READ_CARD = "mobilebackend-quick-sales-read-card"


class KAFKA_TOPIC:
    PROFILE_REPLY_SMS_CONFIRM_CONSENT = "mobilebackend-profile-reply-sms-confirm-consent"
    PUSH_SOCKET_NOTIFY_MOBILE = "mobilebackend-push-socket-notify-mobile"
    REQUEST_TO_THIRD_PARTY = "mobilebackend-request-to-third-party"

    QUICK_SALES_READ_CARD = "mobilebackend-send-request-citizen-ids-read-only-information"

    HANDLE_BUILD_INFORMATION_RELATED_PROFILE = "mobilebackend-handle-build-information-related-profile"
    QUICK_SALES_RES_CIF = "mobilebackend-quick-sales-res-cif"
    QUICK_SALES_REGISTER_EDIGI_ACC = "mobilebackend-quick-sales-register-edigi-acc"
    QUICK_SALES_CREATE_ACC = "mobilebackend-quick-sales-create-acc"
    QUICK_SALES_END = "mobilebackend-quick-sales-end"
    QUICK_SALES_SUBMIT_LP = "mobilebackend-quick-sales-submit-lp"
    QUICK_SALES_UPDATE_TO_CRM = "mobilebackend-quick-sales-update-to-crm"
