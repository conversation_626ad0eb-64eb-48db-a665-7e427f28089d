#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 21/06/2024
"""
import base64
import json


# Function to replace newline characters
def replace_newlines(s):
    return (
        s.replace("\\r\\n", "")
        .replace("\\r", "")
        .replace("\\n", "")
        .replace("\r\n", "")
        .replace("\r", "")
        .replace("\n", "")
    )


# Main function
def main():
    try:
        with open("/Users/<USER>/Downloads/request_check_face_22_20.json", "r", encoding="utf-8") as file:
            json_txt = file.read()

        json_data = json.loads(json_txt)

        request_json = json_data["request"]
        payload_json = request_json["payload"]
        data_request = payload_json["dataRequest"]
        data_request_json = json.loads(data_request)

        raw_img1 = replace_newlines(data_request_json["rawImg1"])
        raw_img2 = replace_newlines(data_request_json["rawImg2"])

        response = json_data["response"]
        response_json = json.loads(response)
        data_response = response_json["dataResponse"]
        data_response_json = json.loads(data_response)

        img1 = replace_newlines(data_response_json["img1"])
        img2 = replace_newlines(data_response_json["img2"])

        img1_new = img1[:-1]  # equivalent to StringUtils.substring(img1, 0, img1.length() - 1)
        img2_new = img2[:-1]  # equivalent to StringUtils.substring(img2, 0, img2.length() - 1)

        img1 += raw_img1
        img2 += raw_img2

        print(img2)
        imgdata = base64.b64decode(s=img2)
        filename = "some_image1.jpeg"  # I assume you have a way of picking unique filenames
        with open(filename, "wb") as f:
            f.write(imgdata)

    except json.JSONDecodeError as e:
        print(f"JSON decoding error: {e}")
    except IOError as e:
        print(f"IO error: {e}")
        
def test():
    x = """iVBORw0KGgoAAAANSUhEUgAAAlgAAAMgCAIAAABwAouTAAAAAXNSR0IArs4c6QAAAANzQklUCAgI\\n2+FP4AAABYtJREFUeJztwTEBAAAAwqD1T20MH6AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\\nvgb9WwABl4i59gAAAABJRU5ErkJggg==\\n"""
    try:
        # Clean and fix base64 string
        x = replace_newlines(x)
        
        # Decode base64
        imgdata = base64.b64decode(x)
        
        # Write to file
        filename = "some_image1.jpeg"
        with open(filename, "wb") as f:
            f.write(imgdata)
        print(f"Successfully saved image to {filename}")
        
    except Exception as e:
        print(f"Error processing base64 image: {e}")
        print("Base64 string preview (first 100 chars):", x[:100])
        print("Base64 string length:", len(x))

if __name__ == "__main__":
    test()
