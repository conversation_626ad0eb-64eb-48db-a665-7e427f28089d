# K<PERSON> hoạch triển khai Form Khách hàng nâng cao

## Cấu trúc HTML

### 1. <PERSON><PERSON><PERSON><PERSON> cấu hình API Endpoint
```html
<div class="api-config-container">
  <div class="api-config-header">
    <h3>Cấu hình API Endpoint</h3>
    <button class="toggle-btn" id="toggleApiConfig" aria-expanded="false">
      <span class="toggle-icon"></span>
    </button>
  </div>
  <div class="api-config-content" id="apiConfigContent">
    <div class="form-group">
      <label for="apiEndpoint">Domain API</label>
      <div class="input-with-icon">
        <i class="icon-server"></i>
        <input type="text" id="apiEndpoint" name="apiEndpoint" 
               placeholder="https://api.my-service.com" 
               aria-describedby="apiEndpointHelp">
        <small id="apiEndpointHelp">Nh<PERSON>p địa chỉ domain của API</small>
      </div>
    </div>
    <button type="button" id="saveApiConfig" class="btn btn-primary">Lưu Cấu hình</button>
  </div>
</div>
```

### 2. Form chính với các fieldset
```html
<form id="customer-form" action="/pbuser/mobilebackend/mockup-data/api/v1/customer-form" method="post">
  <input type="hidden" id="customer_id" name="customer_id" value="">
  
  <div class="form-grid">
    <!-- Thông tin cá nhân -->
    <fieldset class="form-section">
      <legend>Thông tin cá nhân</legend>
      
      <div class="form-group">
        <label for="name" class="required">Họ và tên</label>
        <div class="input-with-icon">
          <i class="icon-user"></i>
          <input type="text" id="name" name="name" required aria-required="true">
          <div class="error-message" id="name-error"></div>
        </div>
      </div>
      
      <!-- Các trường thông tin cá nhân khác -->
    </fieldset>
    
    <!-- Thông tin gia đình -->
    <fieldset class="form-section">
      <legend>Thông tin gia đình</legend>
      <!-- Các trường thông tin gia đình -->
    </fieldset>
    
    <!-- Thông tin địa chỉ -->
    <fieldset class="form-section">
      <legend>Thông tin địa chỉ</legend>
      <!-- Các trường thông tin địa chỉ -->
    </fieldset>
    
    <!-- Cài đặt xác thực -->
    <fieldset class="form-section">
      <legend>Cài đặt xác thực</legend>
      <!-- Các toggle switch xác thực -->
    </fieldset>
  </div>
  
  <div class="form-actions">
    <button type="submit" id="submit-btn" class="btn btn-primary" disabled>Tạo khách hàng</button>
    <button type="button" id="reset-btn" class="btn btn-danger">Reset</button>
  </div>
</form>
```

### 3. Toast Notification
```html
<div class="toast-container">
  <div class="toast" id="toast" role="alert" aria-live="polite">
    <div class="toast-content">
      <i class="icon-check"></i>
      <span id="toast-message">Đã lưu cấu hình thành công!</span>
    </div>
    <button class="toast-close" aria-label="Đóng thông báo">×</button>
  </div>
</div>
```

## CSS Styling

### 1. Biến CSS và Reset
```css
:root {
  /* Color Palette */
  --color-primary: #4361ee;
  --color-primary-dark: #3f37c9;
  --color-success: #4cc9f0;
  --color-error: #f72585;
  --color-background: #f8f9fa;
  --color-card: #ffffff;
  --color-text: #333333;
  --color-text-light: #666666;
  --color-border: #e0e0e0;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Border Radius */
  --border-radius: 8px;
  
  /* Transitions */
  --transition-duration: 0.3s;
  --transition-timing: ease-in-out;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
  background-color: var(--color-background);
  color: var(--color-text);
  line-height: 1.6;
  padding: var(--spacing-lg);
}
```

### 2. Layout Grid
```css
.container {
  max-width: 1200px;
  margin: 0 auto;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.form-section {
  background-color: var(--color-card);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  border: 1px solid var(--color-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-section legend {
  font-weight: bold;
  padding: 0 var(--spacing-sm);
  color: var(--color-primary);
}
```

### 3. Form Elements
```css
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--color-text);
}

.input-with-icon {
  position: relative;
}

.input-with-icon i {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-light);
}

.input-with-icon input,
.input-with-icon select,
.input-with-icon textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) calc(var(--spacing-lg) + var(--spacing-sm));
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: border-color var(--transition-duration) var(--transition-timing),
              box-shadow var(--transition-duration) var(--transition-timing);
}

.input-with-icon input:focus,
.input-with-icon select:focus,
.input-with-icon textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.required:after {
  content: " *";
  color: var(--color-error);
}

.error-message {
  color: var(--color-error);
  font-size: 0.875rem;
  margin-top: var(--spacing-xs);
  min-height: 1.25rem;
}

.input-error {
  border-color: var(--color-error) !important;
}
```

### 4. Buttons
```css
.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-duration) var(--transition-timing),
              transform var(--transition-duration) var(--transition-timing);
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(1px);
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-primary:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.btn-danger {
  background-color: var(--color-error);
  color: white;
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}
```

### 5. Toggle Switches
```css
.toggle-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-right: var(--spacing-sm);
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: var(--transition-duration);
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: var(--transition-duration);
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--color-primary);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--color-primary);
}

input:checked + .slider:before {
  transform: translateX(26px);
}
```

### 6. API Config Section
```css
.api-config-container {
  background-color: rgba(76, 201, 240, 0.1);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  border: 1px solid rgba(76, 201, 240, 0.3);
}

.api-config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  cursor: pointer;
}

.api-config-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--color-primary);
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  width: 24px;
  height: 24px;
  position: relative;
}

.toggle-icon {
  display: block;
  position: relative;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary);
  transition: transform var(--transition-duration) var(--transition-timing);
}

.toggle-icon:before,
.toggle-icon:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary);
  transition: transform var(--transition-duration) var(--transition-timing);
}

.toggle-icon:before {
  transform: translateY(-6px);
}

.toggle-icon:after {
  transform: translateY(6px);
}

.toggle-btn[aria-expanded="true"] .toggle-icon {
  transform: rotate(45deg);
}

.toggle-btn[aria-expanded="true"] .toggle-icon:before {
  transform: translateY(0) rotate(90deg);
}

.toggle-btn[aria-expanded="true"] .toggle-icon:after {
  transform: translateY(0) rotate(90deg);
}

.api-config-content {
  padding: 0 var(--spacing-md) var(--spacing-md);
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-duration) var(--transition-timing);
}

.api-config-content.expanded {
  max-height: 200px;
}
```

### 7. Toast Notification
```css
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.toast {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--color-success);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: var(--spacing-md);
  transform: translateX(120%);
  transition: transform var(--transition-duration) var(--transition-timing);
}

.toast.show {
  transform: translateX(0);
}

.toast-content {
  display: flex;
  align-items: center;
}

.toast-content i {
  margin-right: var(--spacing-sm);
}

.toast-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: var(--spacing-md);
}
```

### 8. Responsive Design
```css
@media (max-width: 576px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}
```

## JavaScript

### 1. API Endpoint Configuration
```javascript
// Toggle API Config Section
const toggleApiConfig = document.getElementById('toggleApiConfig');
const apiConfigContent = document.getElementById('apiConfigContent');

toggleApiConfig.addEventListener('click', function() {
  const expanded = this.getAttribute('aria-expanded') === 'true';
  this.setAttribute('aria-expanded', !expanded);
  if (expanded) {
    apiConfigContent.classList.remove('expanded');
  } else {
    apiConfigContent.classList.add('expanded');
  }
});

// Save API Endpoint to localStorage
const saveApiConfig = document.getElementById('saveApiConfig');
const apiEndpoint = document.getElementById('apiEndpoint');

// Load saved endpoint on page load
document.addEventListener('DOMContentLoaded', function() {
  const savedEndpoint = localStorage.getItem('customerFormApiEndpoint');
  if (savedEndpoint) {
    apiEndpoint.value = savedEndpoint;
  }
});

saveApiConfig.addEventListener('click', function() {
  const endpoint = apiEndpoint.value.trim();
  if (endpoint) {
    localStorage.setItem('customerFormApiEndpoint', endpoint);
    showToast('Đã lưu cấu hình thành công!');
  } else {
    showToast('Vui lòng nhập địa chỉ domain API!', 'error');
  }
});
```

### 2. Form Validation
```javascript
// Form validation
const form = document.getElementById('customer-form');
const submitBtn = document.getElementById('submit-btn');
const requiredFields = form.querySelectorAll('[required]');
const dateFields = [document.getElementById('dob'), document.getElementById('date_of_issue')];
const idCardField = document.getElementById('id_card');

// Validate on input for all required fields
requiredFields.forEach(field => {
  field.addEventListener('input', validateField);
  field.addEventListener('blur', validateField);
});

// Validate date fields
dateFields.forEach(field => {
  field.addEventListener('blur', validateDateFormat);
});

// Validate ID card
idCardField.addEventListener('blur', validateIdCard);

function validateField() {
  const errorElement = document.getElementById(`${this.id}-error`);
  if (!this.value.trim()) {
    this.classList.add('input-error');
    errorElement.textContent = 'Trường này là bắt buộc';
    return false;
  } else {
    this.classList.remove('input-error');
    errorElement.textContent = '';
    checkFormValidity();
    return true;
  }
}

function validateDateFormat() {
  const errorElement = document.getElementById(`${this.id}-error`);
  const datePattern = /^(\d{2})\/(\d{2})\/(\d{4})$/;
  const dateStr = this.value.trim();
  
  if (!dateStr) {
    if (this.hasAttribute('required')) {
      this.classList.add('input-error');
      errorElement.textContent = 'Trường này là bắt buộc';
      return false;
    }
    return true;
  }
  
  if (!datePattern.test(dateStr)) {
    this.classList.add('input-error');
    errorElement.textContent = 'Vui lòng nhập ngày theo định dạng DD/MM/YYYY';
    return false;
  }
  
  const matches = dateStr.match(datePattern);
  const day = parseInt(matches[1], 10);
  const month = parseInt(matches[2], 10);
  const year = parseInt(matches[3], 10);
  
  // Check if date is valid
  if (month < 1 || month > 12 || day < 1 || day > 31) {
    this.classList.add('input-error');
    errorElement.textContent = 'Ngày không hợp lệ. Vui lòng kiểm tra lại.';
    return false;
  }
  
  // Further validation for specific months
  const daysInMonth = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  // Adjust February for leap years
  if (year % 400 === 0 || (year % 100 !== 0 && year % 4 === 0)) {
    daysInMonth[2] = 29;
  }
  
  if (day > daysInMonth[month]) {
    this.classList.add('input-error');
    errorElement.textContent = 'Ngày không hợp lệ cho tháng đã chọn.';
    return false;
  }
  
  this.classList.remove('input-error');
  errorElement.textContent = '';
  checkFormValidity();
  return true;
}

function validateIdCard() {
  const errorElement = document.getElementById(`${this.id}-error`);
  const idCardValue = this.value.trim();
  
  if (!idCardValue) {
    if (this.hasAttribute('required')) {
      this.classList.add('input-error');
      errorElement.textContent = 'Trường này là bắt buộc';
      return false;
    }
    return true;
  }
  
  // Check if ID card is numeric and has 9 or 12 digits
  if (!/^\d+$/.test(idCardValue)) {
    this.classList.add('input-error');
    errorElement.textContent = 'Số CCCD chỉ được chứa các chữ số';
    return false;
  }
  
  if (idCardValue.length !== 9 && idCardValue.length !== 12) {
    this.classList.add('input-error');
    errorElement.textContent = 'Số CCCD phải có 9 hoặc 12 chữ số';
    return false;
  }
  
  this.classList.remove('input-error');
  errorElement.textContent = '';
  checkFormValidity();
  return true;
}

function checkFormValidity() {
  let isValid = true;
  
  // Check all required fields
  requiredFields.forEach(field => {
    if (!field.value.trim() || field.classList.contains('input-error')) {
      isValid = false;
    }
  });
  
  // Enable/disable submit button
  submitBtn.disabled = !isValid;
}

// Form submission
form.addEventListener('submit', function(event) {
  let isValid = true;
  
  // Validate all required fields
  requiredFields.forEach(field => {
    if (!validateField.call(field)) {
      isValid = false;
    }
  });
  
  // Validate date fields
  dateFields.forEach(field => {
    if (!validateDateFormat.call(field)) {
      isValid = false;
    }
  });
  
  // Validate ID card
  if (!validateIdCard.call(idCardField)) {
    isValid = false;
  }
  
  if (!isValid) {
    event.preventDefault();
  }
});
```

### 3. Toast Notification
```javascript
// Toast notification
function showToast(message, type = 'success') {
  const toast = document.getElementById('toast');
  const toastMessage = document.getElementById('toast-message');
  
  toastMessage.textContent = message;
  
  if (type === 'error') {
    toast.style.backgroundColor = 'var(--color-error)';
  } else {
    toast.style.backgroundColor = 'var(--color-success)';
  }
  
  toast.classList.add('show');
  
  // Auto hide after 3 seconds
  setTimeout(() => {
    toast.classList.remove('show');
  }, 3000);
}

// Close toast on click
document.querySelector('.toast-close').addEventListener('click', function() {
  document.getElementById('toast').classList.remove('show');
});
```

### 4. Reset Form
```javascript
// Reset form
const resetBtn = document.getElementById('reset-btn');

resetBtn.addEventListener('click', function() {
  document.getElementById('form-title').innerText = 'Tạo khách hàng mới';
  document.getElementById('submit-btn').innerText = 'Tạo khách hàng';
  form.reset();
  document.getElementById('customer_id').value = '';
  
  // Clear all error messages
  document.querySelectorAll('.error-message').forEach(element => {
    element.textContent = '';
  });
  
  // Remove error class from all inputs
  document.querySelectorAll('.input-error').forEach(element => {
    element.classList.remove('input-error');
  });
  
  // Disable submit button
  submitBtn.disabled = true;
});
```

## Biểu tượng SVG

Để thêm biểu tượng vào các trường input, chúng ta sẽ sử dụng SVG inline. Dưới đây là một số biểu tượng SVG cơ bản:

```html
<!-- User icon -->
<svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
  <circle cx="12" cy="7" r="4"></circle>
</svg>

<!-- ID Card icon -->
<svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <rect x="2" y="4" width="20" height="16" rx="2"></rect>
  <path d="M8 12h8"></path>
  <path d="M8 16h8"></path>
  <circle cx="8" cy="8" r="2"></circle>
</svg>

<!-- Calendar icon -->
<svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
  <line x1="16" y1="2" x2="16" y2="6"></line>
  <line x1="8" y1="2" x2="8" y2="6"></line>
  <line x1="3" y1="10" x2="21" y2="10"></line>
</svg>

<!-- Location icon -->
<svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
  <circle cx="12" cy="10" r="3"></circle>
</svg>

<!-- Document icon -->
<svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
  <polyline points="14 2 14 8 20 8"></polyline>
  <line x1="16" y1="13" x2="8" y2="13"></line>
  <line x1="16" y1="17" x2="8" y2="17"></line>
  <polyline points="10 9 9 9 8 9"></polyline>
</svg>

<!-- Server icon -->
<svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
  <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
  <line x1="6" y1="6" x2="6.01" y2="6"></line>
  <line x1="6" y1="18" x2="6.01" y2="18"></line>
</svg>

<!-- Check icon -->
<svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <polyline points="20 6 9 17 4 12"></polyline>
</svg>
```

## Kết luận

Kế hoạch triển khai này cung cấp một hướng dẫn chi tiết để tạo ra một form khách hàng hiện đại, đáp ứng và đầy đủ tính năng. Khi chuyển sang Code mode, bạn có thể sử dụng các đoạn mã này để xây dựng form hoàn chỉnh.

Các tính năng chính bao gồm:
1. Bố cục responsive với CSS Grid
2. Phân nhóm trường thông tin hợp lý
3. Validation client-side chi tiết
4. Biểu tượng cho các trường input
5. Hiệu ứng chuyển động mượt mà
6. Cải thiện khả năng tiếp cận
7. Khối cấu hình API Endpoint với localStorage
8. Thông báo toast khi lưu cấu hình thành công