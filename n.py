#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 06/06/2025
"""

from src.helpers.internal.mobio.profiling import ProfilingHelper
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)

if __name__ == "__main__":

    lst_log_customer_full_flow = LogCustomerFullFlowQuickSalesModel().find({})
    for log_customer_full_flow in lst_log_customer_full_flow:
        merchant_id = log_customer_full_flow.get("merchant_id")
        profile_id = log_customer_full_flow.get("profile_id")

        form_data_submit = log_customer_full_flow.get("form_data_submit", {})
        response_create_cif = form_data_submit.get("response_create_cif", {})
        if not response_create_cif:
            continue
        profile_cif = response_create_cif.get("CustId", "")
        r_update_profile = ProfilingHelper().update_cif_profile_by_profile_id(merchant_id, profile_id, profile_cif)
        print(r_update_profile)
