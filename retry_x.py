#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 29/05/2025
"""

import datetime

from mobio.libs.logging import MobioLogging

from configs.kafka_config import KAFKA_TOPIC
from src.message_queue.kafka.producer.push_message_to_topic import Producer

if __name__ == "__main__":
    merchant_id = "a0d4a74d-b475-4ef3-af7e-2d890ccce00b"
    staff_id = "46852a36-d7fe-4feb-9c0f-db97a0c675e4"
    object_id = "4aff6955-227d-4754-8a6e-52ff406279fb"
    action_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S.%f")
    log_customer_full_flow_id = "6846b86abc7eb926c6479d9c"

    log_topic_kafka_id = Producer().send_message_to_topic(
        KAFKA_TOPIC.QUICK_SALES_RES_CIF,
        {
            "log_customer_full_flow_id": log_customer_full_flow_id,
            "merchant_id": merchant_id,
            "staff_id": staff_id,
            "action_time": action_time,
            "result_of_previous_step": {},
        },
    )
    MobioLogging().info("RetryFormQuickSales:process_retry_form_to_third_party:log_id: %s" % log_topic_kafka_id)
