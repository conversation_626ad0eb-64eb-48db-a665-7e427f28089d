import base64
import json

from cryptography.fernet import Fernet


class JsonCrypto:
    def __init__(self, secret_key=None):
        """
        Khởi tạo với secret key có sẵn hoặc tạo key mới
        secret_key phải là base64 encoded key
        """
        if secret_key:
            self.key = secret_key.encode()
        else:
            self.key = Fernet.generate_key()
        self.cipher_suite = Fernet(self.key)

    def get_key(self):
        """Trả về secret key dưới dạng string"""
        return self.key.decode()

    def encrypt_json(self, data):
        """
        Mã hóa JSON data
        Input: Python dict/list
        Output: Encrypted string (base64 encoded)
        """
        # Convert data to JSON string
        json_str = json.dumps(data)
        # Encrypt
        encrypted_data = self.cipher_suite.encrypt(json_str.encode())
        # Convert to base64 string for easy storage/transmission
        return base64.b64encode(encrypted_data).decode()

    def decrypt_json(self, encrypted_data):
        """
        Giải mã encrypted data thành JSON
        Input: Encrypted string (base64 encoded)
        Output: Python dict/list
        """
        try:
            # Convert from base64 string
            encrypted_bytes = base64.b64decode(encrypted_data)
            # Decrypt
            decrypted_data = self.cipher_suite.decrypt(encrypted_bytes)
            # Parse JSON
            return json.loads(decrypted_data)
        except Exception as e:
            raise Exception(f"Decryption failed: {str(e)}")


# Example usage
def demo():
    # Sample data
    data = {
        "profile_id": "aa6c36ef-2b68-453c-a516-f88b39d9a9ce",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "form_id": "123123",
        "active_time": "2025-03-20 10:00:00.000",
        "staff_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
    }

    # Create instance with new key
    crypto = JsonCrypto("Y3rKXXri_R34NcUlXK7I4hyEVsp_zaaFrqLIZR4gl84=")
    print(f"Generated Key: {crypto.get_key()}")

    # Encrypt
    encrypted = crypto.encrypt_json(data)
    print(f"\nEncrypted data:\n{encrypted}")

    # Decrypt
    decrypted = crypto.decrypt_json(encrypted)
    print(f"\nDecrypted data:\n{decrypted}")

    # Verify if original and decrypted data match
    print(f"\nOriginal and decrypted data match: {data == decrypted}")


if __name__ == "__main__":
    demo()
