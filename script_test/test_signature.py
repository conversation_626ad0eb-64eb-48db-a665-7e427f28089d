#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 20/05/2024
"""

import base64

from cryptography.exceptions import InvalidSignature
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.serialization import (
    load_pem_private_key,
    load_pem_public_key,
)


def gen_signature(private_key_path, data):
    """
    Generates a signature for the provided data using the private key.

    Args:
        private_key_path (str): Path to the PEM encoded private key file.
        data (str): The data to sign.

    Returns:
        str: Base64 encoded signature.
    """
    with open(private_key_path, "rb") as key_file:
        private_key = load_pem_private_key(key_file.read(), password="Mobio123".encode("utf-8"), backend=None)
    signature = private_key.sign(
        data.encode("utf-8"),
        padding.PSS(
            mgf=padding.MGF1(hashes.SHA256()),
            salt_length=padding.PSS.MAX_LENGTH,
        ),
        hashes.SHA256(),
    )
    return base64.b64encode(signature).decode("utf-8")


def verify_signature(public_key_path, data, signature):
    """
    Verifies the signature for the provided data using the public key.

    Args:
        public_key_path (str): Path to the PEM encoded public key file.
        data (str): The data to verify.
        signature (str): Base64 encoded signature.

    Returns:
        bool: True if the signature is valid, False otherwise.
    """
    with open(public_key_path, "rb") as key_file:
        public_key = load_pem_public_key(key_file.read(), backend=None)
    try:
        public_key.verify(
            base64.b64decode(signature),
            data.encode("utf-8"),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH,
            ),
            hashes.SHA256(),
        )
        return True
    except InvalidSignature:
        return False


if __name__ == "__main__":
    dataToSign = "1312312"
    signature = gen_signature("PrivateKey.pem", dataToSign)
    is_valid = verify_signature("pub.pem", dataToSign, signature)
    print(f"Signature: {signature}")
    print(f"Valid Signature: {is_valid}")
