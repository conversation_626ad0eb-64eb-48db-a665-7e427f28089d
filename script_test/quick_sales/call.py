#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 16/04/2024
"""


import base64
import copy
import datetime
import hashlib
import json
import re
import sys
import time
import uuid
from random import Random

import jwt
from Crypto import Random
from Crypto.Cipher import AES
from Crypto.Util import Counter
from mobio.libs.logging import MobioLogging

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    ConstantKeyConfigSendThirdParty,
    StatusCode,
    ThirdPartyType,
)
from src.common.requests_retry import RequestRetryAdapter
from src.common.utils import utf8_to_ascii
from src.helpers.cms_signature import CMSSignedData
from src.helpers.thirdparty.eib.base_send_request import BaseSendRequestThirdParty


class BaseQuickSales:

    def __init__(self):
        self.logger = MobioLogging()

    def sha512_hash(self, text):
        return hashlib.sha512(text.encode("utf-8")).hexdigest()

    def create_jwt(self, jwt_key, jwt_secret, jwt_audience, expires_in_seconds=86400):
        """
        Creates a JWT token in Python.

        Args:
            jwt_key (str): Key ID for the token.
            jwt_secret (str): Secret key used for signing the token.
            jwt_audience (str): Intended audience for the token.
            expires_in_seconds (int, optional): Expiration time in seconds. Defaults to 30.

        Returns:
            str: The encoded JWT token.
        """

        # Ensure secret is bytes in UTF-8 encoding
        secret_bytes = jwt_secret.encode("utf-8")

        # Create the JWT payload
        payload = {
            "iat": datetime.datetime.now(datetime.UTC),  # Issued at
            "exp": datetime.datetime.now(datetime.UTC) + datetime.timedelta(seconds=expires_in_seconds),  # Expiration
            "aud": jwt_audience,
        }

        # Create the JWT token with PyJWT library
        algorithm = "HS256"

        token = jwt.encode(payload, secret_bytes, algorithm=algorithm, headers={"kid": jwt_key})
        return token

    def build_headers(self, config, payload):
        """
        Builds the headers for the request.

        Args:
            config (dict): Configuration for the request.

        Returns:
            dict: The headers for the request.
        """

        jwt_key = config.get(ConstantKeyConfigSendThirdParty.JWT_KEY)
        jwt_secret = config.get(ConstantKeyConfigSendThirdParty.JWT_SECRET)
        jwt_audience = config.get(ConstantKeyConfigSendThirdParty.JWT_AUDIENCE)
        jwt_token = self.create_jwt(jwt_key, jwt_secret, jwt_audience)
        x_client_id = config.get(ConstantKeyConfigSendThirdParty.X_CLIENT_ID)
        # string_body = "".join(list(payload.values()))
        plaintext = json.dumps(payload, separators=(",", ":"), ensure_ascii=False)
        hash_plaintext = self.sha512_hash(plaintext)
        self.logger.info("build_headers :: hash_plaintext :: {}".format(hash_plaintext))
        signature_body = self.build_signature_by_plaintext(config, hash_plaintext)
        basic_token = self.create_basic_auth(
            config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME), config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        )

        headers = {
            "Content-Type": "application/json",
            "X-Authorization": "Bearer " + jwt_token,
            "X-ClientId": x_client_id,
            "X-Signature": signature_body,
            "Authorization": "Basic " + basic_token,
        }
        return headers

    def fix_header_value(self, header_value):
        # Remove leading whitespace
        header_value = header_value.strip()

        # Convert to bytes
        header_value_bytes = header_value.encode("utf-8")

        # Base64 encode and decode to remove invalid characters
        encoded_value = base64.b64encode(header_value_bytes)
        decoded_value = base64.b64decode(encoded_value)

        # Convert back to string
        fixed_value = decoded_value.decode("utf-8")

        return fixed_value

    def encrypt(self, key, plaintext):
        iv = Random.new().read(AES.block_size)

        ctr = Counter.new(128, initial_value=int.from_bytes(iv, byteorder="big"))

        cipher = AES.new(bytes(key, "utf-8"), AES.MODE_CTR, counter=ctr)
        return base64.b64encode(iv + cipher.encrypt(bytes(plaintext, "utf-8")))

    def decrypt(self, key, ciphertext):
        enc = base64.urlsafe_b64decode(ciphertext)
        iv = enc[: AES.block_size]
        ctr = Counter.new(128, initial_value=int.from_bytes(iv, byteorder="big"))

        cipher = AES.new(bytes(key, "utf-8"), AES.MODE_CTR, counter=ctr)

        return cipher.decrypt(enc[AES.block_size :]).decode("utf-8")

    def build_signature_by_plaintext(self, config, plaintext):
        certificates_path = config.get(ConstantKeyConfigSendThirdParty.CERTIFICATES_PATH)
        pem_pass_phrase = config.get(ConstantKeyConfigSendThirdParty.PEM_PASS_PHRASE)
        # signature
        key_signer, cert_signer = CMSSignedData.read_key_cer_p12(
            private_key_path=certificates_path, certificate_password=pem_pass_phrase
        )
        signature = CMSSignedData.sign_message(
            plaintext, key_signer, cert_signer, digest_alg=config.get(ConstantKeyConfigSendThirdParty.DIGEST_ALG)
        )
        signed_cert_b64 = signature.decode("utf-8").replace("\n", "")
        return signed_cert_b64

    def gen_signature_vnpay(self, config, plaintext):
        certificates_path = config.get(ConstantKeyConfigSendThirdParty.VNPAY_CERTIFICATES_PATH)
        # pem_pass_phrase = config.get(ConstantKeyConfigSendThirdParty.PEM_PASS_PHRASE)
        # signature
        private_key = CMSSignedData.load_rsa_private_key(certificates_path)

        # Generate signature
        signature = CMSSignedData.gen_signature_by_file_rsa(private_key, plaintext)
        return signature

    def create_basic_auth(self, user_name: str, password: str) -> str:
        auth_str = f"{user_name}:{password}"
        auth_bytes = auth_str.encode("ascii")
        base64_bytes = base64.b64encode(auth_bytes)
        base64_str = base64_bytes.decode("ascii")
        return base64_str

    def handle_response(
        self,
        key_get_response_code,
        key_get_response_desc,
        response,
        mapping_code_message_error,
        mapping_code_message_success,
    ):
        data_response = None
        reasons = ""
        status_code = None
        try:
            data_response = response.json()
            ResponseCode = data_response.get(key_get_response_code)
            ResponseDesc_response = data_response.get(key_get_response_desc)

            status_code = StatusCode.THIRD_PARTY_FAILURE
            if not ResponseCode:
                reasons = mapping_code_message_error.get(ResponseCode)
            if mapping_code_message_success.get(ResponseCode):
                status_code = StatusCode.SUCCESS
            else:
                if mapping_code_message_error.get(ResponseCode):
                    reasons = mapping_code_message_error.get(ResponseCode)
                else:
                    reasons = "Mã code lỗi {} chưa được khai báo".format(ResponseCode)

        except Exception as e:  # noqa
            MobioLogging().error("ThirdPartyEIB :: send_sms :: {}".format(e))
            result = response.text
            if not re.search("Successful|0</ResponseCode", result):
                reasons = result
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        return data_response, status_code, reasons


class CallQuickSales(BaseQuickSales):

    def send_sms(self, log_id, config, data):

        access_token = config.get(ConstantKeyConfigSendThirdParty.SMS_ACCESS_TOKEN) or ""

        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)
        access_token = config.get(ConstantKeyConfigSendThirdParty.SMS_ACCESS_TOKEN) or ""

        phone_number = to_data.get(NOTIFICATION_TO_STRUCTURE.PHONE_NUMBER)
        body_data_content = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        message = utf8_to_ascii(body_data_content)
        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")

        data_encrypted = config.get(ConstantKeyConfigSendThirdParty.DATA_ENCRYPTED).format(
            log_id=log_id, phone_number=phone_number, message=message
        )
        # MobioLogging().info("EibSender::data:: %s" % plaintext)
        ciphertext = self.encrypt(access_token, data_encrypted)
        ciphertext = ciphertext.decode("utf-8")

        # signature
        msg_signature = str(log_id) + str(ClientId) + sent_time + data_encrypted
        signed_cert_b64 = self.build_signature_by_plaintext(config, msg_signature)

        payload = {
            "RequestId": log_id,
            "ClientId": ClientId,
            "Timestamp": sent_time,
            "DataEncrypted": ciphertext,
            "Signature": signed_cert_b64,
            "ReferenceNo": str(uuid.uuid1()),
            "PhoneNumber": phone_number,
            "Message": message,
        }

        headers = self.build_headers(config, payload)
        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        info_request = {"headers": headers, "payload": payload, "config": config}
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, str(e)

        return self.handle_response(
            "ResponseCode",
            "ResponseDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def read_card(self, log_id, config, data, merchant_id, account_id, log_id_start, sdk_request_id):
        request_type = ThirdPartyType.SEND_REQUEST_READ_CARD

        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)

        payload = {
            "requestId": log_id,
            "clientId": ClientId,
            "timestamp": sdk_request_id,
            "dataRequest": dataRequest,
            "signature": "null",
        }

        headers = self.build_headers(config, payload)

        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: read_card :: response :: {}".format(response.text))

        return self.handle_response(
            "responseCode",
            "responseMessage",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def check_card(self, log_id, config, data, merchant_id, account_id, log_id_start, sdk_request_id):
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_CARD

        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        method_request_log = config.get(ConstantKeyConfigSendThirdParty.METHOD_REQUEST_LOG)
        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        payload = {
            "requestId": log_id,
            "clientId": ClientId,
            "timestamp": sdk_request_id,
            "dataRequest": dataRequest,
            "signature": "null",
        }

        headers = self.build_headers(config, payload)

        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:

            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: read_card :: response :: {}".format(response.text))
        # save log
        response_text = response.text
        BaseSendRequestThirdParty().push_socket_send_request_insert_data_log(
            merchant_id=merchant_id,
            request=payload,
            response_text=response_text,
            method=method_request_log,
            error_message=reasons,
            account_id=account_id,
            log_id_start=log_id_start,
        )
        return self.handle_response(
            "responseCode",
            "responseMessage",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def check_face(self, log_id, config, data, merchant_id, account_id, log_id_start, sdk_request_id, id_card_no=None):
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_FACE

        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        method_request_log = config.get(ConstantKeyConfigSendThirdParty.METHOD_REQUEST_LOG)
        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)

        payload = {
            "requestId": log_id,
            "clientId": ClientId,
            "timestamp": sdk_request_id,
            "dataRequest": dataRequest,
            "signature": "null",
        }

        payload_send_log = copy.deepcopy(payload)
        payload_send_log["idCardNo"] = id_card_no

        headers = self.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            response_text = response.text if not isinstance(response, str) else response

            BaseSendRequestThirdParty().push_socket_send_request_insert_data_log(
                merchant_id=merchant_id,
                request=payload_send_log,
                response_text=response_text,
                method=method_request_log,
                error_message=reasons,
                account_id=account_id,
                log_id_start=log_id,
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: check_face :: response :: {}".format(response.text))
        response_text = response.text
        BaseSendRequestThirdParty().push_socket_send_request_insert_data_log(
            merchant_id=merchant_id,
            request=payload_send_log,
            response_text=response_text,
            method=method_request_log,
            error_message=reasons,
            account_id=account_id,
            log_id_start=log_id,
        )

        return self.handle_response(
            "responseCode",
            "responseDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def verify_image(self, log_id, config, data, merchant_id, account_id, log_id_start):
        request_type = ThirdPartyType.SEND_REQUEST_VERIFY_IMAGE

        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)
        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        sent_time = datetime.datetime.now(datetime.UTC)
        sent_timestamp = sent_time.strftime("%Y-%m-%d %H:%M:%S")

        payload = {
            "requestId": log_id,
            "channel": channel,
            "timestamp": sent_timestamp,
            "data": dataRequest,
        }
        plan_text = payload.get("requestId") + payload.get("timestamp") + payload.get("data").get("image")
        signed_cert_b64 = self.build_signature_by_plaintext(config, plan_text)
        payload["signature"] = signed_cert_b64

        headers = self.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: verify_image :: response :: {}".format(response.text))
        return self.handle_response(
            "code",
            "desc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def check_customer_exists(self, log_id, config, data, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: check_customer_exists :: log_id :: {}, config :: {}, data :: {}".format(
                log_id, config, data
            )
        )
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_EXIST

        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = data
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        msg_signature = "".join(
            [
                log_id,
                sent_time,
                dataRequest.get("idCard"),
                dataRequest.get("name"),
                dataRequest.get("dateOfBirth"),
                dataRequest.get("gender"),
                dataRequest.get("dateOfIssuance"),
            ]
        )
        signed_cert_b64 = self.gen_signature_vnpay(config, msg_signature)

        payload = {
            "requestId": log_id,
            "timestamp": sent_time,
            "channel": channel,
            "data": dataRequest,
            "signature": signed_cert_b64,
        }

        headers = self.build_headers(config, payload)

        info_request = {"headers": headers, "payload": payload, "config": config}

        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: check_customer_exists :: response :: {}".format(response.text))

        return self.handle_response(
            "code",
            "desc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def save_customer(self, log_id, config, data, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: save_customer :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )
        request_type = ThirdPartyType.SEND_REQUEST_SAVE_CUSTOMER

        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = data
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        msg_signature = "".join(
            [
                log_id,
                sent_time,
                dataRequest.get("idCard"),
                dataRequest.get("name"),
                dataRequest.get("dateOfBirth"),
                dataRequest.get("gender"),
                dataRequest.get("dateOfIssuance"),
            ]
        )
        signed_cert_b64 = self.gen_signature_vnpay(config, msg_signature)
        payload = {
            "requestId": log_id,
            "timestamp": sent_time,
            "channel": channel,
            "data": dataRequest,
            "signature": signed_cert_b64,
        }

        headers = self.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:

            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: save_customer :: response :: {}".format(response.text))
        return self.handle_response(
            "code",
            "desc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def insert_id_check(self, log_id, config, data, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: insert_id_check :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )
        request_type = ThirdPartyType.SEND_REQUEST_INSERT_ID_CHECK

        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = data

        payload = {
            "channel": channel,
        }
        payload.update(dataRequest)

        headers = self.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: insert_id_check :: response :: {}".format(response.text))
        try:
            data_response_json = response.json()
            status = data_response_json.get("status")
            throwException = data_response_json.get("throwException")
            if status != "SUCCESS" or not status:
                reasons = throwException
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        except Exception as e:  # noqa
            MobioLogging().error("ThirdPartyEIB :: insert_id_check :: log_id :: error :: {}".format(log_id, str(e)))
            result = response.text
            if not re.search("SUCCESS", result):
                reasons = result
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        return data_response_json, status_code, reasons

    def id_check_insert_data_log(self, log_id, config, data, account_id, merchant_id, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: id_check_insert_data_log :: log_id :: {}, config :: {}, data :: {}".format(
                log_id, config, data
            )
        )
        request_type = ThirdPartyType.SEND_REQUEST_ID_CHECK_INSERT_DATA_LOG

        # channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = data

        headers = self.build_headers(config, dataRequest)
        info_request = {"headers": headers, "payload": dataRequest, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=dataRequest,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: id_check_insert_data_log :: response :: {}".format(response.text))
        try:
            data_response_json = response.json()
            status = data_response_json.get("status")
            throwException = data_response_json.get("throwException")
            if status != "SUCCESS" or not status:
                reasons = throwException
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        except Exception as e:  # noqa
            MobioLogging().error("ThirdPartyEIB :: insert_id_check :: log_id :: error :: {}".format(log_id, str(e)))
            result = response.text
            if not re.search("SUCCESS", result):
                reasons = result
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        return data_response_json, status_code, reasons

    def check_unique_id(self, log_id, config, data, account_id, merchant_id, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: check_unique_id :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_UNIQUE_ID

        dataRequest = data

        headers = self.build_headers(config, dataRequest)
        info_request = {"headers": headers, "payload": dataRequest, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=dataRequest,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: check_unique_id :: response :: {}".format(response.text))
        return self.handle_response(
            "errorCode",
            "errorDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def ret_cust_add(self, log_id, config, data, account_id, merchant_id, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: ret_cust_add :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )

        dataRequest = data

        headers = self.build_headers(config, dataRequest)

        payload = {
            "RequestUUID": str(uuid.uuid1()),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000"),
        }
        payload.update(dataRequest)

        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: ret_cust_add :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: ret_cust_add :: response text :: {}".format(response.text))
        self.logger.info("ThirdPartyEIB :: ret_cust_add :: response status code :: {}".format(response.status_code))
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info("ThirdPartyEIB :: ret_cust_add :: response data :: {}".format(data_response_json))
            return data_response_json, status_code, response
        return data_response_json, status_code, response

    def register_edigi_account(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: register_edigi_account :: log_id :: {}, config :: {}, data :: {}".format(
                log_id, config, data
            )
        )
        dataRequest = data

        headers = self.build_headers(config, dataRequest)

        payload = {
            "RequestUUID": str(uuid.uuid1()),
        }
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        msg_signature = ""
        for key in ["name", "accountNo", "cifCore", "userAlias", "username", "mobileOtp", "emailReceived"]:
            msg_signature += dataRequest.get(key)

        signed_cert_b64 = self.build_signature_by_plaintext(config, msg_signature)

        payload = {
            "signature": signed_cert_b64,
        }
        payload.update(dataRequest)
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: register_edigi_account :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: register_edigi_account :: response text :: {}".format(response.text))
        self.logger.info(
            "ThirdPartyEIB :: register_edigi_account :: response status code :: {}".format(response.status_code)
        )
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info(
                "ThirdPartyEIB :: register_edigi_account :: response data :: {}".format(data_response_json)
            )
            return data_response_json, status_code, response
        return data_response_json, status_code, response

    def register_vtop_publish(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: register_vtop_publish :: log_id :: {}, config :: {}, data :: {}".format(
                log_id, config, data
            )
        )

        dataRequest = data

        headers = self.build_headers(config, dataRequest)
        data_response_json = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=dataRequest,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: register_vtop_publish :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: register_vtop_publish :: response text :: {}".format(response.text))
        self.logger.info(
            "ThirdPartyEIB :: register_vtop_publish :: response status code :: {}".format(response.status_code)
        )
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info("ThirdPartyEIB :: register_vtop_publish :: response data :: {}".format(data_response_json))
            return data_response_json, StatusCode.SUCCESS, response
        return data_response_json, status_code, response

    def create_caacct_add_indiv(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: create_caacct_add_indiv :: log_id :: {}, config :: {}, data :: {}".format(
                log_id, config, data
            )
        )

        dataRequest = data

        headers = self.build_headers(config, dataRequest)
        data_response_json = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=dataRequest,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: create_caacct_add_indiv :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: create_caacct_add_indiv :: response text :: {}".format(response.text))
        self.logger.info(
            "ThirdPartyEIB :: create_caacct_add_indiv :: response status code :: {}".format(response.status_code)
        )
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info(
                "ThirdPartyEIB :: create_caacct_add_indiv :: response data :: {}".format(data_response_json)
            )
            return data_response_json, StatusCode.SUCCESS, response
        return data_response_json, status_code, response

    def check_cif_for_lien(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: check_cif_for_lien :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )

        dataRequest = data

        headers = self.build_headers(config, dataRequest)
        payload = {
            "RequestUUID": str(uuid.uuid1()),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000"),
        }
        payload.update(dataRequest)
        data_response_json = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: check_cif_for_lien :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: check_cif_for_lien :: response text :: {}".format(response.text))
        self.logger.info(
            "ThirdPartyEIB :: check_cif_for_lien :: response status code :: {}".format(response.status_code)
        )
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info("ThirdPartyEIB :: check_cif_for_lien :: response data :: {}".format(data_response_json))
            return data_response_json, StatusCode.SUCCESS, response
        return data_response_json, status_code, response

    def check_so_dep(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: check_so_dep :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )

        dataRequest = data

        payload = {
            "RequestUUID": str(uuid.uuid1()),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000"),
        }
        payload.update(dataRequest)

        headers = self.build_headers(config, dataRequest)
        data_response_json = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: check_so_dep :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: check_so_dep :: response text :: {}".format(response.text))
        self.logger.info("ThirdPartyEIB :: check_so_dep :: response status code :: {}".format(response.status_code))
        if response.status_code == 200:
            data_response_json = response.json()
            status_code = StatusCode.SUCCESS
            self.logger.info("ThirdPartyEIB :: check_so_dep :: response data :: {}".format(data_response_json))
            return data_response_json, status_code, response
        return data_response_json, status_code, response

    def get_loai_details(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: get_loai_details :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )

        dataRequest = data

        payload = {
            "RequestUUID": str(uuid.uuid1()),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000"),
        }
        payload.update(dataRequest)

        headers = self.build_headers(config, dataRequest)
        data_response_json = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: get_loai_details :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: get_loai_details :: response text :: {}".format(response.text))
        self.logger.info("ThirdPartyEIB :: get_loai_details :: response status code :: {}".format(response.status_code))
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info("ThirdPartyEIB :: get_loai_details :: response data :: {}".format(data_response_json))
            return data_response_json, status_code, response
        return data_response_json, status_code, response

    def get_danh_sach_so_dep(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: get_danh_sach_so_dep :: log_id :: {}, config :: {}, data :: {}".format(
                log_id, config, data
            )
        )

        dataRequest = data

        payload = {
            "RequestUUID": str(uuid.uuid1()),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000"),
        }
        payload.update(dataRequest)

        headers = self.build_headers(config, dataRequest)
        data_response_json = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: get_danh_sach_so_dep :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: get_danh_sach_so_dep :: response text :: {}".format(response.text))
        self.logger.info(
            "ThirdPartyEIB :: get_danh_sach_so_dep :: response status code :: {}".format(response.status_code)
        )
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info("ThirdPartyEIB :: get_danh_sach_so_dep :: response data :: {}".format(data_response_json))
            return data_response_json, status_code, response
        return data_response_json, status_code, response

    def send_otp(self, log_id, config, data, account_id, merchant_id, log_id_start):

        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        access_token = config.get(ConstantKeyConfigSendThirdParty.SMS_ACCESS_TOKEN) or ""

        phone_number = to_data.get(NOTIFICATION_TO_STRUCTURE.PHONE_NUMBER)
        body_data_content = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        phone_number = to_data.get(NOTIFICATION_TO_STRUCTURE.PHONE_NUMBER)

        body_data_content = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        message = utf8_to_ascii(body_data_content)
        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")

        data_encrypted = config.get(ConstantKeyConfigSendThirdParty.DATA_ENCRYPTED).format(
            log_id=log_id, phone_number=phone_number, message=message
        )
        # MobioLogging().info("EibSender::data:: %s" % plaintext)
        ciphertext = self.encrypt(access_token, data_encrypted)
        ciphertext = ciphertext.decode("utf-8")

        # signature
        msg_signature = str(log_id) + str(ClientId) + sent_time + data_encrypted
        signed_cert_b64 = self.build_signature_by_plaintext(config, msg_signature)

        payload = {
            "SendSMSRequestDto": {
                "RequestId": log_id,
                "ClientId": ClientId,
                "Timestamp": sent_time,
                "DataEncrypted": ciphertext,
                "Signature": signed_cert_b64,
                "ReferenceNo": str(uuid.uuid1()),
                "PhoneNumber": phone_number,
                "Message": message,
            }
        }

        headers = self.build_headers(config, payload)
        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        info_request = {"headers": headers, "payload": payload, "config": config}
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, str(e)

        return self.handle_response(
            "ResponseCode",
            "ResponseDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def lan_lock_ulock(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: lan_lock_ulock :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )
        dataRequest = data

        payload = {
            "RequestUUID": str(uuid.uuid1()),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000"),
            "ChanelId": config.get(ConstantKeyConfigSendThirdParty.CHANNEL),
        }
        payload.update(dataRequest)

        headers = self.build_headers(config, dataRequest)
        data_response_json = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: lan_lock_ulock :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: lan_lock_ulock :: response text :: {}".format(response.text))
        self.logger.info("ThirdPartyEIB :: lan_lock_ulock :: response status code :: {}".format(response.status_code))
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info("ThirdPartyEIB :: lan_lock_ulock :: response data :: {}".format(data_response_json))
            return data_response_json, status_code, response
        return data_response_json, status_code, response

    def check_aml(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: check_aml :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )

        dataRequest = data

        headers = self.build_headers(config, dataRequest)
        data_response_json = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=dataRequest,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: check_aml :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: check_aml :: response text :: {}".format(response.text))
        self.logger.info("ThirdPartyEIB :: check_aml :: response status code :: {}".format(response.status_code))
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info("ThirdPartyEIB :: check_aml :: response data :: {}".format(data_response_json))
            return data_response_json, status_code, response
        return data_response_json, status_code, response

    def insert_in4_dsa(self, log_id, config, data, account_id, merchant_id, log_id_start):
        self.logger.info(
            "ThirdPartyEIB :: insert_in4_dsa :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )

        dataRequest = data

        payload = {
            "RequestUUID": str(uuid.uuid1()),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000"),
        }
        payload.update(dataRequest)

        headers = self.build_headers(config, dataRequest)
        data_response_json = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            return data_response_json, status_code, response
        self.logger.info("ThirdPartyEIB :: insert_in4_dsa :: time_request :: {}".format(time_request))
        self.logger.info("ThirdPartyEIB :: insert_in4_dsa :: response text :: {}".format(response.text))
        self.logger.info("ThirdPartyEIB :: insert_in4_dsa :: response status code :: {}".format(response.status_code))
        if response.status_code == 200:
            status_code = StatusCode.SUCCESS
            data_response_json = response.json()
            self.logger.info("ThirdPartyEIB :: insert_in4_dsa :: response data :: {}".format(data_response_json))
            return data_response_json, status_code, response
        return data_response_json, status_code, response


if __name__ == "__main__":
    file_setup = sys.argv[1] if len(sys.argv) > 1 else None

    if not file_setup:
        print("Please provide the config file and function name")
        sys.exit(1)

    with open(file_setup, "r") as f:
        data_setup = json.load(f)

    call_quick_sales = CallQuickSales()
    log_id = str(uuid.uuid1())
    function_name = data_setup.get("function")
    print("function_name", function_name)

    try:
        data_response_json, status_code, response = getattr(call_quick_sales, function_name)(
            log_id,
            data_setup.get("config"),
            data_setup.get("data"),
            data_setup.get("account_id"),
            data_setup.get("merchant_id"),
            data_setup.get("log_id_start"),
        )
        print("data_response_json", data_response_json)
        print("status_code", status_code)
        print("response", response)

    except Exception as e:
        print(e)
        sys.exit(1)
