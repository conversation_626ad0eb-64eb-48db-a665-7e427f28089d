{"function": "ret_cust_add", "config": {"channel": "CRM_UAT", "uri": "http://crm-dev.eximbank.com.vn:2006/api/v1/RetCustAdd", "auth_name": "CRM", "auth_pass": "crm@13579", "mapping_code_message_error": {"99": "<PERSON><PERSON> thống gi<PERSON> đ<PERSON>", "102": "<PERSON><PERSON><PERSON> dạng file ảnh chỉ chấp nhận jpq, jpeg, png", "109": "<PERSON><PERSON><PERSON> thước <PERSON>nh không hợp lệ. <PERSON><PERSON> lòng chọn kích thước 720p - 1080p - 2k", "500": "Lỗi không thể tạo chữ ký", "908": "<PERSON><PERSON><PERSON><PERSON> thông tin header", "909": "Dữ liệu mã hoá thiếu hoặc sai trường thông tin", "1001": "<PERSON><PERSON> ký số không hợp lệ", "1002": "<PERSON> định dạng bản tin", "1006": "Lỗi giải mã dữ liệu", "1013": "Lỗi kết n<PERSON>i hệ thống không xác định", "1015": "Lỗi xác thực", "1016": "<PERSON><PERSON><PERSON> c<PERSON>u xác thực không hợp lệ", "1017": "Lỗi truy cập hệ thống", "1019": "Ảnh ch<PERSON>p không hợp lệ", "1021": "<PERSON><PERSON><PERSON><PERSON> phải người thật", "1028": "Lỗi d<PERSON>ch vụ", "1030": "Lỗi không xác định", "1031": "<PERSON><PERSON> liệu <PERSON> không hợp lệ", "1040": "License partner c<PERSON><PERSON>h<PERSON><PERSON> hợp lệ hoặc đã hết hạn", "1041": "<PERSON><PERSON><PERSON> vụ không đư<PERSON><PERSON> đăng ký sử dụng", "1042": "EPAY đã sử dụng hết giới hạn tài nguyên dịch vụ", "1043": "Partner không tìm thấy thông tin giấy phép", "1061": "<PERSON><PERSON><PERSON> dạng <PERSON>nh không hợp lệ", "1068": "<PERSON><PERSON><PERSON> th<PERSON>i gian yêu cầu", "1088": "Lỗi dữ liệu", "2001": "License đã hết hạn", "2002": "<PERSON><PERSON><PERSON><PERSON> tìm thấy <PERSON>", "2003": "License đã bị khoá", "2004": "License không hợp lệ", "2005": "<PERSON><PERSON><PERSON> vụ không đư<PERSON><PERSON> đăng ký sử dụng", "2006": "License quá giới hạn request", "2008": "License đã hết số lượt sử dụng", "5002": "Đọc thẻ không thành công do thiếu hoặc mất dữ liệu", "5003": "Quá trình xác thực thẻ đã xảy ra lỗi. <PERSON><PERSON>t kết nối đến thẻ!", "5004": "Quá trình xác thực thẻ đã xảy ra lỗi. <PERSON><PERSON>t kết nối đến thẻ!", "5007": "Thông tin CCCD bị sao chép hoặc làm giả", "5008": "<PERSON><PERSON><PERSON><PERSON> tờ không hợ<PERSON> lệ/g<PERSON><PERSON> m<PERSON>/ bị làm giả", "5009": "Quá trình xác thực thẻ đã xảy ra lỗi. <PERSON><PERSON>t kết nối đến thẻ!", "5010": "Certificate hết hạn", "5011": "CCCD h<PERSON><PERSON> hạn", "5012": "<PERSON><PERSON><PERSON> th<PERSON>i gian yêu cầu", "5013": "Đọc thẻ không thành công do không kết nối được đến thẻ", "5014": "<PERSON><PERSON>n bản iOS đang dùng không hỗ trợtiêu chuẩn chip NFC, yêu cầu iOS 13.0 trở lên", "01": "<PERSON> chữ ký, d<PERSON> li<PERSON>u kh<PERSON>ng hợp lệ", "04": "<PERSON><PERSON> liệu gửi thiếu thông tin yêu cầu", "08": "Lỗi xử lý trên server"}, "mapping_code_message_success": {"0": "<PERSON><PERSON><PERSON><PERSON> công", "93": "<PERSON><PERSON><PERSON><PERSON> công", "1000": "<PERSON><PERSON><PERSON><PERSON> công", "00": "<PERSON><PERSON><PERSON><PERSON> công"}, "certificates_path": "/media/data/resources/MobileBackend/a0d4a74d-b475-4ef3-af7e-2d890ccce00b/private_key/Privatekey.p12", "vnpay_certificates_path": "", "pem_pass_phrase": "5SZff7krtdZkozYRJIqa", "device_type": "1", "timeout_api": 30, "jwt_key": "54f2ba79-1ba0-4196-8245-fd3b8932bce1", "jwt_secret": "09f26e402586e2faa8da4c98a35f1b20d6b033c6097befa8be3486a829587fe2f90a832bd3ff9d42710a4da095a2ce285b009f0c3730cd9b8e1af3eb84df6611", "jwt_audience": "CRM", "x_client_id": "54f2ba79-1ba0-4196-8245-fd3b8932bce1", "digest_alg": "sha256"}, "data": {"RequestUUID": "f49881d4-a9b6-4422-bf93-1c63ff7f2030", "MessageDateTime": "2025-04-01 04:37:35.000", "AddrDtls": [{"AddrLine1": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "AddrLine2": ".", "AddrLine3": ".", "AddrCategory": "Mailing", "City": "None", "Country": "VN", "HoldMailFlag": "N", "HouseNum": ".", "PrefAddr": "N", "PrefFormat": "FREE_TEXT_FORMAT", "StartDt": "2025-04-01T11:37:35.000", "State": "MIGR", "StreetName": ".", "StreetNum": ".", "PostalCode": "XXXXX", "FreeTextLabel": null}, {"AddrLine1": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "AddrLine2": ".", "AddrLine3": ".", "AddrCategory": "Home", "City": "None", "Country": "VN", "HoldMailFlag": "N", "HouseNum": ".", "PrefAddr": "N", "PrefFormat": "FREE_TEXT_FORMAT", "StartDt": "2025-04-01T11:37:35.000", "State": "MIGR", "StreetName": ".", "StreetNum": ".", "PostalCode": "XXXXX", "FreeTextLabel": null}, {"AddrLine1": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "AddrLine2": ".", "AddrLine3": ".", "AddrCategory": "Work", "City": "None", "Country": "VN", "HoldMailFlag": "N", "HouseNum": ".", "PrefAddr": "N", "PrefFormat": "FREE_TEXT_FORMAT", "StartDt": "2025-04-01T11:37:35.000", "State": "MIGR", "StreetName": ".", "StreetNum": ".", "PostalCode": "XXXXX", "FreeTextLabel": null}], "AccessOwnerGroupName": "19", "AccessOwnerSegment": "Preferential Banking", "BirthDt": "22", "BirthMonth": "9", "BirthYear": "1990", "CreatedBySystemId": "MANAGER", "DateOfBirth": "1990/09/22T00:00:00.000", "Language": "VN", "FirstName": "26", "MiddleName": "", "LastName": "<PERSON><PERSON>", "Name": "Ngoan 26", "Community": "99", "IsMinor": "N", "IsCustNRE": "N", "DefaultAddrType": "Home", "Gender": "M", "Manager": "", "RelationshipMgrID": "1001", "RelationshipCreatedByID": "1001", "NativeLanguageCode": "VN", "PrefName": "Ngoan 26", "PrimarySolId": "1001", "RelationshipOpeningDt": "", "Salutation": "MS.", "SegmentationClass": "G", "ShortName": "Ngoan 26", "StaffFlag": "N", "SubSegment": "02", "TaxDeductionTable": "ZERO", "IsEbankingEnabled": "N", "RevenueUnits": "VND", "StaffEmployeeId": "q23431rfsfasdf", "TradeFinFlag": "Y", "DSAId": "*********", "CustType": "10001", "CustTypeCode": "10001", "preferredChannelID": "999", "PhoneEmailDtls": [{"PhoneEmailType": "CELLPH", "PhoneNum": "*********", "PhoneNumCityCode": "985", "PhoneNumCountryCode": "84", "PhoneNumLocalCode": "147369", "PhoneOrEmail": "PHONE", "PrefFlag": "Y"}, {"PhoneEmailType": "HOMEPH1", "PhoneNum": "********", "PhoneNumCityCode": "352", "PhoneNumCountryCode": "84", "PhoneNumLocalCode": "97995", "PhoneOrEmail": "PHONE", "PrefFlag": "N"}], "FatcaRemarks": "", "TradeFinData_CustNative": "", "TradeFinData_InlandTradeAllowed": "", "TradeFinData_Name": "", "DemographicData_CustType": "10001", "DemographicData_EmploymentStatus": "", "DemographicData_NameOfEmployer": "", "DemographicData_MaritalStatus": "", "DemographicData_Nationality": "VN", "DemographicData_AnnualSalaryIncome": "********", "DemographicMiscData_Type": "CURRENT_EMPLOYMENT", "DemographicMiscData_EntityCreFlag": "Y", "EntityDoctData_CountryOfIssue": "VN", "EntityDoctData_DocCode": "CCUOC", "EntityDoctData_IssueDt": "2010-12-04T00:00:00.000", "EntityDoctData_TypeCode": "100", "EntityDoctData_PlaceOfIssue": "00", "EntityDoctData_Reference": "034188003226", "EntityDoctData_preferredUniqueId": "Y", "EntityDoctData_IDIssuedOrganisation": "00020", "EntityDoctData_ExpDt": "2025-11-14T00:00:00.000", "RelationshipDtls_ChildCustId": "107368591", "RelationshipDtls_ChildEntity": "CUSTOMER", "RelationshipDtls_ChildEntityType": "Retail", "RelationshipDtls_Relationship": "0310", "RelationshipDtls_RelationshipCategory": "Social", "RelationshipDtls_Type": "0310", "PsychographMiscData_StrText10": "VND", "PsychographMiscData_Type": "CURRENCY", "PsychographMiscData_DTDt1": "2099-12-31T00:00:00.000", "CoreInterfaceFreeText1": "N", "CoreInterfaceFreeText2": "ABCD", "CoreInterfaceFreeText13": "13-10-2019", "CoreInterfaceFreeText14": "15-10-2019", "TreasuryCounterparty": "N"}}