#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 05/06/2024
"""

from src.helpers.thirdparty.eib.send_request_check_face import SendRequestCheckFace

if __name__ == "__main__":

    x = {
        "merchant_id": "a0d4a74d-b475-4ef3-af7e-2d890ccce00b",
        "account_id": "689e4d35-f0dd-4bae-9762-a439bf35cc66",
        "action_time": "2025-02-27 08:58:38",
        "log_id": "08958f3f-f4e9-11ef-8aaa-3d535cb52a14",
        "log_id_start": "f053de84-f4e8-11ef-b7f3-3d535cb52a14",
        "flow_name": "teller_app",
        "request_body": {
            "data_decryption": "Amzh7vtq+mVlnyyv8O/2KY2zP2NWvrwTGsxEUge4e2sMMLDccYJQHoIM2yAdXcEMXQW4dbtu5yUEtQMPockze+92+JRZfk8q4T3DWwhgtPJOrqsf/ftjg1JwjegAHYbteQSL7BG3yQX/szuY3jNCSiLgLTqRa9xpcqfVb091Tcmzwjjj8ctnlkVtUtOdOKbXG61dBUcuozwpn43mIuQoMRceZFEI/G9XFvtxjaRm/XeMeUcQT53870wbmcTfhexAhfVeTTIdfM1PVAmpYi67f2eMzdd+dXoVm63QpoU1So73YRNTcRhxjfHFPhau7aVwOg5CvBtGGLu9zoQvqyynca9UJUxvmKqU1b2DV715ob9qA7ZCz7aDdfsJhtTyd3/1hXAOzxiQQKzlr5mSS/9gIKbMgmJ+Xx+/lV2lXjQdQwCDBrhv30c9R3uBryYVYGUA9l41ELX+4Xqm72M81JoNPPLVrj2GphcvjCO2PX3AcDHi0QtFIGHVJC8uJhug2A==",
            "sdk_request_round": "ywc=",
            "sdk_request_session": "2F1D0D76-8304-4597-883F-8B742225FACF",
            "request_timestamp": "*************",
            "sdk_request_id": "EP2023120402450807876_711FF4BB-CF68-4283-BDE7-35A471ABA0EB",
        },
        "request_type": "send_request_check_face",
    }

    SendRequestCheckFace._send(
        log_id=x.get("log_id"),
        merchant_id=x.get("merchant_id"),
        request_body=x.get("request_body"),
        account_id=x.get("account_id"),
        action_time=x.get("action_time"),
        log_id_start=x.get("log_id_start"),
        flow_name=x.get("flow_name"),
    )
