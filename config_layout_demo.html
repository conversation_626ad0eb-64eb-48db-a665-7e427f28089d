<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Layout <PERSON><PERSON><PERSON></title>
    <style>
        :root {
            --color-primary: #4361ee;
            --color-primary-dark: #3f37c9;
            --color-success: #4cc9f0;
            --color-error: #f72585;
            --color-background: #f8f9fa;
            --color-card: #ffffff;
            --color-text: #333333;
            --color-text-light: #666666;
            --color-border: #e0e0e0;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --border-radius: 8px;
            --transition-duration: 0.3s;
            --transition-timing: ease-in-out;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
            background-color: var(--color-background);
            color: var(--color-text);
            line-height: 1.6;
            padding: var(--spacing-lg);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            color: var(--color-primary);
            border-bottom: 2px solid var(--color-border);
            padding-bottom: var(--spacing-sm);
            margin-bottom: var(--spacing-xl);
            font-size: 2rem;
            font-weight: 700;
        }

        /* Configuration Section */
        .config-container {
            margin-bottom: var(--spacing-xl);
        }

        .config-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        @media (min-width: 768px) {
            .config-grid {
                grid-template-columns: 1fr 1fr;
            }
        }

        /* API Config Section */
        .api-config-container {
            background-color: rgba(76, 201, 240, 0.1);
            border-radius: var(--border-radius);
            border: 1px solid rgba(76, 201, 240, 0.3);
        }

        /* System Config Section */
        .system-config-container {
            background-color: rgba(67, 97, 238, 0.1);
            border-radius: var(--border-radius);
            border: 1px solid rgba(67, 97, 238, 0.3);
        }

        .api-config-header,
        .system-config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            cursor: pointer;
        }

        .api-config-header h3,
        .system-config-header h3 {
            margin: 0;
            font-size: 1.1rem;
            color: var(--color-primary);
        }

        .toggle-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 24px;
            height: 24px;
            position: relative;
        }

        .toggle-icon {
            display: block;
            position: relative;
            width: 100%;
            height: 2px;
            background-color: var(--color-primary);
            transition: transform var(--transition-duration) var(--transition-timing);
        }

        .toggle-icon:before,
        .toggle-icon:after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            background-color: var(--color-primary);
            transition: transform var(--transition-duration) var(--transition-timing);
        }

        .toggle-icon:before {
            transform: translateY(-6px);
        }

        .toggle-icon:after {
            transform: translateY(6px);
        }

        .toggle-btn[aria-expanded="true"] .toggle-icon {
            transform: rotate(45deg);
        }

        .toggle-btn[aria-expanded="true"] .toggle-icon:before {
            transform: translateY(0) rotate(90deg);
        }

        .toggle-btn[aria-expanded="true"] .toggle-icon:after {
            transform: translateY(0) rotate(90deg);
        }

        .api-config-content,
        .system-config-content {
            padding: 0 var(--spacing-md) var(--spacing-md);
            max-height: 300px;
            overflow: hidden;
            transition: max-height var(--transition-duration) var(--transition-timing);
        }

        .api-config-content.collapsed,
        .system-config-content.collapsed {
            max-height: 0;
        }

        .form-group {
            margin-bottom: var(--spacing-md);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 500;
            color: var(--color-text);
        }

        .input-with-icon {
            position: relative;
            display: block;
        }

        .input-with-icon i {
            position: absolute;
            left: 12px;
            top: 12px;
            color: var(--color-text-light);
            width: 18px;
            height: 18px;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
        }

        .input-with-icon i svg {
            width: 100%;
            height: 100%;
            fill: none;
            stroke: currentColor;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
            display: block;
        }

        .input-with-icon input {
            width: 100%;
            padding: 12px 12px 12px 42px;
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            font-size: 1rem;
            line-height: 1.5;
            transition: border-color var(--transition-duration) var(--transition-timing),
                box-shadow var(--transition-duration) var(--transition-timing);
            position: relative;
            z-index: 1;
            background-color: #fff;
        }

        .input-with-icon input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        .input-with-icon:focus-within i {
            color: var(--color-primary);
        }

        /* Toggle Switches */
        .toggle-container {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
            margin-right: var(--spacing-sm);
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: var(--transition-duration);
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: var(--transition-duration);
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: var(--color-primary);
        }

        input:checked+.slider:before {
            transform: translateX(26px);
        }

        .toggle-container label {
            font-weight: normal;
            margin-bottom: 0;
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color var(--transition-duration) var(--transition-timing),
                transform var(--transition-duration) var(--transition-timing);
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn-primary {
            background-color: var(--color-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--color-primary-dark);
        }

        small {
            color: var(--color-text-light);
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Demo Layout Cấu Hình</h1>
        
        <!-- Configuration Section -->
        <div class="config-container">
            <div class="config-grid">
                <!-- API Configuration -->
                <div class="api-config-container">
                    <div class="api-config-header">
                        <h3>Cấu hình API Endpoint</h3>
                        <button class="toggle-btn" id="toggleApiConfig" aria-expanded="true">
                            <span class="toggle-icon"></span>
                        </button>
                    </div>
                    <div class="api-config-content" id="apiConfigContent">
                        <div class="form-group">
                            <label for="apiEndpoint">Domain API</label>
                            <div class="input-with-icon">
                                <i class="icon-server">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                        <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                                        <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                                        <line x1="6" y1="6" x2="6.01" y2="6"></line>
                                        <line x1="6" y1="18" x2="6.01" y2="18"></line>
                                    </svg>
                                </i>
                                <input type="text" id="apiEndpoint" placeholder="https://api.my-service.com">
                            </div>
                            <small>Nhập địa chỉ domain của API</small>
                        </div>
                        <button type="button" class="btn btn-primary">Lưu Cấu hình</button>
                    </div>
                </div>

                <!-- System Configuration -->
                <div class="system-config-container">
                    <div class="system-config-header">
                        <h3>Cấu hình hệ thống</h3>
                        <button class="toggle-btn" id="toggleSystemConfig" aria-expanded="true">
                            <span class="toggle-icon"></span>
                        </button>
                    </div>
                    <div class="system-config-content" id="systemConfigContent">
                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_logging">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_logging">Bật ghi log hệ thống</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_debug_mode">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_debug_mode">Chế độ debug</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_email_notification">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_email_notification">Thông báo qua email</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_sms_notification">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_sms_notification">Thông báo qua SMS</label>
                        </div>

                        <button type="button" class="btn btn-primary">Lưu Cấu hình</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background-color: white; border-radius: 8px; border: 1px solid #e0e0e0;">
            <h3>✨ Tính năng mới:</h3>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li><strong>Layout 2 cột:</strong> API và System config nằm cạnh nhau trên desktop</li>
                <li><strong>Responsive:</strong> Tự động chuyển thành 1 cột trên mobile</li>
                <li><strong>Toggle riêng biệt:</strong> Mỗi section có thể đóng/mở độc lập</li>
                <li><strong>Màu sắc phân biệt:</strong> API (xanh lam), System (xanh tím)</li>
                <li><strong>LocalStorage:</strong> Lưu cấu hình tự động</li>
            </ul>
        </div>
    </div>

    <script>
        // Toggle functionality
        document.getElementById('toggleApiConfig').addEventListener('click', function() {
            const expanded = this.getAttribute('aria-expanded') === 'true';
            this.setAttribute('aria-expanded', !expanded);
            const content = document.getElementById('apiConfigContent');
            if (expanded) {
                content.classList.add('collapsed');
            } else {
                content.classList.remove('collapsed');
            }
        });

        document.getElementById('toggleSystemConfig').addEventListener('click', function() {
            const expanded = this.getAttribute('aria-expanded') === 'true';
            this.setAttribute('aria-expanded', !expanded);
            const content = document.getElementById('systemConfigContent');
            if (expanded) {
                content.classList.add('collapsed');
            } else {
                content.classList.remove('collapsed');
            }
        });
    </script>
</body>
</html>
