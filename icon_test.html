<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Display Test</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .icon-test {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        
        .icon-test i {
            width: 18px;
            height: 18px;
            margin-right: 10px;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .icon-test i svg {
            width: 100%;
            height: 100%;
            fill: none;
            stroke: currentColor;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
            shape-rendering: geometricPrecision;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .icon-test:hover i {
            color: #4361ee;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Icon Display Test</h1>
        <p>This page tests all the icons used in the customer form. Hover over each icon to see the color change effect.</p>
        
        <div class="icon-test">
            <i class="icon-server">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                    <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                    <line x1="6" y1="6" x2="6.01" y2="6"></line>
                    <line x1="6" y1="18" x2="6.01" y2="18"></line>
                </svg>
            </i>
            <span>Server Icon (API Endpoint)</span>
        </div>
        
        <div class="icon-test">
            <i class="icon-user">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
            </i>
            <span>User Icon (Name, Gender, Parents)</span>
        </div>
        
        <div class="icon-test">
            <i class="icon-calendar">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
            </i>
            <span>Calendar Icon (Date of Birth, Date of Issue)</span>
        </div>
        
        <div class="icon-test">
            <i class="icon-location">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                </svg>
            </i>
            <span>Location Icon (Place of Birth, Address)</span>
        </div>
        
        <div class="icon-test">
            <i class="icon-id-card">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <rect x="2" y="4" width="20" height="16" rx="2"></rect>
                    <path d="M8 12h8"></path>
                    <path d="M8 16h8"></path>
                    <circle cx="8" cy="8" r="2"></circle>
                </svg>
            </i>
            <span>ID Card Icon (CCCD, Old ID Card)</span>
        </div>
        
        <div class="icon-test">
            <i class="icon-document">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
            </i>
            <span>Document Icon (CIF)</span>
        </div>
        
        <div class="icon-test">
            <i class="icon-users">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
            </i>
            <span>Users Icon (Ethnicity)</span>
        </div>
        
        <div class="debug-info">
            <strong>Debug Information:</strong><br>
            <span id="debug-output">Click "Test Icons" button to run diagnostics</span>
        </div>
        
        <button onclick="testIcons()" style="margin-top: 10px; padding: 10px 20px; background: #4361ee; color: white; border: none; border-radius: 4px; cursor: pointer;">Test Icons</button>
    </div>
    
    <script>
        function testIcons() {
            const icons = document.querySelectorAll('.icon-test i');
            const debugOutput = document.getElementById('debug-output');
            let output = '';
            
            icons.forEach((icon, index) => {
                const svg = icon.querySelector('svg');
                const computedStyle = window.getComputedStyle(icon);
                const rect = icon.getBoundingClientRect();
                
                output += `Icon ${index + 1} (${icon.className}):\n`;
                output += `  Visible: ${computedStyle.display !== 'none'}\n`;
                output += `  Dimensions: ${rect.width}x${rect.height}px\n`;
                output += `  Color: ${computedStyle.color}\n`;
                output += `  Has SVG: ${!!svg}\n`;
                if (svg) {
                    output += `  SVG Elements: ${svg.querySelectorAll('*').length}\n`;
                }
                output += '\n';
            });
            
            debugOutput.innerHTML = output.replace(/\n/g, '<br>');
        }
    </script>
</body>
</html>
