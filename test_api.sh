#!/bin/bash

# ==== Thời gian hiện tại UTC định dạng yyyy-MM-ddTHH:mm:ss.mmm ====
datetime_part=$(date -u +"%Y-%m-%dT%H:%M:%S")
nanoseconds=$(date -u +"%N")
milliseconds=$(printf "%03d" $((10#${nanoseconds:0:3})))
MESSAGE_DATETIME="${datetime_part}.${milliseconds}"

curl --location --request POST 'http://crm-dev.eximbank.com.vn:2006/api/v1/InqExchangeRate_Esale' \
--header 'x-ClientId: 54f2ba79-1ba0-4196-8245-fd3b8932bce1' \
--header 'Authorization: Basic Q1JNOmNybUAxMzU3OQ==' \
--header 'Content-Type: application/json' \
--data-raw '{
    "RequestUUID": "123456",
    "ChannelId": "COR",
    "MessageDateTime": "2025-06-23T17:00:00.000",
    "CcyCd": "",
    "SolId": "1000"
}'

curl --location --request POST 'http://crm-dev.eximbank.com.vn:2006/api/v1/InqExchangeRate_Esale' \
--header 'Authorization: Basic Q1JNOmNybUAxMzU3OQ==' \
--header 'Content-Type: application/json' \
--data-raw '{
    "RequestUUID": "123456",
    "ChannelId": "COR",
    "MessageDateTime": "2025-06-23T17:00:00.000",
    "CcyCd": "",
    "SolId": "1000"
}'

curl --location --request POST 'http://crm-dev.eximbank.com.vn:2006/api/v1/InqExchangeRate_Esale' \
--header 'Authorization: Basic Q1JNOmNybUAxMzU3OQ==' \
--header 'Content-Type: application/json' \
--data-raw '{
    "RequestUUID": "123456",
    "ChannelId": "COR",
    "MessageDateTime": "$MESSAGE_DATETIME",
    "CcyCd": "",
    "SolId": "1000",
    "ClientId": "CRM_UAT"
}'