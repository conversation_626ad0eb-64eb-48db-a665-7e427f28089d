#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/05/2024
"""

from flask import request
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common.handle_headers import get_merchant_header
from src.common.mobio_json_encode import MobioJSONEncoderV2
from src.models.mongo.attachments_model import AttachmentsModel


class GetInformationProfileController(BaseController):

    def get_avatar_by_log_id(self):
        merchant_id = get_merchant_header()

        log_id = request.args.get("log_id")

        if not log_id:
            raise CustomError("Log ID required")

        attachment = AttachmentsModel().get_attachments_by_avatar(merchant_id, log_id)
        if attachment:
            attachment = MobioJSONEncoderV2().json_loads(attachment)
        else:
            attachment = {}

        return {"data": attachment}

    def get_evident_by_log_id(self):
        merchant_id = get_merchant_header()

        log_id = request.args.get("log_id")

        if not log_id:
            raise CustomError("Log ID required")

        attachment = AttachmentsModel().get_attachments_evident(merchant_id, log_id)
        if attachment:
            attachment = MobioJSONEncoderV2().json_loads(attachment)
        else:
            attachment = {}

        return {"data": attachment}
