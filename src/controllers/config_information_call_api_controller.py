#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 11/06/2024
"""

from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.admin import MobioAdminSDK
from mobio.sdks.base.controllers import BaseController

from src.common.handle_headers import get_merchant_header
from src.common.init_lib import lru_redis_cache
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


class ConfigInformationCallAPIController(BaseController):

    def _validate_upsert_config(self, data_validate):
        rule_validate = {
            "client_id": [Required, InstanceOf(str), Length(1), In(["eib"])],
            "type_config": [Required, InstanceOf(str), Length(1), In(["basic_username_password"])],
            "configuration": [Required, InstanceOf(dict), Length(1)],
        }

        self.abort_if_validate_error(rule_validate, data_validate)

        type_config = data_validate.get("type_config")
        if type_config == "basic_username_password":
            configuration = data_validate.get("configuration")
            rule_validate_configuration = {
                "username": [Required, InstanceOf(str), Length(1)],
                "password": [Required, InstanceOf(str), Length(1)],
            }
            self.abort_if_validate_error(rule_validate_configuration, configuration)

    def upsert_config(self):
        merchant_id = get_merchant_header()
        body = request.get_json()

        self._validate_upsert_config(body)

        MobioLogging().info("Config information")
        type_config = body.get("type_config")

        configuration = body.get("configuration")
        if type_config == "basic_username_password":
            username = configuration.get("username")
            password = configuration.get("password")

            MobioLogging().info("Handler configuration basic_username_password")

            username_encrypt_object = MobioAdminSDK().encrypt_values(
                merchant_id=merchant_id,
                module="MOBILE_BACKEND",
                field="username",
                values=username,
            )
            username_encrypt = username_encrypt_object.get("data", {}).get(username)

            password_encrypt_object = MobioAdminSDK().encrypt_values(
                merchant_id=merchant_id, module="MOBILE_BACKEND", field="password", values=password
            )

            password_encrypt = password_encrypt_object.get("data", {}).get(password)

            data_configs = ConfigInfoApiModel().get_list_config_info_api(merchant_id)
            for data_config in data_configs:
                config_id = data_config.get("_id")
                filter_option = {
                    "status": data_config.get("status"),
                    "merchant_id": data_config.get("merchant_id"),
                    "type": data_config.get("type"),
                }

                config = data_config.get("config")
                config["auth_name"] = username_encrypt
                config["auth_pass"] = password_encrypt

                config_status = ConfigInfoApiModel().update_by_set(filter_option, {"config": config}, upsert=True)

                MobioLogging().info("Config id {} update status: {}".format(config_id, config_status.matched_count))
                # Delete cache
                lru_redis_cache.delete_cache_by_pattern("*get_config_info_api*")
        return
