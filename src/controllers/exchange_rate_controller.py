from mobio.sdks.base.controllers import <PERSON>Controller
from mobio.libs.logging import Mo<PERSON>Logging
from flask import request
from mobio.libs.validator import In, InstanceOf, Length, Required
from src.common import StatusCode, ThirdPartyType
from src.helpers.thirdparty.eib.exchange_rate_helper import ExchangeRateHelper
from src.common.handle_headers import get_merchant_header
from src.models.mongo.config_info_api_model import ConfigInfoApiModel

class ExchangeRateController(BaseController):
    
    LIST_ERROR_CODE = ["0", "1", "-1", "99"]
    
    def _validate_get_exchange_rate(self, data_validate):
        rule_validate = {
            "channel_id": [Required, InstanceOf(str), Length(1)],
            "currency": [Required, InstanceOf(str)],
            "sol_id": [Required, InstanceOf(str), Length(1)],
            "exchange_date": [Required, InstanceOf(str), Length(1)]
        }
        self.validate_optional_err(rule_validate, data_validate)
                
    def get_list_exchange_rate(self):
        merchant_id = get_merchant_header()
        body = request.get_json()
        self._validate_get_exchange_rate(body)
        config = ConfigInfoApiModel().get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_EXCHANGE_RATE)
        exchange_rate, status_code = ExchangeRateHelper()._request_get_quotes_count_list(config, body)
        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            return {"data": {
                "status": "FAILURE",
                "errorcode": "99",
                "errordesc": "Loi he thong",
                "lst_exchange_rate": []
            }}
        if exchange_rate:
            details_exchange_rate = exchange_rate['EXCHANGERATE'][0] if exchange_rate.get("EXCHANGERATE") else []
            result = {}
            if details_exchange_rate:
                result = {
                    "quotecnt": details_exchange_rate.get("QUOTECNT"),
                    "ccycd": details_exchange_rate.get("CCYCD"),
                    "exchangedate": details_exchange_rate.get("EXCHANGEDATE"),
                    "buytransfer": details_exchange_rate.get("BUYTRANSFER"),
                    "selltransfer": details_exchange_rate.get("SELLTRANSFER"),
                    "buycash": details_exchange_rate.get("BUYCASH"),
                    "sellcash": details_exchange_rate.get("SELLCASH"),
                    "buytransfer_intbk": details_exchange_rate.get("BUYTRANSFER_INTBK"),
                    "selltransfer_intbk": details_exchange_rate.get("SELLTRANSFER_INTBK"),
                    "buycash_intbk": details_exchange_rate.get("BUYCASH_INTBK"),
                    "sellcash_intbk": details_exchange_rate.get("SELLCASH_INTBK")
                }
            status = exchange_rate.get("STATUS")
            errorcode = exchange_rate.get("ErrorCode")
            errordesc = exchange_rate.get("ErrorDesc")
            if errorcode not in self.LIST_ERROR_CODE:
                status = "FAILURE"
                errorcode = "99"
                errordesc = "Loi he thong"
            return {"data": {
                "status": status,
                "errorcode": errorcode,
                "errordesc": errordesc,
                "lst_exchange_rate": [result] if result else []
            }}
        return None
    
if __name__ == '__main__':
    print(ExchangeRateController().get_list_exchange_rate())

        
        