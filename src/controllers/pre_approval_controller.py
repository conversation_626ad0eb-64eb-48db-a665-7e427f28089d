#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 01/07/2025
"""


import uuid

from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    StatusCode,
)
from src.common.handle_headers import get_merchant_header
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


class PreApprovalController(BaseController):

    def _validate_check_customer_exist(self, data_validate):
        rule_validate = {
            "id_card": [Required, InstanceOf(str), Length(1)],
            "name": [Required, InstanceOf(str), Length(1)],
            "date_of_birth": [],
            "gender": [],
            "date_of_issuance": [],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def _build_config_send_request_check_customer_exist(self, merchant_id, body_request):

        body_send_request = {
            "idCard": body_request.get("id_card"),
            "name": body_request.get("name"),
            "dateOfBirth": "",
            "gender": "",
            "dateOfIssuance": "",
        }

        config = ConfigInfoApiModel().get_config_info_api_check_customer_exist(merchant_id)
        if not config:
            raise CustomError("Not config send check customer exist")

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: body_send_request}
            }
        }

        return data, config

    def check_customer_exist(self):
        merchant_id = get_merchant_header()

        body = request.get_json()

        self._validate_check_customer_exist(body)
        data_request, config_request = self._build_config_send_request_check_customer_exist(merchant_id, body)

        log_id = str(uuid.uuid4())
        response_customer_exist, status_code, reasons = ThirdPartyEIB.check_customer_exists(
            merchant_id, log_id, config_request, data_request, log_id_start=log_id
        )
        MobioLogging().info(
            "_send_request_check_customer_exist :: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            raise CustomError(reasons)

        data_response = response_customer_exist.get("data", {})
        userExists = data_response.get("userExists")
        return {"data": {"is_exist": True if userExists and userExists.lower() == "true" else False}}
