#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/04/2024
"""

import datetime
import uuid

import pytz
from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import (
    Constant,
    ConstantMessageSendRequestToThirdParty,
    ConstantNameFlow,
    ConstantNumberMaxRequestSmsConsentInRangeTime,
    ConstantStatusRequestSmsConsent,
    KeyConfigNotifySDK,
    ThirdPartyType,
)
from src.common.algorithm_hmac_sha256 import create_signature_by_plan_text
from src.common.handle_headers import get_merchant_header, get_param_value_temp
from src.common.i_base_common import IBaseConfig
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_card_model import LogRequestCardModel
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)
from src.models.mongo.log_request_sms_consent_model import (
    ConstantLogRequestSmsConsentField,
    LogRequestSmsConsentModel,
)


class ConstantBodyRequestConfirmConsent:
    PROFILE_NAME = "profile_name"
    PHONE_NUMBER = "phone_number"


class ConstantBodyReceiver:
    CLIENT_ID = "client_id"
    TYPE_USES = "type_uses"
    PHONE_NUMBER = "phone_number"
    REFERENCE_ID = "reference_id"
    ACTION_TIME = "action_time"
    MESSAGE_CONTENT = "message_content"
    SIGNATURE = "signature"


class ConstantReceiverTypeUses(IBaseConfig):
    TRACKING_CONSENT = "tracking_consent"


class ConstantReceiverClientId(IBaseConfig):
    EIB_SMS = "eib_sms"


class SmsController(BaseController):

    @classmethod
    def _build_log_header(cls, log_id):
        return "SmsController :: Log_id :: {}".format(log_id)

    def _validate_receiver(self, data_validate):
        rule_validate_body = {
            ConstantBodyReceiver.CLIENT_ID: [
                Required,
                InstanceOf(str),
                Length(1),
                In(ConstantReceiverClientId.get_all_attribute()),
            ],
            ConstantBodyReceiver.TYPE_USES: [
                Required,
                InstanceOf(str),
                Length(1),
                In(ConstantReceiverTypeUses.get_all_attribute()),
            ],
            ConstantBodyReceiver.PHONE_NUMBER: [Required, InstanceOf(str)],
            ConstantBodyReceiver.REFERENCE_ID: [Required, InstanceOf(str), Length(1)],
            ConstantBodyReceiver.ACTION_TIME: [Required, InstanceOf(str), Length(1)],
            ConstantBodyReceiver.MESSAGE_CONTENT: [Required, InstanceOf(str), Length(1)],
            ConstantBodyReceiver.SIGNATURE: [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rule_validate_body, data_validate)

    def receiver(self):
        merchant_id = get_merchant_header()
        body = request.get_json()
        self._validate_receiver(body)
        log_id = (
            LogRequestSendThirdPartyModel()
            .save_log_response_eib(ThirdPartyType.RESPONSE_SMS_REPLY_CONFIRM_CONSENT, body)
            .inserted_id
        )
        message_content_reply = body.get(ConstantBodyReceiver.MESSAGE_CONTENT)
        phone_number = body.get(ConstantBodyReceiver.PHONE_NUMBER)
        type_uses = body.get(ConstantBodyReceiver.TYPE_USES)
        action_time_receiver = datetime.datetime.now(datetime.UTC)
        try:
            plan_text = "".join(
                [
                    body[ConstantBodyReceiver.CLIENT_ID],
                    body[ConstantBodyReceiver.TYPE_USES],
                    body[ConstantBodyReceiver.PHONE_NUMBER],
                    body[ConstantBodyReceiver.REFERENCE_ID],
                    body[ConstantBodyReceiver.ACTION_TIME],
                    body[ConstantBodyReceiver.MESSAGE_CONTENT],
                ]
            )
            config_info_api_receiver = ConfigInfoApiModel().get_config_info_api_sms_reply_confirm_consent(merchant_id)
            value_condition_tracking_consent = config_info_api_receiver.get("sms_format_tracking_consent") or "Y"
            server_signature = create_signature_by_plan_text(
                config_info_api_receiver.get("secret_key"), plan_text=plan_text
            )
            client_signature = body.get("signature")
            MobioLogging().info(
                "SmsController :: _validate_receiver :: server_signature: %s, client_signature: %s"
                % (server_signature, client_signature)
            )
            if server_signature != client_signature:
                raise CustomError("signature fail")

            phone_number = body[ConstantBodyReceiver.PHONE_NUMBER]
            phone_number = self.convert_phone_number_request_send_sms(phone_number)
            body[ConstantBodyReceiver.PHONE_NUMBER] = phone_number

            if type_uses == ConstantReceiverTypeUses.TRACKING_CONSENT:
                is_confirm_consent = self._check_message_content_reply_consent(
                    message_content_reply, value_condition_tracking_consent
                )
                MobioLogging().info(
                    "SmsController :: is_confirm_consent: %s, message_content_reply: %s"
                    % (is_confirm_consent, client_signature)
                )
                info_request_sms_consent = LogRequestSmsConsentModel().get_info_request_by_phone_number(
                    merchant_id, phone_number
                )
                if info_request_sms_consent:
                    log_request_id = info_request_sms_consent.get("_id")
                    log_id_start = info_request_sms_consent.get(ConstantLogRequestSmsConsentField.LOG_ID)
                    MobioLogging().info("%s :: receiver body :: %s" % (self._build_log_header(log_id_start), body))
                    MobioLogging().info(
                        "%s :: info_request_sms_consent :: %s"
                        % (self._build_log_header(log_id_start), info_request_sms_consent)
                    )
                    account_id = info_request_sms_consent.get(ConstantLogRequestSmsConsentField.ACCOUNT_ID)
                    account_username = info_request_sms_consent.get(ConstantLogRequestSmsConsentField.ACCOUNT_USERNAME)
                    action_time_request = info_request_sms_consent.get(ConstantLogRequestSmsConsentField.ACTION_TIME)
                    profile_name = info_request_sms_consent.get(ConstantLogRequestSmsConsentField.PROFILE_NAME)
                    message_content_request = info_request_sms_consent.get(
                        ConstantLogRequestSmsConsentField.MESSAGE_CONTENT_REQUEST
                    )

                    action_time_request_format = action_time_request.strftime("%Y-%m-%d %H:%M:%S")
                    action_time_receiver_format = action_time_receiver.strftime("%Y-%m-%d %H:%M:%S")
                    time_delta = action_time_receiver.replace(tzinfo=pytz.UTC) - action_time_request.replace(
                        tzinfo=pytz.UTC
                    )
                    time_delta_seconds = time_delta.seconds
                    MobioLogging().info(
                        "%s :: time_delta seconds :: %s" % (self._build_log_header(log_id_start), time_delta_seconds)
                    )

                    if time_delta_seconds > Constant.MAX_TIME_REPLY_SMS_CONFIRM_CONSENT_SEC:
                        status = ConstantStatusRequestSmsConsent.FAIL
                        reason = "Response time exceeds allowed time. Time request :: {}, time receiver :: {}".format(
                            action_time_request.strftime("%Y-%m-%d %H:%M:%S"),
                            action_time_receiver.strftime("%Y-%m-%d %H:%M:%S"),
                        )
                    else:
                        status = ConstantStatusRequestSmsConsent.DONE
                        reason = None

                    result_update = LogRequestSmsConsentModel().update_status_request_sms_consent(
                        log_request_id, status, reason, body
                    )
                    MobioLogging().info(
                        "%s :: result_update :: %s" % (self._build_log_header(log_id_start), result_update)
                    )

                    information_reply = {
                        "log_id": log_id_start,
                        "profile_name": profile_name,
                        "phone_number": phone_number,
                        # "merchant_id": merchant_id,
                        "account_id": account_id,
                        "is_confirm_consent": is_confirm_consent,
                        "reason": (
                            "Sai cú pháp xác nhận. Vui lòng hướng dẫn khách hàng phản hồi đúng cú pháp."
                            if not is_confirm_consent
                            else ""
                        ),
                    }

                    if is_confirm_consent:
                        data_send_build_file_evident = {
                            "log_id": log_id_start,
                            "message_type": "build_file_evident_profile",
                            "account_id": account_id,
                            "data_build_file_evident": {
                                "account_id": account_id,
                                "account_username": account_username,
                                "phone_number": phone_number,
                                "profile_name": profile_name,
                                "sms_brand_name_send": config_info_api_receiver.get("sms_brand_name_send"),
                                "sms_brand_name_receiver": config_info_api_receiver.get("sms_brand_name_receiver"),
                                "time_sent_sms": action_time_request_format,
                                "time_receiver_sms": action_time_receiver_format,
                                "message_content_reply": config_info_api_receiver.get("information_show_in_evident"),
                                "message_content_request": message_content_request,
                            },
                        }

                        MobioLogging().info("receiver :: push message to gen build evident")
                        Producer().push_message_to_build_information_related_profile(
                            merchant_id, account_id, action_time_receiver_format, data_send_build_file_evident
                        )
                    Producer().push_message_push_socket_notify_mobile(
                        merchant_id=merchant_id,
                        account_id=account_id,
                        message_type=KeyConfigNotifySDK.MOBILEBACKEND_PROFILE_REPLY_SMS_CONFIRM_CONSENT,
                        data_send=information_reply,
                    )
                    return
        except Exception as ex:
            MobioLogging().error("%s :: Error :: %s" % (self._build_log_header(log_id), str(ex)))
            LogRequestCardModel().update_by_set({"_id": log_id}, {"status": "fail", "reason": str(ex)})
            raise CustomError("Có lỗi xảy ra khi xử lý yêu cầu xác nhận tracking consent.")

        raise CustomError("Số điện thoại {} không có request consent.".format(phone_number))

    def _check_message_content_reply_consent(self, message_content, value_condition):
        if message_content.lower().strip() == value_condition.lower():
            return True
        return False

    def _validate_request_send_sms_confirm_consent(self, data_validate):
        rule_validate = {
            ConstantBodyRequestConfirmConsent.PROFILE_NAME: [Required, InstanceOf(str), Length(1, 27)],
            ConstantBodyRequestConfirmConsent.PHONE_NUMBER: [Required, InstanceOf(str), Length(10)],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def convert_phone_number_request_send_sms(self, phone_number):
        if phone_number.startswith("0"):
            return "84" + phone_number[1:]
        return phone_number

    def request_send_sms_confirm_consent(self):
        log_id = str(uuid.uuid1())
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        account_username = get_param_value_temp("username")
        MobioLogging().info(
            "%s :: request_send_sms_confirm_consent :: merchant_id: %s, account_id: %s"
            % (self._build_log_header(log_id), merchant_id, account_id)
        )
        flow_name = request.args.get("flow_name", ConstantNameFlow.CBBH_ADD_PROFILE)
        if flow_name not in ConstantNameFlow.get_all_attribute():
            raise CustomError("Flow name {} not supported".format(flow_name))
        session_id = request.args.get("session")
        body = request.get_json()
        self._validate_request_send_sms_confirm_consent(body)

        phone_number = self.convert_phone_number_request_send_sms(
            body.get(ConstantBodyRequestConfirmConsent.PHONE_NUMBER)
        )
        body[ConstantBodyRequestConfirmConsent.PHONE_NUMBER] = phone_number

        action_time = datetime.datetime.now(datetime.UTC)

        before_time_30_minutes = action_time - datetime.timedelta(minutes=30)

        number_request_send_confirm_consent_range_time = (
            LogRequestSmsConsentModel().get_number_request_to_phone_in_range_time(
                merchant_id,
                phone_number,
                before_time_30_minutes,
                action_time,
            )
        )
        MobioLogging().info(
            "%s :: number_request_send_confirm_consent_range_time :: %s"
            % (self._build_log_header(log_id), number_request_send_confirm_consent_range_time)
        )
        if (
            number_request_send_confirm_consent_range_time
            >= ConstantNumberMaxRequestSmsConsentInRangeTime.MAX_REQUEST_SMS_CONSENT_IN_RANGE_TIME
        ):
            raise CustomError("Chỉ được yêu cầu gửi SMS cho khách hàng này tối đa 5 lần trong 30 phút.")

        str_action_time = action_time.strftime("%Y-%m-%d %H:%M:%S")
        data_send = {
            ConstantMessageSendRequestToThirdParty.LOG_ID: log_id,
            ConstantMessageSendRequestToThirdParty.LOG_ID_START: session_id,
            ConstantMessageSendRequestToThirdParty.FLOW_NAME: flow_name,
            ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body,
            ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_SMS_CONFIRM_CONSENT,
        }
        MobioLogging().info("%s :: data_send :: %s" % (self._build_log_header(log_id), data_send))
        Producer().push_message_to_request_third_party(
            merchant_id, account_id=account_id, action_time=str_action_time, data_send=data_send
        )

        # save log action request

        log_request_id = LogRequestSmsConsentModel().insert_request_sms_consent(
            merchant_id,
            log_id,
            account_id,
            account_username,
            action_time,
            phone_number,
            body.get(ConstantBodyRequestConfirmConsent.PROFILE_NAME),
        )

        MobioLogging().info("%s :: log_request_id :: %s" % (self._build_log_header(log_id), str(log_request_id)))
        return {"data": {"log_id": log_id}}
