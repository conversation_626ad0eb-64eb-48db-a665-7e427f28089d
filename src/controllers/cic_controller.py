#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 22/11/2024
"""
import datetime
import uuid

from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from configs import MobileBackendApplicationConfig, RedisConfig
from src.apis import MobioNotifySDK
from src.common import ConstantCicAreaCode, ConstantCicReportType, CustomerType
from src.common.handle_headers import get_merchant_header, get_param_value_temp
from src.common.mobio_json_encode import MobioJSONEncoderV2
from src.common.utils import utf8_to_ascii
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.helpers.thirdparty.cic.call import ThirdPartyCICHelper
from src.helpers.thirdparty.ecm.call import ThirdPartyECMHelper
from src.models.mongo.cic_config_model import CicConfigModel
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_cic_request_search_new_model import (
    LogCicRequestSearchNewModel,
)
from src.models.mongo.report_cic_model import ReportCicModel
from src.models.mongo.request_export_report_cic_model import RequestExportReportCicModel
from src.models.mongo.save_view_account_model import SaveViewAccountModel

MobioMediaSDK().config(admin_host=MobileBackendApplicationConfig.ADMIN_HOST, cache_prefix=RedisConfig.CACHE_PREFIX)


class CicController(BaseController):
    def get_report_status(self):
        merchant_id = get_merchant_header()
        result = CicConfigModel().get_config_status(merchant_id)
        for item in result:
            item["title"] = item["title"].get(self.language, item["title"].get("vi"))
            item["name"] = item["title"]
            item["code"] = item.get("key")
            item["background_color"] = item.get("background_color")

        return {"data": result}

    def get_mapping_status_record_cic(self, merchant_id):
        result_config_code = CicConfigModel().get_config_status(merchant_id)
        result = {}
        for item in result_config_code:
            result[item["key"]] = item["status"]
        return result

    def get_mapping_type_status_record_cic(self, merchant_id):
        result_config_code = CicConfigModel().get_config_status(merchant_id)
        result = {}
        for item in result_config_code:
            result[item["key"]] = item.get("type")
        return result

    @classmethod
    def _convert_customer_type(cls, customer_type):
        return {CustomerType.KHCN: CustomerType.CN, CustomerType.KHDN: CustomerType.DN}.get(
            customer_type, customer_type
        )

    def _validate_action_search_new(self, data_validate):
        rule_validate = {
            "customer_type": [],
            "force_new_flag": [Required, InstanceOf(bool), In([True, False])],
            "cic_code": [],
            "list_product_code": [],
        }
        self.validate_optional_err(rule_validate, data_validate)

    def build_data_insert_report_search_cic(self, merchant_id, action, report_result, account_data, product_data):

        action_time = datetime.datetime.now(datetime.UTC)
        action_time_day = datetime.datetime.strftime((action_time + datetime.timedelta(hours=7)), "%Y-%m-%d")

        data_insert = {
            "merchant_id": merchant_id,
            "action": action,
            "status": report_result,
            "action_time": datetime.datetime.now(datetime.UTC),
            "action_time_day": action_time_day,
            **product_data,
            **account_data,
        }

        return data_insert

    def action_search_new(self):
        log_id = str(uuid.uuid4())
        customer_type = request.args.get("customer_type")
        if customer_type not in CustomerType.ALL:
            raise CustomError("customer_type {} not support!".format(customer_type))
        body = request.get_json()
        data_validate = {"customer_type": customer_type, **body}
        self._validate_action_search_new(data_validate)

        merchant_id = get_merchant_header()
        profile_identify = body.get("profile_identify", {})
        customer_id = ""
        if profile_identify:
            customer_id = profile_identify.get("customer_id", "")
        list_product_code = body.get("list_product_code", [])
        force_new_flag = body.get("force_new_flag", False)
        profile_name = body.get("profile_name")
        company_tax_code = body.get("company_tax_code")
        company_name = body.get("company_name")
        business_registration = body.get("business_registration")
        cic_code = body.get("cic_code")

        account_id = get_param_value_temp("id")
        username = get_param_value_temp("username")

        MobioLogging().info("Start convert payload request CIC")
        config_custom = ConfigInfoApiModel().get_config_info_custom_send_request_cich2h(merchant_id)
        username = self._convert_username_send_cich2h(config_custom, username)

        config_api = ConfigInfoApiModel().get_config_info_api_send_request_inquiry_product_cic(merchant_id)

        product_type_request = ""
        if list_product_code:
            product_type_request = ",".join(list_product_code)
        customer_name = profile_name if customer_type == CustomerType.KHCN else ""

        client_name = customer_name if customer_name else company_name if company_name else ""

        branch_code = InternalAdminHelper().get_sol_id_by_account_id(merchant_id, account_id)

        payload_request_cic = {
            "customerType": self._convert_customer_type(customer_type),
            "productType": product_type_request,
            "customerName": client_name,
            "forceNewFlag": force_new_flag,
            "identityCard": customer_id,
            "cicCode": cic_code,
            "taxCode": company_tax_code,
            "businessRegistration": business_registration,
            "applicationRequest": config_api.get("applicationRequest", ""),
            "personInquiry": username,
            "branchCode": branch_code,
            "address": "",
        }

        (
            status_code,
            description,
            data_response,
            number_inquiry_remain,
            role_permission,
        ) = ThirdPartyCICHelper().send_request_inquiry_product(
            log_id,
            merchant_id,
            payload_request_cic,
        )
        if status_code != 200:
            raise CustomError(description, status_code)
        data_result = []

        mapping_status_record_cic = self.get_mapping_status_record_cic(merchant_id)
        mapping_type_status_record_cic = self.get_mapping_type_status_record_cic(merchant_id)

        data_reports = []

        action = "search_new" if force_new_flag else "reuse"

        data_insert_request_cic = {}
        account_data = InternalAdminHelper().get_account_detail_info(merchant_id, account_id)
        mapping_product = self.get_mapping_product_code_product_name(merchant_id)
        for item in data_response:
            status_code = item.get("statusCode")
            status_item = mapping_status_record_cic.get(status_code)
            sheet_number = item.get("sheetNumber")
            item_new = {
                "sheet_number": sheet_number,
                "cic_code": item.get("cicCode"),
                "report_status": status_code,
                "status_message": item.get("statusMessage"),
                "status_code": item.get("statusCode"),
                "product_code": item.get("productType"),
                "requested_date": self.convert_timestamp_to_iso(item.get("requestedDate")),
                "received_date": self.convert_timestamp_to_iso(item.get("receivedDate")),
                "error_code": item.get("errorCode"),
                "error_message": item.get("errorMessage"),
                "reuse_flag": item.get("reuseFlag"),
                "requester": item.get("requester"),
                "requested_user": item.get("requester"),
                "status": status_item,
                "report_doc_id": item.get("pdfDocId"),
                "report_doc_name": item.get("pdfFilename"),
                "request_fullname": item.get("requesterFullName"),
            }
            product_data = {
                "product_code": item.get("productType"),
                "product_name": mapping_product.get(item.get("productType")),
            }

            status_type = mapping_type_status_record_cic.get(status_code)

            if status_type == "wait_cic":
                data_insert_request_cic = {
                    "filter": body,
                    "sheet_number": sheet_number,
                    "account_id": account_id,
                    "merchant_id": merchant_id,
                    "request_time": datetime.datetime.now(datetime.UTC),
                    "status": "request",
                    "client_name": client_name,
                }

            data_result.append(item_new)
            item_report = self.build_data_insert_report_search_cic(
                merchant_id=merchant_id,
                action=action,
                report_result=status_item,
                account_data=account_data,
                product_data=product_data,
            )
            data_reports.append(item_report)

        if data_insert_request_cic:
            insert_request_cic = LogCicRequestSearchNewModel().insert_document(data_insert_request_cic)
            MobioLogging().info("action_search_new :: insert_request_cic :: {}".format(insert_request_cic.inserted_id))

        if data_reports:
            MobioLogging().info("action_search_new :: data_reports :: {}".format(data_reports))
            ReportCicModel().insert_many(data_reports)

        result = {
            "data": data_result,
            "number_inquiry_remain": -1 if number_inquiry_remain is None else number_inquiry_remain,
            "role_permission": role_permission,
        }
        return result

    @classmethod
    def _convert_username_send_cich2h(cls, config_custom, username):
        if not config_custom:
            return username
        replace_username = config_custom.get("replace_username")
        for item in replace_username:
            username = username.replace(item, "")
        return username

    def get_cic_code(self):
        log_id = str(uuid.uuid4())
        customer_type = request.args.get("customer_type")
        merchant_id = get_merchant_header()
        user_name = get_param_value_temp("username")
        """
            Sample body request:
            {
                "profile_identify": {
                    "customer_id": "123456789"
                },
                "profile_name": "Nguyen Van A",
                "cic_code": "123456789"
            }
        """
        body_request = request.get_json()

        config_custom = ConfigInfoApiModel().get_config_info_custom_send_request_cich2h(merchant_id)
        config_api = ConfigInfoApiModel().get_config_info_api_send_request_check_customer_cic(merchant_id)
        user_name = self._convert_username_send_cich2h(config_custom, user_name)
        profile_identify = body_request.get("profile_identify", {})
        customer_id = ""
        if profile_identify:
            customer_id = profile_identify.get("customer_id", "")
        payload_request_cic = {
            "customerType": self.get_mapping_customer_type_to_number(customer_type),
            "cicCode": body_request.get("cic_code"),
            "customerName": body_request.get("profile_name", ""),
            "customerId": customer_id,
            "taxCode": body_request.get("company_tax_code", ""),
            "businessRegistration": body_request.get("business_registration", ""),
            "applicationRequest": config_api.get("application_request", ""),
            "personInquiry": user_name,
            "address": "",
        }
        MobioLogging().info("payload_request_cic :: {}".format(payload_request_cic))

        status_code, description, data_response = ThirdPartyCICHelper().send_request_check_customer(
            log_id,
            merchant_id,
            payload_request_cic,
        )
        if status_code != 200 and status_code != 4006:
            raise CustomError(description, status_code)
        data_result = []
        for item in data_response:
            item_new = {
                "order": item.get("index"),
                "cic_code": item.get("cicCode"),
                "profile_name": item.get("customerName"),
                "company_tax_code": item.get("taxCode"),
                "profile_customer_id": item.get("idCustomer"),
                "address": item.get("address"),
                "business_registration": item.get("businessRegistration"),
                "created_by": item.get("createdBy"),
                "createdDate": self.convert_timestamp_to_iso(item.get("createdDate")),
            }
            data_result.append(item_new)

        result = {
            "code": status_code,
            "data": data_result,
            "message": description,
        }
        return result

    def get_product_cic(self):
        merchant_id = get_merchant_header()
        customer_type = request.args.get("customer_type")
        search = request.args.get("search")
        result_products = CicConfigModel().get_config_products(merchant_id)

        if not customer_type:
            products = []
            products.extend(result_products.get(CustomerType.KHCN, []))
            products.extend(result_products.get(CustomerType.KHDN, []))
        else:
            products = result_products.get(customer_type, [])

        results = []
        key_search = None
        if search:
            key_search = utf8_to_ascii(search).lower()
        for item in products:
            name_search = utf8_to_ascii(item.get("name", "")).lower()
            if key_search and key_search not in name_search:
                continue
            results.append(item)

        return {"data": results}

    @classmethod
    def get_mapping_customer_type_to_number(self, customer_type):
        return {CustomerType.KHCN: "2", CustomerType.KHDN: "1"}.get(customer_type)

    @classmethod
    def get_mapping_product_code_product_name(self, merchant_id):
        result_products = CicConfigModel().get_config_products(merchant_id)
        products = []
        products.extend(result_products.get(CustomerType.KHCN, []))
        products.extend(result_products.get(CustomerType.KHDN, []))
        return {product.get("code"): product.get("name") for product in products}

    @classmethod
    def _convert_time_query_send_cich2h(cls, start_time, end_time):
        if start_time and end_time:
            try:

                start_time = datetime.datetime.strptime(start_time, "%Y-%m-%dT%H:%MZ")
                start_time += datetime.timedelta(hours=7)
                start_time = start_time.strftime("%Y%m%d")
            except Exception as ex:
                raise CustomError("Convert datetime fail, error :: {}".format(str(ex)))
            try:
                # end_time = cls._convert_datetime(end_time)
                end_time = datetime.datetime.strptime(end_time, "%Y-%m-%dT%H:%MZ")
                end_time += datetime.timedelta(hours=7)
                end_time = end_time.strftime("%Y%m%d")
            except Exception as ex:
                raise CustomError("Convert datetime fail, error :: {}".format(str(ex)))
        else:
            end_time = datetime.datetime.now(datetime.UTC)
            start_time = end_time - datetime.timedelta(days=30)

            start_time += datetime.timedelta(hours=7)
            end_time += datetime.timedelta(hours=7)
            start_time = start_time.strftime("%Y%m%d")
            end_time = end_time.strftime("%Y%m%d")
        return {"fromDate": str(start_time), "toDate": str(end_time)}

    def get_history(self):
        log_id = str(uuid.uuid4())
        account_id = get_param_value_temp("id")
        customer_type = request.args.get("customer_type")
        merchant_id = get_merchant_header()
        user_name = get_param_value_temp("username")
        body_request = request.get_json()

        start_time = body_request.get("start_time")
        end_time = body_request.get("end_time")

        time_query = self._convert_time_query_send_cich2h(start_time, end_time)

        config_custom = ConfigInfoApiModel().get_config_info_custom_send_request_cich2h(merchant_id)
        config_api = ConfigInfoApiModel().get_config_info_api_send_request_search_history_cic(merchant_id)
        user_name = self._convert_username_send_cich2h(config_custom, user_name)

        profile_identify = body_request.get("profile_identify", {})
        customer_id = ""
        if profile_identify:
            customer_id = profile_identify.get("customer_id", "")

        payload_request_cic = {
            "customerType": self._convert_customer_type(customer_type),
            "cicCode": body_request.get("cic_code"),
            "customerID": customer_id,
            "taxCode": body_request.get("company_tax_code", ""),
            "businessLicense": body_request.get("business_registration", ""),
            "applicationRequest": config_api.get("application_request", ""),
            "personInquiry": user_name,
            "requestBy": user_name,
            **time_query,
        }

        list_product_code = body_request.get("list_product_code", [])
        if list_product_code:
            product_type_request = ",".join(list_product_code)
            payload_request_cic["productType"] = product_type_request
        MobioLogging().info("payload_request_cic :: {}".format(payload_request_cic))
        (
            status_code,
            description,
            data_response,
            number_inquiry_remain,
            role_permission,
        ) = ThirdPartyCICHelper().send_request_search_history(
            log_id,
            merchant_id,
            payload_request_cic,
        )
        if status_code and int(status_code) != 200:
            raise CustomError(description, status_code)
        mapping_status_record_cic = self.get_mapping_status_record_cic(merchant_id)
        data_result = []
        data_reports = []
        action = "search_history"
        account_data = InternalAdminHelper().get_account_detail_info(merchant_id, account_id)
        mapping_product = self.get_mapping_product_code_product_name(merchant_id)

        for item in data_response:
            status_item = mapping_status_record_cic.get(item.get("reportStatus"))

            item_new = {
                "order": item.get("index"),
                "cic_code": item.get("cicCode"),
                "profile_name": item.get("customerName"),
                "profile_customer_id": item.get("idCustomer"),
                "address": item.get("address"),
                "company_tax_code": item.get("taxCode"),
                "business_registration": item.get("businessLicense"),
                "product_code": item.get("productType"),
                "report_status": item.get("reportStatus"),
                "requested_date": self.convert_timestamp_to_iso(item.get("requestedDate")),
                "received_date": self.convert_timestamp_to_iso(item.get("receivedDate")),
                "report_doc_id": item.get("pdfDocId"),
                "report_doc_name": item.get("pdfFilename"),
                "reuse_flag": item.get("reuseFlag"),
                "sheet_number": item.get("sheetNumber"),
                "requested_user": item.get("requestedBy"),
                "status": status_item,
                "request_fullname": item.get("requesterFullName"),
            }
            data_result.append(item_new)
            item_report = self.build_data_insert_report_search_cic(
                merchant_id=merchant_id,
                action=action,
                report_result=status_item,
                account_data=account_data,
                product_data={
                    "product_code": item.get("productType"),
                    "product_name": mapping_product.get(item.get("productType")),
                },
            )
            data_reports = [item_report]

        if data_reports:
            MobioLogging().info("get_history :: data_reports :: {}".format(data_reports))
            ReportCicModel().insert_many(data_reports)

        result = {
            "data": data_result,
            "number_inquiry_remain": -1 if number_inquiry_remain is None else number_inquiry_remain,
            "role_permission": role_permission,
        }
        return result

    @classmethod
    def convert_timestamp_to_iso(cls, timestamp):
        if not timestamp:
            return ""
        return datetime.datetime.utcfromtimestamp(int(timestamp) / 1000).strftime("%Y-%m-%dT%H:%MZ")

    def detail_by_sheet_number(self, sheet_number):
        merchant_id = get_merchant_header()
        user_name = get_param_value_temp("username")

        config_custom = ConfigInfoApiModel().get_config_info_custom_send_request_cich2h(merchant_id)
        user_name = self._convert_username_send_cich2h(config_custom, user_name)

        log_id = str(uuid.uuid4())
        config_api = ConfigInfoApiModel().get_config_info_api_send_request_search_history_cic(merchant_id)
        payload_request = {
            "sheetNumber": sheet_number,
            "customerType": "",
            "cicCode": None,
            "customerID": "",
            "taxCode": None,
            "requestBy": user_name,
            "personInquiry": user_name,
            "businessLicense": "",
            "applicationRequest": config_api.get("application_request", "")
        }
        (
            status_code,
            description,
            data_response,
            number_inquiry_remain,
            _,
        ) = ThirdPartyCICHelper().send_request_search_history(
            log_id,
            merchant_id,
            payload_request,
        )
        if status_code and int(status_code) != 200:
            raise CustomError(description, status_code)
        # detail_extra_account = InternalAdminHelper().get_information_extra_account(merchant_id, account_id)

        result = {}
        mapping_report_status_cic = self.get_mapping_status_record_cic(merchant_id)
        if data_response:
            for item in data_response:
                if item.get("sheetNumber") != payload_request.get("sheetNumber"):
                    continue
                result = {
                    "cic_code": item.get("cicCode"),
                    "profile_name": item.get("customerName"),
                    "company_tax_code": item.get("taxCode"),
                    "profile_customer_id": item.get("idCustomer"),
                    "address": item.get("address"),
                    "business_registration": item.get("businessLicense"),
                    "product_code": item.get("productType"),
                    "report_status": item.get("reportStatus"),
                    "requested_user": item.get("requestedBy"),
                    "requested_date": self.convert_timestamp_to_iso(item.get("requestedDate")),
                    "received_date": self.convert_timestamp_to_iso(item.get("receivedDate")),
                    "report_doc_id": item.get("pdfDocId"),
                    "report_doc_name": item.get("pdfFilename"),
                    "reuse_flag": item.get("reuseFlag"),
                    "sheet_number": item.get("sheetNumber"),
                    "status": mapping_report_status_cic.get(item.get("reportStatus")),
                    "request_fullname": item.get("requesterFullName"),
                }
                break

        return {"data": result, "number_inquiry_remain": -1 if number_inquiry_remain is None else number_inquiry_remain}

    def get_link_file_report(self, report_id):
        merchant_id = get_merchant_header()
        user_name = get_param_value_temp("username")
        config_custom = ConfigInfoApiModel().get_config_info_custom_send_request_ecm(merchant_id)
        user_name = self._convert_username_send_cich2h(config_custom, user_name)
        log_id = str(uuid.uuid4())
        (
            status_code,
            description,
            data_response,
        ) = ThirdPartyECMHelper.send_request_download_file(
            log_id,
            merchant_id,
            username=user_name,
            doc_id=report_id,
        )
        if status_code and int(status_code) != 200:
            raise CustomError(description, status_code)

        return data_response

    def _validate_upsert_save_view(self, data_validate):
        rule_validate = {
            "area_code": [Required, InstanceOf(str), Length(1), In(ConstantCicAreaCode.get_all_attribute())],
            "filters": [Required, InstanceOf(dict), Length(1)],
        }
        if data_validate.get("area_code") in [ConstantCicAreaCode.DETAIL_COMPANY, ConstantCicAreaCode.DETAIL_PROFILE]:
            rule_validate["object_id"] = [Required, InstanceOf(str), Length(1)]
        self.validate_optional_err(rule_validate, data_validate)

    def upsert_save_view(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        body = request.get_json()

        self._validate_upsert_save_view(body)

        filter_option = {
            "area_code": body.get("area_code"),
            "object_id": body.get("object_id", ""),
            "merchant_id": merchant_id,
            "account_id": account_id,
            "type": "CIC",
        }

        MobioLogging().info("upsert_save_view :: filter_option :: {}".format(filter_option))

        data_upsert = {
            "area_code": body.get("area_code"),
            "object_id": body.get("object_id", ""),
            "merchant_id": merchant_id,
            "account_id": account_id,
            "type": "CIC",
            "filters": body.get("filters"),
            "action_time": datetime.datetime.utcnow(),
        }

        status_upsert = SaveViewAccountModel().upsert(filter_option, data_upsert)
        MobioLogging().info("upsert_save_view :: status_upsert :: {}".format(status_upsert))

        return {"data": MobioJSONEncoderV2().json_loads(data_upsert)}

    def _validate_filters_save_view(self, data_validate):
        rule_validate = {
            "area_code": [Required, InstanceOf(str), Length(1), In(ConstantCicAreaCode.get_all_attribute())],
        }
        if data_validate.get("area_code") in [ConstantCicAreaCode.DETAIL_COMPANY, ConstantCicAreaCode.DETAIL_PROFILE]:
            rule_validate["object_id"] = [Required, InstanceOf(str), Length(1)]
        self.validate_optional_err(rule_validate, data_validate)

    def filter_save_view(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        body = request.get_json()

        self._validate_filters_save_view(body)

        filter_option = {
            "area_code": body.get("area_code"),
            "object_id": body.get("object_id", ""),
            "merchant_id": merchant_id,
            "account_id": account_id,
            "type": "CIC",
        }

        MobioLogging().info("filters_save_view :: filter_option :: {}".format(filter_option))

        data = SaveViewAccountModel().find_one(filter_option)
        if not data:
            return
        MobioLogging().info("filters_save_view :: data :: {}".format(data))

        return {"data": MobioJSONEncoderV2().json_loads(data)}

    def _validate_report_history_list(self, data_validate):
        rule_validate = {
            "start_time": [Required, InstanceOf(str), Length(1)],
            "end_time": [Required, InstanceOf(str), Length(1)],
            "area_codes": [],
            "sol_ids": [],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def _validate_report_type(self, report_type):
        if report_type not in ConstantCicReportType.get_all_attribute():
            raise CustomError("Report type not support!!")

    def report_history_list(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        body = request.get_json()

        report_type = request.args.get("type")
        self._validate_report_type(report_type)
        self._validate_report_history_list(body)

        start_time = body.get("start_time")
        end_time = body.get("end_time")
        area_codes = body.get("area_codes")
        sol_ids = body.get("sol_ids")

        try:
            start_time = datetime.datetime.strptime(start_time, "%Y-%m-%dT%H:%MZ")
        except Exception as ex:
            raise CustomerType("Format time error!!!")
        try:
            end_time = datetime.datetime.strptime(end_time, "%Y-%m-%dT%H:%MZ")
        except Exception as ex:
            raise CustomerType("Format time error!!!")

        filter_query = {
            "merchant_id": merchant_id,
            "$and": [
                {
                    "action_time": {"$gte": start_time},
                },
                {"action_time": {"$lte": end_time}},
            ],
        }
        lst_account_lower_levels = InternalAdminHelper().get_list_account_by_level_account_current(
            merchant_id, account_id
        )
        if lst_account_lower_levels:
            filter_query.update({"account_id": {"$in": lst_account_lower_levels}})
        if area_codes:
            filter_query.update({"area_code": {"$in": area_codes}})
        if sol_ids:
            filter_query.update({"sol_id": {"$in": sol_ids}})

        MobioLogging().info("report_history_list :: filter_query :: {}".format(filter_query))
        report_datas = []
        if report_type == "number_of_searches":
            report_datas = ReportCicModel().aggregate_report_cic_list_by_day(filter_query)
            report_datas = [*report_datas]
        if report_type == "user":
            report_datas = ReportCicModel().aggregate_report_cic_list_by_user(filter_query)
            report_datas = [*report_datas]

        num_days = (end_time.date() - start_time.date()).days + 1
        all_dates = [start_time.date() + datetime.timedelta(days=i) for i in range(num_days)]

        result_by_date = {}
        for single_date in all_dates:
            single_date += datetime.timedelta(hours=7)
            date_str = single_date.strftime("%Y-%m-%d")
            result_by_date.update(
                {
                    date_str: {
                        "date": date_str,
                        "number_fail_searches": 0,
                        "number_new_searches": 0,
                        "number_success_searches": 0,
                    }
                }
            )
        for item in report_datas:
            item_date = item.get("date")
            result_by_date[item_date] = item

        return {"data": list(result_by_date.values())}

    def _validate_total_report_history(self, data_validate):
        rule_validate = {
            "start_time": [Required, InstanceOf(str), Length(1)],
            "end_time": [Required, InstanceOf(str), Length(1)],
            "area_codes": [],
            "sol_ids": [],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def total_report_history(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        body = request.get_json()

        report_type = request.args.get("type")
        self._validate_report_type(report_type)

        self._validate_total_report_history(body)
        start_time = body.get("start_time")
        end_time = body.get("end_time")
        area_codes = body.get("area_codes")
        sol_ids = body.get("sol_ids")

        try:
            start_time = datetime.datetime.strptime(start_time, "%Y-%m-%dT%H:%MZ")
        except Exception as ex:
            raise CustomerType("Format time error!!!")
        try:
            end_time = datetime.datetime.strptime(end_time, "%Y-%m-%dT%H:%MZ")
        except Exception as ex:
            raise CustomerType("Format time error!!!")

        filter_query = {
            "merchant_id": merchant_id,
            "$and": [
                {
                    "action_time": {"$gte": start_time},
                },
                {"action_time": {"$lte": end_time}},
            ],
        }
        lst_account_lower_levels = InternalAdminHelper().get_list_account_by_level_account_current(
            merchant_id, account_id
        )
        if lst_account_lower_levels:
            filter_query.update({"account_id": {"$in": lst_account_lower_levels}})
        if area_codes:
            filter_query.update({"area_code": {"$in": area_codes}})
        if sol_ids:
            filter_query.update({"sol_id": {"$in": sol_ids}})

        report_datas = []
        if report_type == "number_of_searches":
            report_datas = ReportCicModel().aggregate_total_report_cic_day(filter_query)
            report_datas = [*report_datas]
        if report_type == "user":
            report_datas = ReportCicModel().aggregate_total_report_cic_user(filter_query)
            report_datas = [*report_datas]
        data = {"total_number_searches": 0, "total_number_new_searches": 0}
        if report_datas:
            report_data = report_datas[0]
            data = {
                "total_number_new_searches": report_data.get("total_number_new_searches", 0),
                "total_number_searches": report_data.get("total_number_searches_success", 0)
                + report_data.get("total_number_searches_fail", 0),
            }

        return {"data": data}

    def _validate_export_report_history(self, data_validate):
        rule_validate = {
            "start_time": [Required, InstanceOf(str), Length(1)],
            "end_time": [Required, InstanceOf(str), Length(1)],
            "area_codes": [],
            "sol_ids": [],
            "emails": [Required, InstanceOf(list), Length(1)],
        }
        self.abort_if_validate_error(rule_validate, data_validate)

    def export_report_history(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        body = request.get_json()

        self._validate_export_report_history(body)
        RequestExportReportCicModel().insert_document(
            {
                "merchant_id": merchant_id,
                "account_request_id": account_id,
                "filter": {
                    "start_time": body.get("start_time"),
                    "end_time": body.get("end_time"),
                    "area_codes": body.get("area_codes"),
                    "sol_ids": body.get("sol_ids"),
                },
                "emails": body.get("emails"),
                "status": "request",
                "request_time": datetime.datetime.utcnow(),
            }
        )

        return

    def cic_receiver_search_results(self):
        merchant_id = get_merchant_header()
        body = request.get_json()

        data = body.get("data")

        mapping_data_by_sheet_numbers = {item["sheetNumber"]: item for item in data}

        sheet_numbers = list(mapping_data_by_sheet_numbers.keys())

        MobioLogging().info("cic_receiver_search_results :: sheet_numbers :: {}".format(sheet_numbers))

        filter_query = {"merchant_id": merchant_id, "sheet_number": {"$in": sheet_numbers}, "status": "request"}
        lst_request_by_sheet_numbers = LogCicRequestSearchNewModel().find(filter_query)

        mapping_report_status_cic = self.get_mapping_status_record_cic(merchant_id)
        for request_by_sheet_number in lst_request_by_sheet_numbers:

            sheet_number = request_by_sheet_number.get("sheet_number")
            data_response = mapping_data_by_sheet_numbers.get(sheet_number)

            filter_data = request_by_sheet_number.get("filter")
            account_request = request_by_sheet_number.get("account_id")

            client_name = request_by_sheet_number.get("client_name")

            data_send = {
                "filter": filter_data,
                "data": {
                    "report_status": data_response.get("status_code"),
                    "requested_user": data_response.get("requester"),
                    "requested_date": self.convert_timestamp_to_iso(data_response.get("requestedDate")),
                    "received_date": self.convert_timestamp_to_iso(data_response.get("receivedDate")),
                    "report_doc_id": data_response.get("pdfDocId"),
                    "report_doc_name": data_response.get("pdfFilename"),
                    "sheet_number": data_response.get("sheetNumber"),
                    "status": mapping_report_status_cic.get(mapping_report_status_cic.get("reportStatus")),
                    "highline_text": client_name,
                },
            }
            MobioNotifySDK().send_message_notify_push_id_mobile_app(
                merchant_id=merchant_id,
                key_config="notify_default",
                account_ids=[account_request],
                socket_type="mobilebackend_reply_request_search_cic",
                title="Kết quả tra cứu CIC của khách hàng {} đã sẵn sàng.".format(client_name),
                content="Kết quả tra cứu CIC của khách hàng {} đã sẵn sàng.".format(client_name),
                **data_send,
            )

        status_update = LogCicRequestSearchNewModel().update_by_set(
            filter_query,
            {"type": "response", "status": "response", "response_time": datetime.datetime.now(datetime.UTC)},
        )
        MobioLogging().info("cic_receiver_search_results :: status_update :: {}".format(status_update.matched_count))

        return


if __name__ == "__main__":
    print(CicController()._convert_time_query_send_cich2h("2024-12-18T00:00Z", "2025-12-18T00:00Z"))
    print(CicController()._convert_time_query_send_cich2h(None, None))
