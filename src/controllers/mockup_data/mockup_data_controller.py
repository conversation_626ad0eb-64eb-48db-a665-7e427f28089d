#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 12/02/2025
"""

import base64
import json
import random
import shlex
import time
import uuid
from datetime import datetime, timedelta
from hashlib import sha256

from Crypto.Cipher import DES3
from Crypto.Util.Padding import unpad
from flask import jsonify, render_template, request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.base.controllers import BaseController

from scripts.test.script_test_sign_key_vnpay import ThirdPartyEIB
from src.apis import HTTP
from src.common import ConstantKeyConfigSendThirdParty, ThirdPartyType
from src.common.requests_retry import RequestRetryAdapter
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)
from src.models.mongo.mockup_data.mockup_data_customer_model import (
    MockupDataCustomerModel,
)
from src.models.mongo.mockup_data.mockup_lock_account_model import (
    MockupLockAccountModel,
)
from src.models.mongo.result_sample_model import ResultSampleModel


class ConstantTypeSendOTP:
    SMS_CONFIRM_NFC = "Xác nhận luồng NFC"
    SMS_OTP_EYKC = "Xác nhận luồng submit LandingPage eYKC"


class MockupDataController(BaseController):
    def __init__(self):
        super().__init__()
        self.group_otp_lark_bot = (
            "https://open.larksuite.com/open-apis/bot/v2/hook/b00d6007-fa9b-4dcb-a83f-110d84b03f76"
        )
        self.type_descriptions = {
            "01": "so giong nhau",
            "02": "So phat loc 6,8",
            "03": "So phat tai 39,79",
            "04": "So tien",
            "05": "So luc, ngu, tu, tam hoa",
            "06": "So phat loc 368,386",
            "07": "So lap",
            "08": "So soi guong",
            "10": "So ngu, tu, tam hoa nhuy giua",
            "11": "So ngu, tu, tam hoa khong nhuy",
            "12": "So ngu, tu, tam hoa bang nhau",
            "13": "So ngu, tu, tam hoa khac nhau",
            "14": "So luu y dac biet",
            "15": "So tien cap",
            "19": "So hon hop",
        }

    def _send_notify_group_otp_lark_bot(
        self, message, phone_number, otp=None, type=ConstantTypeSendOTP.SMS_CONFIRM_NFC
    ):
        payload = {
            "msg_type": "interactive",
            "card": {
                "config": {"wide_screen_mode": True},
                "elements": [],
                "header": {"template": "blue", "title": {"content": "OTP - {}".format(type), "tag": "plain_text"}},
            },
        }

        elements = [
            {"tag": "markdown", "content": "Nội dung tin nhắn: **message**".replace("message", message)},
            {"tag": "hr"},
            {
                "tag": "markdown",
                "content": "Số điện thoại: **{{phone_number}}**".replace("{{phone_number}}", phone_number),
            },
        ]
        if otp:
            elements.extend(
                [
                    {"tag": "hr"},
                    {
                        "tag": "markdown",
                        "content": "OTP: **{{otp}}**".replace("{{otp}}", otp),
                    },
                ]
            )

        payload["card"]["elements"] = elements
        response_push = RequestRetryAdapter().retry_a_post_request(
            self.group_otp_lark_bot, payload=payload, times=5, headers={}
        )
        MobioLogging().info("response_push: %s", response_push.json())
        return jsonify(response_push.json()), 200

    def send_notify(self):
        """
        URL webhook: https://open.larksuite.com/open-apis/bot/v2/hook/b00d6007-fa9b-4dcb-a83f-110d84b03f76
        Nghiệp vụ đoạn này sẽ push notification lên bot lark
        Cần add user vào group lark để nhận được notification
        """

        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        message = body_request.get("Message")
        phone_number = body_request.get("PhoneNumber")
        self._send_notify_group_otp_lark_bot(message, phone_number, None, ConstantTypeSendOTP.SMS_CONFIRM_NFC)
        return jsonify({"message": "success"}), 200

    def _generate_cust_id(self, length=9):
        """
        Generate a random customer ID string with specified length.
        Default length is 9 digits (like "121559748").

        Args:
            length (int): Length of the customer ID to generate

        Returns:
            str: Random numeric string of specified length
        """
        # First digit should not be 0
        first_digit = str(random.randint(1, 9))
        # Generate remaining digits
        remaining_digits = "".join(str(random.randint(0, 9)) for _ in range(length - 1))
        return first_digit + remaining_digits

    def add_cif_ret_cust(self):
        """
        Nghiệp vụ đoạn này sẽ add cif ret cust
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        if not body_request.get("PrimarySolId"):
            return jsonify({"message": "PrimarySolId is required"}), 400

        idCard = body_request.get("EntityDoctData_ReferenceNum")
        customer = MockupDataCustomerModel().get_customer_by_id_card(idCard)
        if not customer:
            return (
                jsonify(
                    {
                        "CustId": None,
                        "Desc": "Customer not found",
                        "Status": "FAIL",
                        "ErrorCode": "1",
                        "ErrorDesc": "Customer not found",
                    }
                ),
                400,
            )

        if customer.get("fail_when_create_cif"):
            return (
                jsonify(
                    {
                        "CustId": None,
                        "Desc": "Fail when create cif",
                        "Status": "FAIL",
                        "ErrorCode": "1",
                        "ErrorDesc": "Fail when create cif",
                    }
                ),
                400,
            )

        cust_id = self._generate_cust_id()

        customer_id = customer.get("_id")
        MockupDataCustomerModel().update_one_query({"_id": customer_id}, {"cifCore": cust_id})

        return (
            jsonify(
                {
                    "CustId": cust_id,
                    "Desc": f"Retail Customer successfully created with CIFID {cust_id}",
                    "Status": "SUCCESS",
                    "ErrorCode": "0",
                    "ErrorDesc": "SUCCESS",
                }
            ),
            200,
        )

    def register_edigi_account(self):
        """
        Đăng ký mở tài khoản EDigi
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        # rule_validate = {
        #     "name": [Required, InstanceOf(str), Length(1)],  # Họ và tên
        #     "username": [Required, InstanceOf(str), Length(1)],  # SĐT đăng nhập
        #     "mobileOtp": [Required, InstanceOf(str), Length(1)],  # SĐT nhận OTP
        #     "type": [Required, InstanceOf(str), Length(1)],  # Loại khách hàng: "100"/"10001" - Cá nhân
        #     "packageCode": [Required, InstanceOf(str), Length(1)],  # Mã gói
        #     "packageType": [Required, InstanceOf(str), Length(1)],  # Loại gói
        #     "idNumber": [Required, InstanceOf(str), Length(1)],  # Số cmnd/pp/cccd
        #     "issueDate": [Required, InstanceOf(str), Length(1)],  # Ngày cấp (dd/MM/yyyy)
        #     "issuePlace": [Required, InstanceOf(str), Length(1)],  # Nơi cấp
        #     "email": [Required, InstanceOf(str), Length(1)],  # Email nhận thông tin
        #     "address": [Required, InstanceOf(str), Length(1)],  # Địa chỉ
        #     "cifCore": [Required, InstanceOf(str), Length(1)],  # Số cif KH
        #     "dateOfBirth": [Required, InstanceOf(str), Length(1)],  # Ngày sinh (dd/MM/yyyy)
        #     "resident": [Required, InstanceOf(str), Length(1)],  # Y: Cư trú; N: Không cư trú
        #     "nationality": [Required, InstanceOf(str), Length(1)],  # Quốc tịch
        #     "gender": [Required, InstanceOf(str), Length(1)],  # F: female; M: male
        #     "emailReceived": [Required, InstanceOf(str), Length(1)],  # Email
        #     "mobileContact": [Required, InstanceOf(str), Length(1)],  # SĐT liên hệ
        #     "branchCodeCif": [Required, InstanceOf(str), Length(1)],  # Chi nhánh mở cif
        #     "posCodeCif": [Required, InstanceOf(str), Length(1)],  # PGD mở cif
        #     "accountNo": [Required, InstanceOf(str), Length(1)],  # Tài khoản mặc định
        #     "rank": [Required, InstanceOf(str), Length(1)],  # Hạng khách hàng [S:Silver] [G:Gold] [P:Platinum]
        #     "authenMethod": [Required, InstanceOf(str), Length(1)],  # 1-SMS-OTP, 2-SOFT-OTP
        #     "idType": [Required, InstanceOf(str), Length(1)],  # CMND/CCUOC
        #     "mainCcy": [Required, InstanceOf(str), Length(1)],  # VND, USD
        #     "userAlias": [Required, InstanceOf(str), Length(1)],  # userAlias
        #     "staffCode": [Required, InstanceOf(str), Length(1)],  # Mã nhân viên mở cif
        #     "signature": [Required, InstanceOf(str), Length(1)],  # Chuỗi chữ ký SHA256
        # }

        # self.abort_if_validate_error(rule_validate, body_request)

        cif_core = body_request.get("cifCore")
        customer = MockupDataCustomerModel().find_one({"cifCore": cif_core})
        if not customer:
            return jsonify({"AcctId": None, "ErrorCode": "1", "ErrorDesc": "Customer not found"}), 400

        if customer.get("fail_when_create_account_edigi"):
            return (
                jsonify(
                    {
                        "mid": "0",
                        "code": "100",
                        "des": "Fail when create account edigi",
                        "mesCode": "OMNI-ERROR-01",
                        "data": "",
                    }
                ),
                400,
            )

        # Giả lập response thành công
        return (
            jsonify(
                {
                    "mid": "0",
                    "code": "00",
                    "des": "Thành công",
                    "mesCode": "OMNI-ERROR-01",
                    "data": "",
                }
            ),
            200,
        )

    def vtop_publish(self):
        """
        Đăng ký mở thẻ VTOP
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        rule_validate = {
            "flag_status": [Required, InstanceOf(str), Length(1)],  # N: Insert, U: Update, T: Transfer, D: Delete
            "brcd": [Required, InstanceOf(str), Length(4)],  # Mã chi nhánh
            "seqno": [Required, InstanceOf(str), Length(8)],  # Truyền seqno để update dữ liệu
            "ppscrlmt": [Required, InstanceOf(str), Length(21, 3)],
            "fullnm": [Required, InstanceOf(str), Length(30)],  # Họ Tên
            "prnnm": [Required, InstanceOf(str), Length(30)],  # Tên in trên thẻ
            "birthdt": [Required, InstanceOf(str), Length(8)],  # Ngày sinh
            "sex": [Required, InstanceOf(str), Length(1)],  # Giới tính
            "idcard": [Required, InstanceOf(str), Length(50)],  # CMND/Passport ID
            "pmnaddr": [Required, InstanceOf(str), Length(255)],
            "phoneno": [Required, InstanceOf(str), Length(20)],  # Điện thoại 1
            "officenm": [Required, InstanceOf(str), Length(50)],  # Tên Cty
            "officetelno": [Required, InstanceOf(str), Length(50)],  # Điện thoại cty 1
            "officepos": [Required, InstanceOf(str), Length(20)],  # Chức vụ
            "annual": [Required, InstanceOf(str), Length(21, 3)],
            "settlp": [Required, InstanceOf(str), Length(1)],  # Hạn mức trong ngày
            "dracctno": [Required, InstanceOf(str), Length(20)],  # Trích lục tự động từ tài khoản
            "drratio": [Required, InstanceOf(str), Length(6, 3)],  # Trích lục tự động hay không
            "receiptdt": [Required, InstanceOf(str), Length(8)],  # Ngày xử lý
            "crlmt": [Required, InstanceOf(str), Length(21, 3)],
            "expdt": [Required, InstanceOf(str), Length(8)],  # Ngày hết hạn
            "checked": [Required, InstanceOf(str), Length(30)],
            "checkeddt": [Required, InstanceOf(str), Length(8)],
            "approved": [Required, InstanceOf(str), Length(30)],
            "approveddt": [Required, InstanceOf(str), Length(8)],
            "status": [Required, InstanceOf(str), Length(1)],
            "photofilename": [Required, InstanceOf(str), Length(20)],
            "appdate": [Required, InstanceOf(str), Length(8)],  # Ngày tạo
            "cardtype": [Required, InstanceOf(str), Length(9)],  # Loại thẻ
            "mobifon": [Required, InstanceOf(str), Length(20)],  # Di động
            "email": [Required, InstanceOf(str), Length(40)],  # Email
            "note": [Required, InstanceOf(str), Length(255)],
            "custseq": [Required, InstanceOf(str), Length(9)],  # Mã CIF
            "acctno": [Required, InstanceOf(str), Length(20)],  # Số tài khoản
            "ccy": [Required, InstanceOf(str), Length(3)],  # Loại tiền
            "dpchk": [Required, InstanceOf(str), Length(1)],
            "brname": [Required, InstanceOf(str), Length(50)],
            "taxcd": [Required, InstanceOf(str), Length(20)],  # Mã công ty
            "tradeid": [Required, InstanceOf(str), Length(3)],
            "addr1": [Required, InstanceOf(str), Length(250)],  # Địa chỉ 1
            "addr2": [Required, InstanceOf(str), Length(250)],  # Địa chỉ 2
            "citynm": [Required, InstanceOf(str), Length(50)],  # Thành phố
            "phoneno2": [Required, InstanceOf(str), Length(20)],  # Điện thoại 2
            "offaddr1": [Required, InstanceOf(str), Length(100)],  # Địa chỉ cty 1
            "offaddr2": [Required, InstanceOf(str), Length(100)],  # Địa chỉ cty 1
            "offcity": [Required, InstanceOf(str), Length(50)],  # Thành phố của cty
            "offphoneno2": [Required, InstanceOf(str), Length(50)],  # Điện thoại cty 2
            "empeid": [Required, InstanceOf(str), Length(30)],
            "deptcd": [Required, InstanceOf(str), Length(4)],  # Mã chi nhánh
            "addusrid": [Required, InstanceOf(str), Length(10)],  # User Add
            "apprusrid": [Required, InstanceOf(str), Length(10)],
            "brapprusrid": [Required, InstanceOf(str), Length(10)],  # CN phát hành thẻ
            "companycode": [Required, InstanceOf(str)],  # Mã công ty
        }

        self.abort_if_validate_error(rule_validate, body_request)

        # Giả lậm response thành công
        return (
            jsonify(
                {
                    "status_return": "1",
                    "description_message": "Insert the chinh thanh cong",
                    "brcd": "2207",
                    "seqno": "00639768",
                    "ppscrlmt": "0",
                    "fullnm": "LE VAN CHUONG",
                    "prnnm": "LE VAN CHUONG",
                    "birthdt": "19970225",
                    "sex": "1",
                    "idcard": "*********",
                    "pmnaddr": "102 phan phan phan phan",
                    "phoneno": "**********",
                    "officenm": None,
                    "officetelno": None,
                    "officepos": None,
                    "annual": None,
                    "settltp": None,
                    "dracctno": None,
                    "drratio": "0",
                    "receiptdt": "00639768",
                    "crlmt": "0",
                    "expdt": "20280527",
                    "checked": "HOA.DV",
                    "checkeddt": "********",
                    "approved": "LAN.DTH",
                    "approveddt": "********",
                    "status": "7",
                    "photofilename": None,
                    "appdate": "********",
                    "cardtype": None,
                    "mobifon": "**********",
                    "email": "<EMAIL>",
                    "note": "e",
                    "custseq": "*********",
                    "accountcd": None,
                    "cardnbr": None,
                    "acctno": "*********",
                    "ccy": "VND",
                    "dpchk": None,
                    "brname": "Eximbank Ben Chuong Duong",
                    "taxcd": None,
                    "tradeid": "C006",
                    "addr1": "Hoai Chau, Hoai Nhon, Binh Dinh",
                    "addr2": "Hoai Chau, Hoai Nhon, Binh Dinh",
                    "citynm": "Binh Dinh",
                    "phoneno2": None,
                    "offaddr1": None,
                    "offaddr2": None,
                    "offcity": None,
                    "offphoneno2": None,
                    "empeid": None,
                    "deptcd": "2207",
                    "accountcd": None,
                    "acctno": "*********",
                    "cardnbr": None,
                }
            ),
            200,
        )

    def read_card(self):
        """
        Hàm chỉ đọc thẻ CCCD gắn chip
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        # rule_validate = {
        #     "requestId": [Required, InstanceOf(str)],  # Mã định danh request UUID
        #     "clientId": [Required, InstanceOf(str)],  # Mã định danh phần mềm gửi yêu cầu
        #     "timestamp": [Required, InstanceOf(str)],  # Thời gian request
        #     "dataRequest": [Required, InstanceOf(str)],  # Đối tượng chứa thông tin request đọc dữ liệu CCCD
        #     "signature": [InstanceOf(str)],  # Chữ ký điện tử
        # }

        # self.abort_if_validate_error(rule_validate, body_request)

        idCardRequest = body_request.get("idCardRequest")
        if idCardRequest:
            mockup_data_customer = MockupDataCustomerModel().find_one({"id_card": idCardRequest})
        else:
            mockup_data_customer = MockupDataCustomerModel().find_one({"set_data_default": True})
        if mockup_data_customer:
            if mockup_data_customer.get("id_verified"):
                ethnic = mockup_data_customer.get("ethnic")
                address = mockup_data_customer.get("address")
                gender = mockup_data_customer.get("gender")
                id_card = mockup_data_customer.get("id_card")
                origin = mockup_data_customer.get("origin")
                dob = mockup_data_customer.get("dob")
                date_of_issue = mockup_data_customer.get("date_of_issue")
                name = mockup_data_customer.get("name")
                old_id_card = mockup_data_customer.get("old_id_card")

                json_data = {
                    "deviceType": "TEST_DEVICE",
                    "dateOfExpiry": "10/09/2035",
                    "fatherName": "",
                    "ethnic": ethnic,
                    "address": address,
                    "gender": gender,
                    "idCard": id_card,
                    "placeOfResidence": "{address}, {origin}".format(address=address, origin=origin),
                    "motherName": "",
                    "dateOfBirth": dob,
                    "dateOfIssuance": date_of_issue,
                    "personalSpecificIdentification": "Sẹo chấm C:1cm5 sau cánh mũi trái",
                    "religion": "Phật giáo",
                    "nationality": "Việt Nam",
                    "oldIdCardNo": old_id_card,
                    "placeOfOrigin": origin,
                    "name": name,
                    "spouseName": "",
                    "cardImage": "iVBORw0KGgoAAAANSUhEUgAAAlgAAAMgCAIAAABwAouTAAAFi0lEQVR4nO3BMQEAAADCoPVPbQwfoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC+Bv1bAAGXiLn2AAAAAElFTkSuQmCC",
                }

                data_response = {
                    "responseId": str(uuid.uuid4()),
                    "responseCode": "1000",
                    "responseMessage": "Thành công.",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "referenceId": "AB" + str(random.randint(10000000000, 99999999999)) + str(int(time.time())),
                    "dataResponse": json.dumps(json_data),
                    "signature": None,
                }

            else:
                data_response = {
                    "responseId": str(uuid.uuid4()),
                    "responseCode": "1040",
                    "responseMessage": "Không tìm thấy thông tin trong hệ thống.",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }
            return jsonify(data_response), 200

        result_sample = ResultSampleModel().find_one({"path_api": "ReadCard"})
        if result_sample:
            return jsonify(result_sample.get("data")), 200
        return jsonify({"message": "success"}), 200

    def check_card(self):
        """
        Hàm xác thực thẻ CCCD gắn chip
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        # rule_validate = {
        #     # "requestId": [Required, InstanceOf(str)],  # Mã định danh request UUID
        #     # "clientId": [Required, InstanceOf(str)],  # Mã định danh phần mềm gửi yêu cầu
        #     # "timestamp": [Required, InstanceOf(str)],  # Thời gian request
        #     # "dataRequest": [Required, InstanceOf(str)],  # Đối tượng chứa thông tin request đọc dữ liệu CCCD
        #     # "encryptedData": [Required, InstanceOf(str)],  # Object JSON string mã hóa AES
        #     # "requestTimeStamp": [Required, InstanceOf(str)],  # timestamp tại thời điểm request
        #     # "requestSession": [Required, InstanceOf(str)],  # Chuỗi uuid dùng để mã hóa data
        #     # "requestRound": [Required, InstanceOf(str)],  # Base64 số vòng mã hóa
        #     # "signature": [InstanceOf(str)],  # Chữ ký điện tử
        # }

        # self.abort_if_validate_error(rule_validate, body_request)
        idCardRequest = body_request.get("idCardRequest")
        if idCardRequest:
            mockup_data_customer = MockupDataCustomerModel().find_one({"id_card": idCardRequest})
        else:
            mockup_data_customer = MockupDataCustomerModel().find_one({"set_data_default": True})

        if mockup_data_customer:
            if mockup_data_customer.get("id_verified"):
                ethnic = mockup_data_customer.get("ethnic")
                address = mockup_data_customer.get("address")
                gender = mockup_data_customer.get("gender")
                id_card = mockup_data_customer.get("id_card")
                origin = mockup_data_customer.get("origin")
                dob = mockup_data_customer.get("dob")
                name = mockup_data_customer.get("name")
                result_check_card = mockup_data_customer.get("check_card", False)
                old_id_card = mockup_data_customer.get("old_id_card")

                json_data = {
                    "deviceType": "TEST_DEVICE",
                    "dateOfExpiry": "10/09/2035",
                    "fatherName": "",
                    "ethnic": ethnic,
                    "address": address,
                    "gender": gender,
                    "idCard": id_card,
                    "placeOfResidence": "{address}, {origin}".format(address=address, origin=origin),
                    "motherName": "",
                    "dateOfBirth": dob,
                    "personalSpecificIdentification": "Sẹo chấm C:1cm5 sau cánh mũi trái",
                    "dateOfIssuance": "12/09/2022",
                    "religion": "Phật giáo",
                    "result": result_check_card,
                    "nationality": "Việt Nam",
                    "oldIdCardNo": old_id_card,
                    "placeOfOrigin": origin,
                    "name": name,
                    "spouseName": "",
                    "cardImage": "iVBORw0KGgoAAAANSUhEUgAAAlgAAAMgCAIAAABwAouTAAAFi0lEQVR4nO3BMQEAAADCoPVPbQwfoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC+Bv1bAAGXiLn2AAAAAElFTkSuQmCC",
                }
                data_response = {
                    "responseId": str(uuid.uuid4()),
                    "responseCode": "1000",
                    "responseMessage": "Thành công.",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "referenceId": "AB" + str(random.randint(10000000000, 99999999999)) + str(int(time.time())),
                    "dataResponse": json.dumps(json_data),
                    "signature": None,
                }
            return jsonify(data_response), 200
        result_sample = ResultSampleModel().find_one({"path_api": "CheckCard"})
        if result_sample:
            return jsonify(result_sample.get("data")), 200
        return jsonify({"message": "success"}), 200

    def check_face(self):
        """
        Hàm so sánh/đối chiếu ảnh khuôn mặt
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        # rule_validate = {
        #     "requestId": [Required, InstanceOf(str)],  # Mã định danh request UUID
        #     "clientId": [Required, InstanceOf(str)],  # Mã định danh phần mềm gửi yêu cầu
        #     "timestamp": [Required, InstanceOf(str)],  # Thời gian request
        #     "dataRequest": [Required, InstanceOf(str)],  # Đối tượng chứa thông tin request đọc dữ liệu CCCD
        #     "encryptedData": [Required, InstanceOf(str)],  # Object JSON string mã hóa AES
        #     "requestTimeStamp": [Required, InstanceOf(str)],  # timestamp tại thời điểm request
        #     "requestSession": [Required, InstanceOf(str)],  # Chuỗi uuid dùng để mã hóa data
        #     "requestRound": [Required, InstanceOf(str)],  # Base64 số vòng mã hóa
        #     "rawImg1": [Required, InstanceOf(str)],  # Base64 ảnh 1 (Bỏ qua 100 ký tự đầu)
        #     "rawImg2": [Required, InstanceOf(str)],  # Base64 ảnh 2 (Bỏ qua 100 ký tự đầu)
        #     "signature": [InstanceOf(str)],  # Chữ ký điện tử
        # }

        # self.abort_if_validate_error(rule_validate, body_request)

        idCardRequest = body_request.get("idCardRequest")
        if idCardRequest:
            mockup_data_customer = MockupDataCustomerModel().find_one({"id_card": idCardRequest})
        else:
            mockup_data_customer = MockupDataCustomerModel().find_one({"set_data_default": True})
        if mockup_data_customer:
            if mockup_data_customer.get("face_verified"):
                data_response = {
                    "responseId": str(uuid.uuid4()),
                    "responseCode": "00",
                    "responseMessage": "Thành công.",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "referenceId": "AB" + str(random.randint(10000000000, 99999999999)) + str(int(time.time())),
                    "dataResponse": '{"eyesOpen":false,"multipleFaces":false,"faceMasked":false,"blurFace":false,"liveness":true,"img2":"iVBORw0KGgoAAAANSUhEUgAAAoAAAAHgCAYAAAA10dzkAAAAAXNSR0IArs4c6QAAIABJREFUeF6svWeQZelVJbquv+kqTWV5b7qq","matching":90,"img1":"/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAx"}',
                    "signature": None,
                }
            else:
                data_response = {
                    "responseId": str(uuid.uuid4()),
                    "responseCode": "1040",
                    "responseMessage": "Không tìm thấy thông tin trong hệ thống.",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }
            return jsonify(data_response), 200
        result_sample = ResultSampleModel().find_one({"path_api": "CheckFace"})
        if result_sample:
            return jsonify(result_sample.get("data")), 200
        return jsonify({"message": "success"}), 200

    def save_customer(self):
        """
        API lưu thông tin
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        # rule_validate = {
        #     "requestId": [Required, InstanceOf(str)],  # Mã định danh request
        #     "channel": [Required, InstanceOf(str)],  # Kênh gọi API
        #     "timestamp": [Required, InstanceOf(str)],  # Thời gian gửi request
        #     "data": [Required, InstanceOf(dict)],  # Object # Kết quả xác thực C06
        #     "signature": [Required, InstanceOf(str)],  # Chuỗi ký sinh ra từ thuật toán RSA với key đc cấp
        # }

        # self.abort_if_validate_error(rule_validate, body_request)
        # data_save = body_request.get("data")
        # rule_validate_data_save = {
        #     "idCard": [Required, InstanceOf(str)],  # Số giấy tờ
        #     "address": [Required, InstanceOf(str)],  # Địa chỉ
        #     "cif": [Required, InstanceOf(str)],  # Số cif KH
        #     "typeCard": [Required, InstanceOf(str)],  # Loại GTTT
        #     "oldIdCardNo": [Required, InstanceOf(str)],  # Số giấy tờ cũ
        #     "name": [Required, InstanceOf(str)],  # Họ và tên trên giấy tờ
        #     "dateOfBirth": [Required, InstanceOf(str)],  # Ngày tháng năm sinh
        #     "gender": [Required, InstanceOf(str)],  # Giới tính
        #     "ethnic": [Required, InstanceOf(str)],  # Dân tộc
        #     "religion": [Required, InstanceOf(str)],  # Tôn giáo
        #     "placeOfOrigin": [Required, InstanceOf(str)],  # Địa chỉ thường trú
        #     "issuePlace": [Required, InstanceOf(str)],  # Nơi cấp
        #     "personalSpecificIdentification": [Required, InstanceOf(str)],  # Đặc điểm nhận dạng
        #     "dateOfIssuance": [Required, InstanceOf(str)],  # Ngày hiệu lực
        #     "dateOfExpiry": [Required, InstanceOf(str)],  # Ngày hết hạn
        #     "motherName": [Required, InstanceOf(str)],  # Họ và tên mẹ
        #     "spouseName": [Required, InstanceOf(str)],  # Họ và tên vợ/chồng
        #     "fatherName": [Required, InstanceOf(str)],  # Họ và tên bố
        #     "nationality": [Required, InstanceOf(str)],  # Quốc tịch
        #     "nfcImage": [Required, InstanceOf(str)],  # Chuỗi Base64 của ảnh KH đọc từ nfc trong CCCD
        #     "faceImage": [Required, InstanceOf(str)],  # Chuỗi Base64 của ảnh KH
        #     "statusC06": [Required, InstanceOf(str)],  # Trạng thái xác thực C06
        #     "resultC06": [Required, InstanceOf(str)],
        # }

        # self.abort_if_validate_error(data_save, rule_validate_data_save)
        result_sample = ResultSampleModel().find_one({"path_api": "SaveCustomer"})
        if result_sample:
            return jsonify(result_sample.get("data")), 200
        return jsonify({"message": "success"}), 200

    def check_customer_exists(self):
        """
        API Kiểm tra khách hàng đã tồn tại trong hệ thống
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        # rule_validate = {
        #     "requestId": [Required, InstanceOf(str)],  # Mã định danh request
        #     "channel": [Required, InstanceOf(str)],  # Kênh gọi API
        #     "timestamp": [Required, InstanceOf(str)],  # Thời gian gửi request
        #     "data": [Required, InstanceOf(dict)],  # Object
        #     "signature": [Required, InstanceOf(str)],  # Chuỗi ký sinh ra từ thuật toán RSA với key đc cấp
        #     "idCardReq"
        # }

        # self.abort_if_validate_error(rule_validate, body_request)

        data_check = body_request.get("data")
        # rule_validate_data_check = {
        #     "idCard": [Required, InstanceOf(str)],  # Số giấy tờ
        #     "name": [Required, InstanceOf(str)],  # Họ và tên trên giấy tờ
        #     "dateOfBirth": [Required, InstanceOf(str)],  # Ngày tháng năm sinh
        #     "gender": [Required, InstanceOf(str)],  # Giới tính
        #     "dateOfIssuance": [Required, InstanceOf(str)],  # Ngày hiệu lực
        # }

        # self.abort_if_validate_error(rule_validate_data_check, data_check)

        id_card_request = body_request.get("idCardRequest")
        if not id_card_request:
            id_card_request = data_check.get("idCard")

        customer_by_id_card = MockupDataCustomerModel().get_customer_by_id_card(id_card_request)
        if not customer_by_id_card:
            customer_by_id_card = MockupDataCustomerModel().get_customer_by_old_id_card_no(id_card_request)
            if not customer_by_id_card or not customer_by_id_card.get("exist_cif_with_id_card"):
                return (
                    jsonify({"code": "00", "desc": "SVC-SUCCESS-00", "data": {"userExists": "false", "data": {}}}),
                    200,
                )
            customer_by_id_card["cif"] = id_card_request

        result_data = {"userExists": "false", "data": {}}
        if customer_by_id_card and customer_by_id_card.get("cif"):
            result_data["userExists"] = "true"
            if "_id" in customer_by_id_card:
                customer_by_id_card.pop("_id")
            data_response = {
                "idCard": customer_by_id_card.get("id_card"),
                "requestId": str(uuid.uuid4()),
                "channelId": None,
                "address": customer_by_id_card.get("address"),
                "cif": customer_by_id_card.get("cif"),
                "email": customer_by_id_card.get("email"),
                "phone": customer_by_id_card.get("phone"),
                "oldCardNo": customer_by_id_card.get("old_id_card"),
                "name": customer_by_id_card.get("name"),
                "dateOfBirth": customer_by_id_card.get("date_of_birth"),
                "gender": customer_by_id_card.get("gender"),
                "ethnic": customer_by_id_card.get("ethnic"),
                "religion": customer_by_id_card.get("religion"),
                "placeOfOrigin": None,
                "personalSpecificIdentification": None,
                "dateOfIssuance": customer_by_id_card.get("date_of_issuance"),
                "dateOfExpiry": customer_by_id_card.get("date_of_expiry"),
                "motherName": customer_by_id_card.get("mother_name"),
                "fatherName": None,
                "spouseName": None,
                "nationality": None,
                "nfcImagePath": "/var/ekyc/ekyc/20240523/95f4428d-e491-4d3g-b96f-26fa91683284/jwfmfv_21d331fc-6862-4741-9be7-ee77b6e240d8_nfc.jpg",
                "nfcImageUrl": "http://10.1.45.97/ekyc/20240523/95f4428d-e491-4d3g-b96f-26fa91683284/jwfmfv_21d331fc-6862-4741-9be7-ee77b6e240d8_nfc.jpg",
                "base64NfcImage": "iVBORw0KGgoAAAANSUhEUgAAAlgAAAMgCAIAAABwAouTAAAFi0lEQVR4nO3BMQEAAADCoPVPbQwfoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC+Bv1bAAGXiLn2AAAAAElFTkSuQmCC",
                "base64FaceImage": "iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAIAAADTED8xAAADMElEQVR4nOzVwQnAIBQFQYXff81RUkQCOyDj1YOPnbXWPmeTRef+/3O/OyBjzh3CD95BfqICMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMK0CMO0TAAD//2Anhf4QtqobAAAAAElFTkSuQmCC",
                "time": "2024-05-23T10:19:24.38",
                "activeProfile": "uat",
                "statusC06": None,
                "resultC06": None,
            }
            result_data["data"] = data_response

        return jsonify({"code": "00", "desc": "SVC-SUCCESS-00", "data": result_data}), 200

    def check_unique_id(self):
        """
        Kiểm tra thông tin giấy tờ đã tồn tại trên hệ thống
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        # rule_validate = {
        #     "requestUUID": [Required, InstanceOf(str)],  # Mã định danh request
        #     "messageDateTime": [Required, InstanceOf(str)],  # Thời gian gửi request (YYYY-MM-DDTHH:MM:SS.000)
        #     "uniqueidType": [Required, InstanceOf(str)],  # Loại giấy tờ
        #     "uniqueId": [Required, InstanceOf(str)],  # Số đăng ký của khách hàng
        #     "name": [Required, InstanceOf(str)],  # Tên đăng ký
        #     "dob": [Required, InstanceOf(str)],  # Ngày sinh
        # }

        # self.abort_if_validate_error(rule_validate, body_request)
        filter_query = {
            "$or": [
                {"id_card": body_request.get("uniqueId").replace("CCUOC_", "")},
                {"old_id_card": body_request.get("uniqueId").replace("CCUOC_", "")},
            ]
        }
        customer = MockupDataCustomerModel().find_one(filter_query)
        if customer and customer.get("cif"):
            return jsonify({"errorCode": "99", "errorDesc": "SUCCESS"}), 200
        return jsonify({"errorCode": "0", "errorDesc": "SUCCESS"}), 200

    def id_check_insert_data_log(self):
        """
        Lưu log vào DB TellerApp
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        # rule_validate = {
        #     "method": [Required, InstanceOf(str)],  # Phương thức gọi (CHECK_CARD / CHECK_FACE)
        #     "request": [Required, InstanceOf(str)],  # giá trị request khi gửi đi
        #     "response": [Required, InstanceOf(str)],  # giá trị response khi nhận về
        #     "error": [Required, InstanceOf(str)],  # Lỗi khi thực hiện gọi hàm
        #     "channel": [Required, InstanceOf(str)],  # Kênh gọi API
        #     "createBy": [Required, InstanceOf(str)],  # người thực hiện
        #     "solId": [Required, InstanceOf(str)],  # chi nhánh thực hiện
        # }

        # self.abort_if_validate_error(rule_validate, body_request)

        return jsonify({"message": "success"}), 200

    def insert_id_check(self):
        """
        Api Insert ID Check vào DB TellerApp
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        # rule_validate = {
        #     "address": [Required, InstanceOf(str)],  # Địa chỉ thường trú
        #     "dateOfBirth": [Required, InstanceOf(str)],  # Ngày sinh (dd/mm/yyyy)
        #     "dateOfExpiry": [Required, InstanceOf(str)],  # Ngày hết hạn (dd/mm/yyyy)
        #     "dateOfIssuance": [InstanceOf(str)],  # Ngày cấp (dd/mm/yyyy)
        #     "deviceType": [Required, InstanceOf(str), In(["EPAY", "TEST", "LIVE"])],  # Loại dịch vụ
        #     "ethnic": [Required, InstanceOf(str)],  # Dân tộc
        #     "fatherName": [InstanceOf(str)],  # Tên cha
        #     "gender": [Required, InstanceOf(str)],  # Giới tính
        #     "idCard": [Required, InstanceOf(str)],  # Số CCCD
        #     "motherName": [InstanceOf(str)],  # Tên mẹ
        #     "name": [Required, InstanceOf(str)],  # Tên
        #     "nationality": [Required, InstanceOf(str)],  # Quốc tịch
        #     "oldIdCardNo": [Required, InstanceOf(str)],  # Số CMND
        #     "personalSpecid": [Required, InstanceOf(str)],  # Đặc điểm nhận dạng
        #     "placeOfOrigin": [Required, InstanceOf(str)],  # Quê quán
        #     "placeOfResidence": [InstanceOf(str)],  # Địa chỉ thường trú
        #     "religion": [InstanceOf(str)],  # Tôn giáo
        #     "spouseName": [InstanceOf(str)],  # Tên vợ/chồng
        #     "userId": [Required, InstanceOf(str)],  # Người tạo
        #     "solId": [Required, InstanceOf(str)],  # Chi nhánh tạo
        #     "channel": [Required, InstanceOf(str)],  # Kênh thực hiện
        #     "cardImage": [InstanceOf(str)],  # hình nfc (String Base64)
        #     "faceImage": [InstanceOf(str)],  # hình liveness (String Base64)
        # }

        # self.abort_if_validate_error(rule_validate, body_request)
        return jsonify({"message": "success"}), 200

    def verify_image(self):
        """
        API Kiểm tra chuẩn ảnh: ISO, Blur,...
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        # rule_validate = {
        #     "requestId": [Required, InstanceOf(str)],  # Mã định danh request
        #     "channel": [Required, InstanceOf(str)],  # Kênh gọi API
        #     "timestamp": [Required, InstanceOf(str)],  # Thời gian gửi request
        #     "data": [Required, InstanceOf(dict)],  # Object
        #     "image": [Required, InstanceOf(str)],  # Chuỗi Base64 của ảnh KH
        #     "signature": [Required, InstanceOf(str)],  # Ký RSA theo checksum SHA256 của ảnh
        # }

        # self.abort_if_validate_error(rule_validate, body_request)
        result_sample = ResultSampleModel().find_one({"path_api": "VerifyImage"})
        if result_sample:
            return jsonify(result_sample.get("data")), 200
        return jsonify({"message": "success"}), 200

    def ca_acct_add_indiv(self):
        """
        API Tạo tài khoản KHCN - CAAcctAddIndiv
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        rule_validate = {
            "RequestUUID": [Required, InstanceOf(str)],  # Mã định danh yêu cầu
            "ChannelId": [Required, InstanceOf(str)],  # Kênh thực hiện
            "MessageDateTime": [Required, InstanceOf(str)],  # Thời gian gửi yêu cầu (YYYY-MM-DDThh:mm:ss.000)
            "CustId": [Required, InstanceOf(str)],  # Số cif
            "SchmCode": [InstanceOf(str)],  # Mã SCHM
            "AcctCurr": [InstanceOf(str)],  # Đơn vị tiền
            "BranchId": [InstanceOf(str)],  # Mã chi nhánh
            "GenLedgerSubHeadCode": [InstanceOf(str)],  # Mã tiểu đề phụ GL mà theo đó Tài khoản được mở
            "AcctName": [InstanceOf(str)],  # Họ Tên
            "AcctShortName": [InstanceOf(str)],  # Tên đệm
            "AcctStmtMode": [InstanceOf(str)],  # Chế độ thanh toán tài khoản
            "Type": [InstanceOf(str)],  # Loại
            "StartDt": [InstanceOf(str)],  # Ngày bắt đầu cho người ký được ủy quyền
            "HolStat": [InstanceOf(str)],  # Giữ chỉ số nguyên bản
            "DespatchMode": [InstanceOf(str)],  # Có in
            "DSAID": [InstanceOf(str)],  # Mã DSA
            "NEXTINTERESTRUNDATECR": [InstanceOf(str)],  # Ngày lãi xuất tiếp theo
            "LOCALCALENDAR": [InstanceOf(str)],  # Có in theo lịch
            "INTCRACC": [InstanceOf(str)],  # Có ghi có
            "FREETEXT1": [InstanceOf(str)],  # Ngày bắt đầu cho thời hạn hiệu lực của mã phí
            "FREETEXT2": [InstanceOf(str)],  # Ngày kết thúc cho thời hạn hiệu lực của mã phí
            "FREETEXT3": [InstanceOf(str)],
            "FREETEXT4": [InstanceOf(str)],
            "FREETEXT8": [InstanceOf(str)],
            "FREECODE4": [InstanceOf(str)],
            "FREECODE8": [InstanceOf(str)],  # FREECODE8 (Có Y/N)
            "FREECODE9": [InstanceOf(str)],
            "INTRATECODE": [InstanceOf(str)],  # Mã hàng
            "ACCTPREFINTCR": [InstanceOf(str)],  # Phần trăm lãi suất ưu đãi
            "INTCRACCNUM": [InstanceOf(str)],
            "ACCTMGRID": [InstanceOf(str)],  # ID người dùng của người quản lý tài khoản
            "MINBALIND": [InstanceOf(str)],  # Mã số dư tối thiểu
            "MINBAL": [InstanceOf(str)],  # Số dư tối thiểu
            "PrefLangCode": [InstanceOf(str)],  # Mã ngôn ngữ
            "relation": [InstanceOf(str)],  # Mối quan hệ
            "title": [InstanceOf(str)],  # Lời chào (MR...)
            "userName": [InstanceOf(str)],  # Tên user
            "addrLine1": [InstanceOf(str)],  # Địa chỉ
            "notes": [InstanceOf(str)],  # Chú ý
            "phone": [InstanceOf(str)],  # Số điện thoại
            "email": [InstanceOf(str)],  # mail
            "MINBALCRNCY": [InstanceOf(str)],  # Loại tiền
            "LuckyAccNum": [InstanceOf(str)],  # Tài khoản số đẹp
            "LienFeeAmount": [InstanceOf(str)],  # Số tiền phí
        }

        CustIdRequest = body_request.get("CustId")
        customer = MockupDataCustomerModel().find_one({"cifCore": CustIdRequest})
        if not customer:
            return jsonify({"AcctId": None, "ErrorCode": "1", "ErrorDesc": "Customer not found"}), 400

        if customer.get("fail_when_create_account_indiv"):
            return jsonify({"AcctId": None, "ErrorCode": "1", "ErrorDesc": "Fail when create account indiv"}), 400

        self.abort_if_validate_error(rule_validate, body_request)
        return jsonify({"AcctId": "*********", "ErrorCode": "0", "ErrorDesc": "SUCCESS"}), 200

    def check_cif_for_lien(self):
        """
        Hàm truy vấn nợ phí của tài khoản số đẹp - CheckCIFForLien
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        rule_validate = {
            # All fields are mandatory (Man = M)
            "RequestUUID": [Required, InstanceOf(str)],  # Mã định danh yêu cầu
            "ChannelId": [Required, InstanceOf(str)],  # Kênh thực hiện
            "MessageDateTime": [Required, InstanceOf(str)],  # Thời gian gửi yêu cầu (YYYY-MM-DDThh:mm:ss.000)
            "CIFId": [Required, InstanceOf(str)],  # Số cif
        }

        self.abort_if_validate_error(rule_validate, body_request)
        return jsonify({"message": "success"}), 200

    def check_so_dep(self):
        """
        Hàm trả thông tin tài khoản số đẹp (full tài khoản) - CheckSoDep
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        rule_validate = {
            # Mandatory fields (Man = M)
            "RequestUUID": [Required, InstanceOf(str)],  # Mã định danh yêu cầu
            "ChannelId": [Required, InstanceOf(str)],  # Kênh thực hiện
            "MessageDateTime": [Required, InstanceOf(str)],  # Thời gian gửi yêu cầu (YYYY-MM-DDThh:mm:ss.000)
            "FORACID": [Required, InstanceOf(str)],  # Full số tài khoản số đẹp
            # Optional fields
            "CIFID": [InstanceOf(str)],  # Số cif (Mặc định là KHCN)
        }

        self.abort_if_validate_error(rule_validate, body_request)
        return (
            jsonify(
                {
                    "MaLoai": "N100000KHCN",
                    "LoaiSo": "10 So khong dep",
                    "Amount": "500000",
                    "ErrorCode": "0",
                    "ErrorDesc": "SUCCESS",
                }
            ),
            200,
        )

    def _generate_special_account_types(self, num, loai1, loai2, count=6):
        digit_counts = ["03", "04", "05"]  # Only these as requested
        # Amount based on digit count
        amount_mapping = {"03": "500000", "04": "2000000", "05": "4000000"}

        # Number names in Vietnamese
        number_names = {"03": "3", "04": "4", "05": "5"}

        # Select a subset of type codes to use (just num1 and num2)
        selected_type_codes = [loai1, loai2]

        results = []
        for digit_count in digit_counts:
            for type_code in selected_type_codes:

                maloai = f"Y{num}{digit_count}{type_code}KHCN"
                tenloai = f"{number_names[digit_count]} {self.type_descriptions[type_code]}"
                sotien = amount_mapping[digit_count]

                results.append({"MALOAI": maloai, "TENLOAI": tenloai, "SOTIEN": sotien})

        return results

    def get_loai_details(self):
        """
        Hàm trả danh sách mã loại số đẹp và phí - GetLoaiDetails
        Loại số đẹp :
            - 01: số giống nhau,
            - 02: số phát lộc 6,8.
            - 03: số phát tài 39,79
            - 04: số tiến.
            - 05: số lục, ngũ, tứ, tam hoa.
            - 06: số phát lộc 368, 386.
            - 07: số lặp
            - 08: số soi gương.
            - 10: số ngũ, tứ, tam hoa nhụy giữa.
            - 11: số ngũ, tứ, tam hoa không nhụy.
            - 12: Số ngũ, tứtứ, tam hoa bằng nhau.
            - 13: Số ngũ, tứtứ, tam hoa khác nhau.
            - 14: Số lưu ý đặc biệt.
            - 15: Số tiến cặp
            - 19: số hỗn hợp
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        get_all_loai_1 = ["01", "02", "03", "04", "05", "06", "07", "08", "10", "11", "12", "13", "14", "15", "19"]
        rule_validate = {
            "RequestUUID": [Required, InstanceOf(str)],
            "ChannelId": [Required, InstanceOf(str)],
            "MessageDateTime": [Required, InstanceOf(str)],
            "NUM": [Required, InstanceOf(str)],
            "LOAI1": [
                Required,
                InstanceOf(str),
                In(get_all_loai_1),
            ],
            "LOAI2": [
                Required,
                InstanceOf(str),
                In(get_all_loai_1),
            ],
        }

        self.abort_if_validate_error(rule_validate, body_request)

        num = body_request.get("NUM")
        loai1 = body_request.get("LOAI1")
        loai2 = body_request.get("LOAI2")

        return (
            jsonify(
                {
                    "Number": "6",
                    "AcctDetails": self._generate_special_account_types(num, loai1, loai2),
                    "ErrorCode": "0",
                    "ErrorDesc": "SUCCESS",
                }
            ),
            200,
        )

    def generate_special_numbers(self, length, type_code, search_pattern=None, count=20, lst_account_number_exist=None):
        """
        Generate special account numbers based on the type_code from MALOAI

        Args:
            length (int): Length of the account number (8, 9, 10, or 12)
            type_code (str): Type code from MALOAI (01, 02, 03, etc.)
            search_pattern (str): Optional pattern user is looking for
            count (int): Number of results to return

        Returns:
            list: List of tuples (account_number, pattern_description, weight, type_code)
        """
        results = []
        generated = set()  # To avoid duplicates

        # Handle search pattern
        fixed_prefix = ""
        fixed_suffix = ""
        if search_pattern and search_pattern.strip():
            if len(search_pattern) <= length:
                fixed_suffix = search_pattern
            else:
                # If pattern is too long, just use the first 'length' characters
                fixed_suffix = search_pattern[:length]

        for _ in range(count * 3):  # Generate more than needed, will filter later

            count_generate = 3
            gen_account_number = False
            while count_generate > 0:
                if len(results) >= count:
                    break

                # Generate base account number
                account_number = ""

                # Generate number based on type_code
                if type_code == "01":  # số giống nhau
                    # Numbers with repeating digits
                    digit = random.choice("**********")
                    repeat_length = min(
                        length - len(fixed_prefix) - len(fixed_suffix), random.randint(3, 6)
                    )  # At least 3 repeating, up to 6
                    repeated_part = digit * repeat_length
                    padding_length = length - len(fixed_prefix) - len(fixed_suffix) - len(repeated_part)
                    padding = "".join(random.choices("**********", k=padding_length))
                    account_number = fixed_prefix + padding + repeated_part + fixed_suffix

                elif type_code == "02":  # số phát lộc 6,8
                    # Numbers with many 6s and 8s
                    lucky_digits = "68"
                    lucky_count = min(
                        length - len(fixed_prefix) - len(fixed_suffix), random.randint(4, 6)
                    )  # 4-6 lucky digits
                    lucky_part = "".join(random.choices(lucky_digits, k=lucky_count))
                    padding_length = length - len(fixed_prefix) - len(fixed_suffix) - len(lucky_part)
                    padding = "".join(random.choices("**********", k=padding_length))
                    account_number = fixed_prefix + padding + lucky_part + fixed_suffix

                elif type_code == "03":  # số phát tài 39,79
                    # Numbers with prosperity patterns 39, 79
                    prosperity_patterns = ["39", "79"]
                    pattern = random.choice(prosperity_patterns)
                    repeat_count = min(
                        (length - len(fixed_prefix) - len(fixed_suffix)) // 2, random.randint(1, 3)
                    )  # 1-3 prosperity patterns
                    prosperity_part = pattern * repeat_count
                    padding_length = length - len(fixed_prefix) - len(fixed_suffix) - len(prosperity_part)
                    padding = "".join(random.choices("**********", k=padding_length))
                    account_number = fixed_prefix + padding + prosperity_part + fixed_suffix

                elif type_code == "04":  # số tiến
                    # Sequential numbers
                    start_digit = random.randint(0, 9 - min(6, length - len(fixed_prefix) - len(fixed_suffix)))
                    seq_length = min(
                        length - len(fixed_prefix) - len(fixed_suffix), random.randint(4, 6)
                    )  # 4-6 sequential digits
                    sequential_part = "".join(str((start_digit + i) % 10) for i in range(seq_length))
                    padding_length = length - len(fixed_prefix) - len(fixed_suffix) - len(sequential_part)
                    padding = "".join(random.choices("**********", k=padding_length))
                    account_number = fixed_prefix + padding + sequential_part + fixed_suffix

                elif type_code == "05":  # số lục, ngũ, tứ, tam hoa
                    # Numbers with 3-6 identical digits in a row
                    digit = random.choice("**********")
                    repeat_length = random.choice([3, 4, 5, 6])  # tam/tứ/ngũ/lục hoa
                    repeat_length = min(repeat_length, length - len(fixed_prefix) - len(fixed_suffix))
                    repeated_part = digit * repeat_length
                    padding_length = length - len(fixed_prefix) - len(fixed_suffix) - len(repeated_part)
                    padding = "".join(random.choices("**********", k=padding_length))
                    account_number = fixed_prefix + padding + repeated_part + fixed_suffix

                elif type_code == "06":  # số phát lộc 368, 386
                    # Numbers with lucky patterns 368, 386
                    lucky_patterns = ["368", "386"]
                    pattern = random.choice(lucky_patterns)
                    pattern_count = min((length - len(fixed_prefix) - len(fixed_suffix)) // 3, 2)
                    lucky_part = pattern * pattern_count
                    padding_length = length - len(fixed_prefix) - len(fixed_suffix) - len(lucky_part)
                    padding = "".join(random.choices("**********", k=padding_length))
                    account_number = fixed_prefix + padding + lucky_part + fixed_suffix

                elif type_code == "07":  # số lặp
                    # Repeating patterns
                    pattern_length = random.choice([2, 3])
                    if length - len(fixed_prefix) - len(fixed_suffix) >= pattern_length * 2:
                        pattern = "".join(random.choices("**********", k=pattern_length))
                        repeat_count = (length - len(fixed_prefix) - len(fixed_suffix)) // pattern_length
                        repeated_part = pattern * repeat_count
                        padding_length = length - len(fixed_prefix) - len(fixed_suffix) - len(repeated_part)
                        padding = "".join(random.choices("**********", k=padding_length))
                        account_number = fixed_prefix + padding + repeated_part + fixed_suffix
                    else:
                        # Fallback if not enough space for repeating pattern
                        account_number = (
                            fixed_prefix
                            + "".join(random.choices("**********", k=length - len(fixed_prefix) - len(fixed_suffix)))
                            + fixed_suffix
                        )

                elif type_code == "08":  # số soi gương
                    # Mirror numbers
                    if length - len(fixed_prefix) - len(fixed_suffix) >= 4:
                        half_length = (length - len(fixed_prefix) - len(fixed_suffix)) // 2
                        half = "".join(random.choices("**********", k=half_length))
                        mirrored = half + half[::-1]
                        padding_length = length - len(fixed_prefix) - len(fixed_suffix) - len(mirrored)
                        padding = "".join(random.choices("**********", k=padding_length))
                        account_number = fixed_prefix + padding + mirrored + fixed_suffix
                    else:
                        # Fallback if not enough space for mirror pattern
                        account_number = (
                            fixed_prefix
                            + "".join(random.choices("**********", k=length - len(fixed_prefix) - len(fixed_suffix)))
                            + fixed_suffix
                        )

                else:
                    # Default for other type codes or fallback
                    account_number = (
                        fixed_prefix
                        + "".join(random.choices("**********", k=length - len(fixed_prefix) - len(fixed_suffix)))
                        + fixed_suffix
                    )
                if account_number not in lst_account_number_exist:
                    gen_account_number = True
                    break
                count_generate -= 1
            if gen_account_number:
                # Ensure exact length
                if len(account_number) > length:
                    account_number = account_number[:length]
                elif len(account_number) < length:
                    account_number = account_number.ljust(length, random.choice("**********"))

                # Skip if already generated
                if account_number in generated:
                    continue

                generated.add(account_number)

                # Calculate a weight/score based on how "special" the number is
                # This is arbitrary and can be customized based on preferences
                weight = random.randint(50, 100)

                # Get description for this pattern
                pattern_description = self.type_descriptions.get(type_code, "Số đẹp")

                results.append((account_number, pattern_description, weight, type_code))

            # Sort by weight (higher is better)
            results.sort(key=lambda x: x[2], reverse=True)

            # Return top 'count' results
        return results[:count]

    def get_danh_sach_so_dep(self):
        """
        API trả về danh sách tài khoản số đẹp (tối đa 20 số).
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)

        rule_validate = {
            "RequestUUID": [Required, InstanceOf(str)],
            "ChannelId": [Required, InstanceOf(str)],
            "MessageDateTime": [Required, InstanceOf(str)],
            "NUM": [Required, InstanceOf(str), In(["08", "09", "10", "12"])],
            "LOAI": [InstanceOf(str)],
            "STKCHON": [Required, InstanceOf(str)],
        }

        self.abort_if_validate_error(rule_validate, body_request)

        # Parse parameters
        num = body_request.get("NUM")
        num_length = int(num)
        loai = body_request.get("LOAI", "")  # MALOAI format (Y{num}{digit_count}{type_code}KHCN)
        stkchon = body_request.get("STKCHON", "")

        lst_account_number_exist = MockupLockAccountModel().distinct("account_number", {})

        # Extract type details from MALOAI if available
        type_code = "01"  # Default to "số giống nhau"
        digit_count = "03"  # Default to 3 digits

        if loai:
            if len(loai) >= 9 and loai.startswith("Y"):
                try:
                    # Extract digit count and type code from MALOAI
                    digit_count = loai[3:5]  # Positions 3-4 (03, 04, 05)
                    type_code = loai[5:7]  # Positions 5-6 (01, 02, etc.)
                except:
                    pass  # Use defaults if parsing fails
            else:
                type_code = loai

        # Generate special account numbers based on num_length and type_code
        special_numbers = self.generate_special_numbers(
            length=num_length,
            type_code=type_code,
            search_pattern=stkchon if stkchon != "None" else None,
            lst_account_number_exist=lst_account_number_exist,
        )

        # Get sotien based on digit_count
        amount_mapping = {"03": "500000", "04": "2000000", "05": "4000000"}
        sotien = amount_mapping.get(digit_count, "500000")

        # Prepare response
        acct_details = [
            {
                "STK": num,
                "MALOAI": loai if loai else f"Y{num}{digit_count}{type_code}KHCN",
                "TENLOAI": desc,  # Use description from generate_special_numbers
                "SOTIEN": sotien,
            }
            for num, desc, _, _ in special_numbers
        ]

        response = {
            "Number": str(len(acct_details)),
            "AcctDetails": acct_details,
            "ErrorCode": "00",
            "ErrorDesc": "Success",
        }

        # if not acct_details:
        #     response["ErrorCode"] = "01"
        #     response["ErrorDesc"] = "Không tìm thấy tài khoản phù hợp"

        return jsonify(response), 200

    def decrypt_otp(self, encryption_key_str: str, encrypted_b64_str: str) -> str:
        """
        Giải mã một chuỗi Base64 đã được mã hóa bằng TripleDES (3DES) ECB với PKCS7 padding.

        LƯU Ý: Phải sử dụng cùng một khóa và phương pháp tạo khóa như khi mã hóa.
        """
        try:
            key_bytes = encryption_key_str.encode("utf-8")
            encrypted_bytes = base64.b64decode(encrypted_b64_str)

            # --- BẮT BUỘC: Áp dụng cùng logic tạo khóa như trong hàm encrypt_otp ---
            # Kiểm tra và điều chỉnh độ dài khóa (phải khớp với logic mã hóa)
            if len(key_bytes) not in (16, 24):
                # Nếu hàm mã hóa đã hash key, hàm giải mã cũng phải hash key
                print("Cảnh báo: Độ dài khóa gốc không hợp lệ. Sử dụng SHA-256 hash (24 byte) giống như khi mã hóa.")
                key_bytes = sha256(key_bytes).digest()[:24]
                # Nếu hàm mã hóa báo lỗi thay vì hash, bạn cũng nên báo lỗi ở đây
                # raise ValueError("Độ dài khóa phải là 16 hoặc 24 byte cho 3DES.")

            # Khởi tạo cipher với cùng chế độ và khóa
            cipher = DES3.new(key_bytes, DES3.MODE_ECB)

            # Giải mã dữ liệu
            decrypted_padded_bytes = cipher.decrypt(encrypted_bytes)

            # Loại bỏ PKCS7 padding
            decrypted_bytes = unpad(decrypted_padded_bytes, DES3.block_size, style="pkcs7")

            # Chuyển đổi byte đã giải mã (và bỏ padding) thành chuỗi UTF-8
            return decrypted_bytes.decode("utf-8")

        except (ValueError, KeyError) as e:
            # Lỗi có thể xảy ra do Base64 không hợp lệ, padding sai, hoặc khóa sai
            print(f"Lỗi khi giải mã OTP: Dữ liệu không hợp lệ hoặc khóa sai. Chi tiết: {e}")
            raise  # Hoặc trả về None/chuỗi rỗng tùy theo yêu cầu xử lý lỗi
        except Exception as e:
            print(f"Lỗi không xác định khi giải mã OTP: {e}")
            raise

    def send_otp(self):
        """
        Hàm gửi tin nhắn OTP
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        send_sms_request_dto = body_request.get("SendSMSRequestDto")
        data_encrypted = send_sms_request_dto.get("DataEncrypted")
        config_send_sms = ConfigInfoApiModel().get_config_info_api_not_merchant_id(
            ThirdPartyType.SEND_REQUEST_QUICK_SALES_SEND_OTP_CONFIRM_LANDING_PAGE
        )

        access_token = config_send_sms.get(ConstantKeyConfigSendThirdParty.SMS_ACCESS_TOKEN) or ""

        # MobioLogging().info("EibSender::data:: %s" % plaintext)
        ciphertext = ThirdPartyEIB.decrypt(access_token, data_encrypted)
        # Parse XML và lấy thông tin cần thiết
        from xml.etree import ElementTree as ET

        root = ET.fromstring(ciphertext)

        phone_number = root.find("PhoneNumber").text
        otp = root.find("OTP").text
        if otp:
            otp = self.decrypt_otp(
                config_send_sms.get(ConstantKeyConfigSendThirdParty.SMS_LANDINGPAGE_ENCRYPTION_KEY), otp
            )
        message = root.find("Message").text.format(OTP=otp)
        self._send_notify_group_otp_lark_bot(message, phone_number, otp, ConstantTypeSendOTP.SMS_OTP_EYKC)
        return jsonify({"ResponseCode": "00", "ResponseDesc": "Success"}), 200

    def lan_lock_unlock(self):
        """
        Query tài khoản trạng thái lock/unlock; Lock tài khoản số đẹp; Unlock tài khoản số đẹp
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        rule_validate = {
            # Required fields (Man = Y)
            "RequestUUID": [Required, InstanceOf(str)],  # Mã định danh thông điệp yêu cầu
            "ChannelId": [Required, InstanceOf(str)],  # Kênh thực hiện
            "MessageDateTime": [Required, InstanceOf(str)],  # Thời gian gửi yêu cầu (YYYY-MM-DDThh:mm:ss.000)
            "Function": [
                Required,
                InstanceOf(str),
                In(["A", "U", "I", "NOT_USE"]),
            ],  # Chức năng (A: Lock, U: Unlock, I: Query)
            "SolId": [Required, InstanceOf(str)],  # Mã chi nhánh của tài khoản
            "AccountNumber": [Required, InstanceOf(str)],  # Số tài khoản
            "SchemeCode": [Required, InstanceOf(str)],  # SchemeCode
            # Optional fields (Man = N)
            "CIFId": [InstanceOf(str)],  # Số Cif
            "HOVerifyFlag": [InstanceOf(str)],  # HO Verified flag for the LAN
        }

        self.abort_if_validate_error(rule_validate, body_request)

        account_number = body_request.get("AccountNumber")
        function = body_request.get("Function")

        # Query existing account status
        existing_account = MockupLockAccountModel().find_one({"account_number": account_number})

        if function == "A":  # Lock
            if existing_account and existing_account.get("status") in ["A", "NOT_USE"]:
                return (
                    jsonify(
                        {
                            "ErrorCode": "01",
                            "ErrorDesc": "Tài khoản này không thể sử dụng do đang bị khóa hoặc không được phép sử dụng",
                        }
                    ),
                    200,
                )

            # Create or update account status to Locked
            MockupLockAccountModel().insert_document(
                {
                    "status": "A",
                    "sol_id": body_request.get("SolId"),
                    "scheme_code": body_request.get("SchemeCode"),
                    "cif_id": body_request.get("CIFId"),
                    "ho_verify_flag": body_request.get("HOVerifyFlag"),
                    "updated_at": datetime.now(),
                    "account_number": account_number,
                }
            )
            return jsonify({"ErrorCode": "00", "ErrorDesc": "Khóa tài khoản thành công"}), 200

        elif function == "U":  # Unlock
            if not existing_account:
                return jsonify({"ErrorCode": "02", "message": "Không tìm thấy thông tin tài khoản"}), 200

            # Update account status to Unlocked
            MockupLockAccountModel().update_one_query(
                {"account_number": account_number},
                {
                    "status": "U",
                    "updated_at": datetime.now(),
                },
            )
            return jsonify({"ErrorCode": "00", "ErrorDesc": "Mở khóa tài khoản thành công"}), 200

        elif function == "I":  # Query
            if not existing_account:
                return (
                    jsonify({"ErrorCode": "02", "ErrorDesc": "Không tìm thấy thông tin tài khoản", "data": None}),
                    200,
                )

            return (
                jsonify(
                    {
                        "ErrorCode": "00",
                        "ErrorDesc": "Thành công",
                        "AcctDtls": {
                            "AccountNumber": existing_account.get("account_number"),
                            "Status": existing_account.get("status"),
                            "SolId": existing_account.get("sol_id"),
                            "SchemeCode": existing_account.get("scheme_code"),
                            "CIFId": existing_account.get("cif_id"),
                            "HOVerifiedFlag": existing_account.get("ho_verify_flag"),
                            "UpdatedAt": existing_account.get("updated_at"),
                        },
                    }
                ),
                200,
            )

        return jsonify({"ErrorCode": "00", "ErrorDesc": "Thành công"}), 200

    def check_aml(self):
        """
        CheckAML
        """
        body_request = request.get_json()
        MobioLogging().info("body_request: %s", body_request)
        rule_validate = {
            # Required fields (Man = M)
            "FORMAT_TYPE": [Required, InstanceOf(str)],  # Loại lệnh (FIX:FT)
            "F_BRANCH": [Required, InstanceOf(str)],  # Chi nhánh
            "F_TRX_REF": [Required, InstanceOf(str)],  # Mã giao dịch
            "F_TRX_TYPE": [Required, InstanceOf(str)],  # Loại giao dịch (FIX:FTO)
            "F_CURRENCY": [Required, InstanceOf(str)],  # Loại tiền tệ
            "F_AMOUNT": [Required, InstanceOf(str)],  # Số tiền giao dịch
            "F_ISSUER_NAME": [],  # Tên người gửi
            "F_ISSUER_ID": [Required, InstanceOf(str)],  # CMND người gửi
            "F_ISSUER_COUNTRY": [Required, InstanceOf(str)],  # Quốc gia gửi
            "F_FROM_BANK": [Required, InstanceOf(str)],  # Ngân hàng gửi
            "F_TO_BANK": [Required, InstanceOf(str)],  # Ngân hàng nhận
            "F_BEN_NAME": [Required, InstanceOf(str)],  # Tên người nhận
            "F_BEN_ID": [Required, InstanceOf(str)],  # CMND người nhận
            "F_BEN_CITY": [Required, InstanceOf(str)],  # Thành phố
            "F_ISSUER_CATEGORY": [InstanceOf(str)],  # Danh mục
            "F_ISSUER_ADDRESS": [InstanceOf(str)],  # Địa chỉ người gửi
            "F_ISSUER_CITY": [InstanceOf(str)],  # Thành phố
            "F_BEN_CATEGORY": [InstanceOf(str)],  # Danh mục
            "F_BEN_ADDRESS": [InstanceOf(str)],  # Địa chỉ người nhận
            "F_BEN_COUNTRY": [InstanceOf(str)],  # Quốc gia nhận
        }

        self.abort_if_validate_error(rule_validate, body_request)
        id_card = body_request.get("F_ISSUER_ID")
        mockup_data_customer = MockupDataCustomerModel().find_one({"id_card": id_card})
        if mockup_data_customer:
            if mockup_data_customer.get("check_aml"):
                return jsonify({"ResultMsg": "!OK", "DETECTIONID": ""}), 200
            else:
                return jsonify({"ResultMsg": "OK", "DETECTIONID": ""}), 200

        return jsonify({"ResultMsg": "OK", "DETECTIONID": ""}), 200

    def insert_in4_dsa(self):
        return jsonify({"Status": "SUCCESS", "ErrorCode": "0", "ErrorDesc": "SUCCESS"}), 200

    def view_workflow_tree(self):
        """
        Retrieve and display workflow data in a tree structure

        Returns:
            flask.Response: HTML response with workflow tree visualization
        """
        # Get object_id from request parameters, if provided
        object_id = request.args.get("object_id")

        if object_id:
            # Find logs where object_id matches the provided value
            log_quick_sales = list(
                LogCustomerFullFlowQuickSalesModel().find(
                    {
                        "$or": [
                            {"object_id": object_id},
                            {"form_data_submit.profile.profile_identify.identify_value": object_id},
                        ]
                    }
                )
            )
            if not log_quick_sales:
                # If no results found with the object_id, return a message
                return render_template(
                    "mockup_data/log_flow.html",
                    log_quick_sales=[],
                    search_message=f"No data found for object_id: {object_id}",
                )

            # Process each log to adjust timestamps
            for log in log_quick_sales:
                staff_id = log.get("staff_id")
                merchant_id = log.get("merchant_id")
                staff_info = InternalAdminHelper().get_account_detail_info(merchant_id, staff_id)
                if staff_info:
                    log["staff_name"] = staff_info.get("username")

                # Adjust created_time for UTC+7
                if log.get("created_time"):
                    log["created_time"] = log["created_time"] + timedelta(hours=7)

                # Adjust action_time in steps
                for step_key, step_data in log.items():
                    if step_key.startswith("step_") and isinstance(step_data, dict):
                        if step_data.get("action_time"):
                            step_data["action_time"] = step_data["action_time"] + timedelta(hours=7)
        else:
            log_quick_sales = []

        # Render the template with the workflow data
        return render_template("mockup_data/log_flow.html", log_quick_sales=log_quick_sales)

    def handle_customer_form(self):
        """Handle the customer form GET (view) and POST (save) requests"""
        from flask import request

        # Get data from request
        search_query = request.args.get("search", None)
        if request.method == HTTP.METHOD.POST.upper():
            request_data = request.form.to_dict()
        else:
            request_data = None

        try:
            message = None
            success = False

            # Handle POST request (form submission)
            if request_data:
                # Get form data
                customer_data = request_data.copy()
                customer_id = customer_data.pop("customer_id", None)

                # Check if creating new or updating existing
                if customer_id:
                    # Update existing customer
                    success, message = self.update_customer(customer_id, customer_data)
                else:
                    # Create new customer
                    success, message = self.create_customer(customer_data)

            # Get customers for display in the table (with search if provided)
            customers = self.get_customers(search_query)

            # Return data for the template
            return {"customers": customers, "message": message, "success": success}

        except Exception as ex:
            return {"customers": [], "message": str(ex), "success": False}

    def handle_delete_customer(self, customer_id=None):
        """Handle customer deletion and return redirect URL"""
        try:
            if not customer_id:
                # Try to get from request if not passed as parameter
                customer_id = request.form.get("customer_id")

            if not customer_id:
                return False, "No customer ID provided"

            success, message = self.delete_customer(customer_id)
            return success, message

        except Exception as ex:
            return False, str(ex)

    def get_customers(self, search_query=None):
        """Get list of customers with optional search filter"""
        try:
            from src.models.mongo.mockup_data.mockup_data_customer_model import (
                MockupDataCustomerModel,
            )

            # Get customers from database
            customers = MockupDataCustomerModel().get_customers(search_query)

            # Format customers for display
            formatted_customers = []
            for customer in customers:
                customer_id = str(customer.get("_id"))
                formatted_customer = {
                    "id": customer_id,
                    "name": customer.get("name"),
                    "id_card": customer.get("id_card"),
                    "cif": customer.get("cif"),
                    "dob": customer.get("dob"),
                    "date_of_issue": customer.get("date_of_issue"),
                    "gender": customer.get("gender"),
                    "origin": customer.get("origin"),
                    "ethnicity": customer.get("ethnicity"),
                    "face_verified": customer.get("face_verified", False),
                    "id_verified": customer.get("id_verified", False),
                    "check_card": customer.get("check_card", False),
                    "check_aml": customer.get("check_aml", False),
                    "father_name": customer.get("father_name", ""),
                    "mother_name": customer.get("mother_name", ""),
                    "address": customer.get("address", ""),
                    "old_id_card": customer.get("old_id_card", ""),
                    "exist_cif_with_id_card": customer.get("exist_cif_with_id_card", False),
                    "set_data_default": customer.get("set_data_default", False),
                    "fail_when_create_cif": customer.get("fail_when_create_cif", False),
                    "fail_when_create_account_indiv": customer.get("fail_when_create_account_indiv", False),
                    "fail_when_create_account_edigi": customer.get("fail_when_create_account_edigi", False),
                }
                formatted_customers.append(formatted_customer)

            return formatted_customers

        except Exception as ex:
            MobioLogging().error("Error getting customers: %s", ex)
            return []

    def create_customer(self, customer_data):
        """Create a new customer"""
        try:
            from src.models.mongo.mockup_data.mockup_data_customer_model import (
                MockupDataCustomerModel,
            )

            # Create customer in database
            result, message = MockupDataCustomerModel().create_customer(customer_data)

            if result:
                return True, message
            return False, message

        except Exception as ex:
            return False, str(ex)

    def update_customer(self, customer_id, customer_data):
        """Update an existing customer"""
        try:
            from src.models.mongo.mockup_data.mockup_data_customer_model import (
                MockupDataCustomerModel,
            )

            # Update customer in database
            result, message = MockupDataCustomerModel().update_customer(customer_id, customer_data)

            return result, message

        except Exception as ex:
            return False, str(ex)

    def delete_customer(self, customer_id):
        """Delete a customer"""
        try:
            from src.models.mongo.mockup_data.mockup_data_customer_model import (
                MockupDataCustomerModel,
            )

            # Delete customer from database
            result, message = MockupDataCustomerModel().delete_customer(customer_id)

            return result, message

        except Exception as ex:
            return False, str(ex)

    def get_customers_api_response(self):
        # Get search query from request
        search_query = request.args.get("search", None)

        # Call the method to get customers
        customers = self.get_customers(search_query)

        # Return as JSON response
        return self.send_response(customers, HTTP.STATUS.OK, "Success")

    def get_log_detail(self, log_id):
        from bson import ObjectId
        from bson.errors import InvalidId

        if not log_id:
            # Đảm bảo phản hồi là JSON với Content-Type đúng
            from flask import jsonify, make_response

            response = make_response(jsonify({"success": False, "error": "Log ID is required"}))
            response.headers["Content-Type"] = "application/json"
            return response

        try:
            # Convert log_id to ObjectId
            try:
                log_obj_id = ObjectId(log_id)
            except InvalidId:
                # Xử lý trường hợp log_id không phải là ObjectId hợp lệ
                from flask import jsonify, make_response

                response = make_response(jsonify({"success": False, "error": f"Invalid ObjectId format: {log_id}"}))
                response.headers["Content-Type"] = "application/json"
                return response

            # Get log detail from model
            log_model = LogRequestSendThirdPartyModel()
            log_detail = log_model.find_by_id(log_obj_id)

            if not log_detail:
                # Đảm bảo phản hồi là JSON với Content-Type đúng
                from flask import jsonify, make_response

                response = make_response(jsonify({"success": False, "error": f"Log not found for ID: {log_id}"}))
                response.headers["Content-Type"] = "application/json"
                return response

            # Get info_request
            info_request = log_detail.get("info_request", {})
            info_response = log_detail.get("info_response", {})
            action_time = log_detail.get("action_time", "")
            config = log_detail.get("config", {})

            headers = info_request.get("headers", {})
            payload = info_request.get("payload", {})
            config_uri = config["uri"]

            non_header_keys = [
                "uri",
                "mapping_code_message_error",
                "mapping_code_message_success",
                "timeout_api",
                "application_request",
                "api_key",
            ]

            # Combine headers: headers from "headers" section and additional headers from "config"
            all_headers = headers.copy()
            for key, value in config.items():
                if key not in non_header_keys:
                    all_headers[key] = value

            # Build curl command with line breaks for readability
            curl_command = f'curl -X POST "{config_uri}" \\\n'
            for key, value in all_headers.items():
                curl_command += f'  -H "{key}: {value}" \\\n'
            # Add payload
            payload_json = json.dumps(payload)
            escaped_payload = shlex.quote(payload_json)
            curl_command += f"  -d {escaped_payload} \\\n"
            # Add timeout
            curl_command += " -k"

            if action_time:
                action_time = action_time.strftime("%Y-%m-%d %H:%M:%S")
            if info_response:
                try:
                    info_response = json.loads(info_response)
                except Exception as e:
                    MobioLogging().error("Error parsing info_response: %s", e)

            # Prepare data for response
            result = {
                "headers": headers,
                "payload": payload,
                "response": info_response,
                "type": log_detail.get("type", ""),
                "time_request": log_detail.get("time_request", ""),
                "action_time": action_time,
                "config": config,
                "curl_command": curl_command,
            }

            # Đảm bảo phản hồi là JSON với Content-Type đúng
            from flask import jsonify, make_response

            response = make_response(jsonify({"success": True, "data": result}))
            response.headers["Content-Type"] = "application/json"
            return response

        except Exception as e:
            response = make_response(jsonify({"success": False, "error": str(e)}))
            response.headers["Content-Type"] = "application/json"
            return response


if __name__ == "__main__":
    MockupDataController().send_notify()
