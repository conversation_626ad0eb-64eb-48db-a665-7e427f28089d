#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 12/04/2024
"""

import datetime
import json
import os
import uuid

from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import (
    SHARE_FOLDER_IMAGE_VERIFY,
    ConstantMessageSendRequestToThirdParty,
    ConstantNameFlow,
    ThirdPartyType,
)
from src.common.handle_headers import get_merchant_header, get_param_value_temp
from src.message_queue.kafka.producer.push_message_to_topic import Producer


class ConstantBodyIdentificationCard:
    DATA_DECRYPTION = "data_decryption"
    SDK_REQUEST_ROUND = "sdk_request_round"
    SDK_REQUEST_SESSION = "sdk_request_session"
    REQUEST_TIMESTAMP = "request_timestamp"
    SDK_REQUEST_ID = "sdk_request_id"


class ConstantBodyCheckFace:
    DATA_DECRYPTION = "data_decryption"
    SDK_REQUEST_ROUND = "sdk_request_round"
    SDK_REQUEST_SESSION = "sdk_request_session"
    SDK_REQUEST_ID = "sdk_request_id"
    RAW_IMG_1 = "raw_img_1"
    RAW_IMG_2 = "raw_img_2"
    REQUEST_TIMESTAMP = "request_timestamp"
    IMAGE_VERIFY = "image_verify"


class DecryptionController(BaseController):

    def _validate_face_check(self, data_validate):
        rule_validate = {
            ConstantBodyCheckFace.DATA_DECRYPTION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.SDK_REQUEST_ID: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.DATA_DECRYPTION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.SDK_REQUEST_ROUND: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.SDK_REQUEST_SESSION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.RAW_IMG_1: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.RAW_IMG_2: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.REQUEST_TIMESTAMP: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.IMAGE_VERIFY: [Required, InstanceOf(str), Length(1)],
        }

        self.abort_if_validate_error(rule_validate, data_validate)

    def face_check(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        flow_name = request.args.get("flow_name", ConstantNameFlow.TELLER_APP)
        if flow_name not in ConstantNameFlow.get_all_attribute():
            raise CustomError("Flow name {} not supported".format(flow_name))
        session_id = request.args.get("session")
        MobioLogging().info(
            "DecryptionController :: face_check :: merchant_id: %s, account_id: %s" % (merchant_id, account_id)
        )
        body = request.get_json()
        self._validate_face_check(data_validate=body)

        log_id = str(uuid.uuid1())
        MobioLogging().info("DecryptionController :: face_check :: log_id: %s" % (log_id))
        action_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")

        image_verify = body.pop(ConstantBodyCheckFace.IMAGE_VERIFY)
        raw_img_1 = body.pop(ConstantBodyCheckFace.RAW_IMG_1)
        raw_img_2 = body.pop(ConstantBodyCheckFace.RAW_IMG_2)
        # save image verify to file json
        path_file_image_verify = os.path.join(SHARE_FOLDER_IMAGE_VERIFY, f"image_verify_{log_id}.json")
        with open(path_file_image_verify, "w") as f:
            json.dump({"image_verify": image_verify, "raw_img_1": raw_img_1, "raw_img_2": raw_img_2}, f)

        data_send = {
            ConstantMessageSendRequestToThirdParty.LOG_ID: log_id,
            ConstantMessageSendRequestToThirdParty.LOG_ID_START: session_id,
            ConstantMessageSendRequestToThirdParty.FLOW_NAME: flow_name,
            ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body,
            ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_CHECK_FACE,
        }
        Producer().push_message_to_request_third_party(
            merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
        )
        return {"data": {"session_id": session_id, "log_id": log_id}}

    def _validate_identification_card(self, data_validate):
        rule_validate = {
            ConstantBodyIdentificationCard.DATA_DECRYPTION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyIdentificationCard.SDK_REQUEST_ID: [Required, InstanceOf(str), Length(1)],
            ConstantBodyIdentificationCard.SDK_REQUEST_ROUND: [Required, InstanceOf(str), Length(1)],
            ConstantBodyIdentificationCard.SDK_REQUEST_SESSION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyIdentificationCard.REQUEST_TIMESTAMP: [Required, InstanceOf(str), Length(1)],
        }

        self.abort_if_validate_error(rule_validate, data_validate)

    def identification_card(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        flow_name = request.args.get("flow_name", ConstantNameFlow.TELLER_APP)
        if flow_name not in ConstantNameFlow.get_all_attribute():
            raise CustomError("Flow name {} not supported".format(flow_name))
        session_id = request.args.get("session")
        MobioLogging().info(
            "DecryptionController :: identification_card :: merchant_id: %s, account_id: %s" % (merchant_id, account_id)
        )
        body = request.get_json()
        self._validate_identification_card(body)
        log_id = str(uuid.uuid1())
        action_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        MobioLogging().info("DecryptionController :: face_check :: log_id: %s" % (log_id))
        data_send = {
            ConstantMessageSendRequestToThirdParty.LOG_ID: log_id,
            ConstantMessageSendRequestToThirdParty.LOG_ID_START: session_id,
            ConstantMessageSendRequestToThirdParty.FLOW_NAME: flow_name,
            ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body,
            ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_READ_CARD,
        }
        Producer().push_message_to_request_third_party(
            merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
        )
        return {"data": {"session_id": session_id, "log_id": log_id}}
