#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 13/02/2025
"""

import datetime
import json
import os
import uuid

from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Length, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import (
    SHARE_FOLDER_IMAGE_VERIFY,
    ConstantMessageSendRequestToThirdParty,
    ConstantNameFlow,
    StatusCode,
    ThirdPartyType,
)
from src.common.handle_headers import get_merchant_header, get_param_value_temp
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.helpers.thirdparty.eib.send_request_check_card import SendRequestCheckCard
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.helpers.thirdparty.quick_sales.retry_form_quick_sales import (
    RetryFormQuickSales,
)
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_pre_approved_model import LogPreApprovedModel
from src.models.mongo.log_request_card_model import LogRequestCardModel
from src.models.mongo.log_request_check_cif_model import LogRequestCheckCifModel


class ConstantBodyIdentificationCard:
    DATA_DECRYPTION = "data_decryption"
    SDK_REQUEST_ROUND = "sdk_request_round"
    SDK_REQUEST_SESSION = "sdk_request_session"
    REQUEST_TIMESTAMP = "request_timestamp"
    SDK_REQUEST_ID = "sdk_request_id"


class ConstantBodyCheckFace:
    DATA_DECRYPTION = "data_decryption"
    SDK_REQUEST_ROUND = "sdk_request_round"
    SDK_REQUEST_SESSION = "sdk_request_session"
    SDK_REQUEST_ID = "sdk_request_id"
    RAW_IMG_1 = "raw_img_1"
    RAW_IMG_2 = "raw_img_2"
    REQUEST_TIMESTAMP = "request_timestamp"
    IMAGE_VERIFY = "image_verify"


class QuickSalesController(BaseController):

    def _validate_check_cif(self, body_request):
        rule_validate = {
            "profile_name": [],
            "phone_number": [Required, Length(1, 100), InstanceOf(str)],
            "identify": [Required, Length(1, 100), InstanceOf(dict)],
        }
        self.abort_if_validate_error(rule_validate, body_request)
        data_identify = body_request.get("identify")
        rule_validate_data_identify = {
            "identify_type": [Required, Length(1, 100), InstanceOf(str), In(["citizen_identity"])],
            "identify_value": [Required, Length(1, 100), InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_validate_data_identify, data_identify)

    def check_cif(self):

        session_id = request.args.get("session") if request.args.get("session") else str(uuid.uuid4())
        account_id = get_param_value_temp("id")
        username = get_param_value_temp("username")
        merchant_id = get_merchant_header()
        body_request = request.get_json()

        self._validate_check_cif(body_request)
        try:
            result_check_aml, result_check_cif, response_request = QuickSalesHelper().check_cif_by_information_profile(
                session_id,
                merchant_id,
                body_request.get("profile_name"),
                body_request.get("phone_number"),
                body_request.get("identify"),
            )
        except Exception as e:
            raise CustomError(str(e))
        filter_option = {
            "merchant_id": merchant_id,
            "session_id": session_id,
        }
        log_request_card_check_cif = LogRequestCheckCifModel().find_one(filter_option)
        if not log_request_card_check_cif:
            LogRequestCheckCifModel().insert_document(
                {
                    "merchant_id": merchant_id,
                    "account_id": account_id,
                    "username": username,
                    "session_id": session_id,
                    "body_request": body_request,
                    "result": {"result_check_cif": result_check_cif, "result_check_aml": result_check_aml},
                    "created_at": datetime.datetime.now(datetime.UTC),
                }
            )
        else:
            LogRequestCheckCifModel().update_one_query(
                filter_option,
                {
                    "result": {"result_check_cif": result_check_cif, "result_check_aml": result_check_aml},
                    "body_request": body_request,
                },
            )

        # Get thông tin check customer exist
        status_update_log = LogRequestCardModel().update_one_query(
            {"merchant_id": merchant_id, "log_id_request": session_id},
            {"dataResponseCheckCustomerExist": response_request},
        )
        MobioLogging().info(f"status_update_log: {status_update_log}")

        return {
            "data": {
                "is_exist": result_check_cif,
                "is_aml": result_check_aml,
                "session_id": session_id,
            }
        }

    def _validate_lock_unlock_account(self, body_request):
        rule_validate = {
            "account_number": [Required, Length(1, 100), InstanceOf(str)],
            "action": [Required, Length(1, 100), InstanceOf(str), In(["lock", "unlock"])],
        }
        self.abort_if_validate_error(rule_validate, body_request)

    def lock_unlock_account(self):
        merchant_id = get_merchant_header()
        account_action_id = get_param_value_temp("id")
        body_request = request.get_json()

        self._validate_lock_unlock_account(body_request)

        data_response, status_code, reasons, log_id = QuickSalesHelper().lock_unlock_account(
            merchant_id, account_action_id, body_request.get("account_number"), body_request.get("action")
        )
        MobioLogging().info(f"data_response: {data_response}, status_code: {status_code}, reasons: {reasons}")

        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            raise CustomError(reasons)

        return

    def _validate_premium_account_types(self, body_request):
        pass

    def premium_account_types(self):
        merchant_id = get_merchant_header()
        config_param_quick_sales = ConfigInfoApiModel().find_one(
            {"merchant_id": merchant_id, "type": ThirdPartyType.CONFIG_PARAM_QUICK_SALES_PREMIUM_ACCOUNT}
        )
        if not config_param_quick_sales:
            raise CustomError("Không tìm thấy config param quick sales premium account")
        premium_account_types = config_param_quick_sales.get("premium_account_types")

        return {"data": premium_account_types}

    def _validate_premium_number_types_fee(self, body_request):
        rule_validate = {
            "premium_number_type_code": [Required, Length(1, 100), InstanceOf(str)],
            "premium_account_length_code": [Required, Length(1, 100), InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_validate, body_request)

    def premium_number_types_fee(self):
        merchant_id = get_merchant_header()
        body_request = request.get_json()
        staff_id = get_param_value_temp("id")

        self._validate_premium_number_types_fee(body_request)

        premium_number_type_code_1 = body_request.get("premium_number_type_code")
        premium_number_type_code_2 = body_request.get("premium_number_type_code")
        premium_account_length_code = body_request.get("premium_account_length_code")

        data_get_loai_details = {
            "num": premium_account_length_code,
            "loai1": premium_number_type_code_1,
            "loai2": premium_number_type_code_2,
        }

        data_response, status_code, reasons, log_request_id = QuickSalesHelper().get_loai_details(
            merchant_id, staff_id, data_get_loai_details
        )
        MobioLogging().info(
            f"data_response: {data_response}, status_code: {status_code}, reasons: {reasons}, log_request_id: {log_request_id}"
        )

        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            raise CustomError(reasons)

        return data_response

    def premium_number_types(self):
        merchant_id = get_merchant_header()

        config_param_quick_sales = ConfigInfoApiModel().find_one(
            {"merchant_id": merchant_id, "type": ThirdPartyType.CONFIG_PARAM_QUICK_SALES_PREMIUM_ACCOUNT}
        )
        if not config_param_quick_sales:
            raise CustomError("Không tìm thấy config param quick sales premium account")

        premium_number_types = config_param_quick_sales.get("premium_number_types")

        return {"data": premium_number_types}

    def _validate_filter_premium_account(self, body_request):
        rule_validate = {
            "premium_account_length_code": [Required, Length(1, 100), InstanceOf(str)],
            "premium_account_type_code": [],
            "number_customer_select": [],
        }
        self.abort_if_validate_error(rule_validate, body_request)

    def filter_premium_account(self):
        merchant_id = get_merchant_header()
        body_request = request.get_json()
        staff_id = get_param_value_temp("id")

        self._validate_filter_premium_account(body_request)

        premium_account_length_code = body_request.get("premium_account_length_code")
        premium_account_type_code = body_request.get("premium_account_type_code") or ""
        number_customer_select = body_request.get("number_customer_select") or ""

        config_param_quick_sales = ConfigInfoApiModel().find_one(
            {"merchant_id": merchant_id, "type": ThirdPartyType.CONFIG_PARAM_QUICK_SALES_PREMIUM_ACCOUNT}
        )
        if not config_param_quick_sales:
            raise CustomError("Không tìm thấy config param quick sales premium account")

        premium_number_types = config_param_quick_sales.get("premium_number_types")
        lst_code_premium_number_types = [item.get("code") for item in premium_number_types]
        if premium_account_type_code and premium_account_type_code in lst_code_premium_number_types:
            # Call API 4.17 lấy chi tiết loại tài khoản
            data_get_loai_details = {
                "num": premium_account_length_code,
                "loai1": premium_account_type_code,
                "loai2": premium_account_type_code,
            }

            data_response, status_code, reasons, log_request_id = QuickSalesHelper().get_loai_details(
                merchant_id, staff_id, data_get_loai_details
            )
            MobioLogging().info(
                f"filter_premium_account :: get_loai_details :: data_response: {data_response}, status_code: {status_code}, reasons: {reasons}, log_request_id: {log_request_id}"
            )
            if status_code == StatusCode.SUCCESS:
                data_get_loai_details = data_response.get("data", [])
                data_get_loai_detail = data_get_loai_details[0] if data_get_loai_details else {}
                if data_get_loai_detail:
                    premium_account_type_code = data_get_loai_detail.get("premium_number_type_code")

        data_filter_premium_account = {
            "num": premium_account_length_code,
            "loai": premium_account_type_code,
            "search": number_customer_select,
        }
        MobioLogging().info(f"data_filter_premium_account: {data_filter_premium_account}")

        data_response, status_code, reasons, log_request_id = QuickSalesHelper().get_danh_sach_so_dep(
            merchant_id, data_filter_premium_account
        )
        MobioLogging().info(
            f"data_response: {data_response}, status_code: {status_code}, reasons: {reasons}, log_request_id: {log_request_id}"
        )

        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            raise CustomError(reasons)

        return data_response

    def _validate_filter_account_number_by_type(self, body):
        rule_validate = {
            "type": [Required, InstanceOf(str), Length(1), In(["citizen_identity", "phone_number"])],
            "value": [Required, InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rule_validate, body)

    def filter_account_number_by_type(self):
        merchant_id = get_merchant_header()
        body_request = request.get_json()

        self._validate_filter_account_number_by_type(body_request)

        type = body_request.get("type")
        value = body_request.get("value")

        premium_account_length_code = "09"

        if type == "citizen_identity":
            premium_account_length_code = "12"
        elif type == "phone_number":
            premium_account_length_code = "12"
            if value.startswith("+"):
                value = value[1:]
            if value.startswith("84"):
                value = "0" + value[2:]

        data_filter_premium_account = {
            "num": premium_account_length_code,
            "loai": type,
            "value": value,
        }

        data_response, status_code, reasons, log_request_id = QuickSalesHelper().check_so_dep(
            merchant_id, data_filter_premium_account
        )
        MobioLogging().info(
            f"data_response: {data_response}, status_code: {status_code}, reasons: {reasons}, log_request_id: {log_request_id}"
        )

        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            raise CustomError(reasons)

        return data_response

    def premium_account_lengths(self):
        merchant_id = get_merchant_header()
        config_param_quick_sales = ConfigInfoApiModel().find_one(
            {"merchant_id": merchant_id, "type": ThirdPartyType.CONFIG_PARAM_QUICK_SALES_PREMIUM_ACCOUNT}
        )
        if not config_param_quick_sales:
            raise CustomError("Không tìm thấy config param quick sales premium account")

        return {"data": config_param_quick_sales.get("account_lengths")}

    def _validate_face_recognition_verify(self, body_request):
        rule_validate = {
            ConstantBodyCheckFace.DATA_DECRYPTION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.SDK_REQUEST_ID: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.DATA_DECRYPTION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.SDK_REQUEST_ROUND: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.SDK_REQUEST_SESSION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.RAW_IMG_1: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.RAW_IMG_2: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.REQUEST_TIMESTAMP: [Required, InstanceOf(str), Length(1)],
            ConstantBodyCheckFace.IMAGE_VERIFY: [Required, InstanceOf(str), Length(1)],
        }

        self.abort_if_validate_error(rule_validate, body_request)

    def face_recognition_verify(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        flow_name = request.args.get("flow_name", ConstantNameFlow.QUICK_SALES)
        if flow_name not in ConstantNameFlow.get_all_attribute():
            raise CustomError("Flow name {} not supported".format(flow_name))
        session_id = request.args.get("session")
        MobioLogging().info(
            "QuickSalesController :: face_recognition_verify :: merchant_id: %s, account_id: %s"
            % (merchant_id, account_id)
        )
        body = request.get_json()
        self._validate_face_recognition_verify(body_request=body)

        log_id = str(uuid.uuid1())
        MobioLogging().info("QuickSalesController :: face_recognition_verify :: log_id: %s" % (log_id))
        action_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")

        image_verify = body.pop(ConstantBodyCheckFace.IMAGE_VERIFY)
        raw_img_1 = body.pop(ConstantBodyCheckFace.RAW_IMG_1)
        raw_img_2 = body.pop(ConstantBodyCheckFace.RAW_IMG_2)
        # save image verify to file json
        path_file_image_verify = os.path.join(SHARE_FOLDER_IMAGE_VERIFY, f"image_verify_{log_id}.json")
        with open(path_file_image_verify, "w") as f:
            json.dump({"image_verify": image_verify, "raw_img_1": raw_img_1, "raw_img_2": raw_img_2}, f)

        data_send = {
            ConstantMessageSendRequestToThirdParty.LOG_ID: log_id,
            ConstantMessageSendRequestToThirdParty.LOG_ID_START: session_id,
            ConstantMessageSendRequestToThirdParty.FLOW_NAME: flow_name,
            ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body,
            ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_CHECK_FACE,
        }
        Producer().push_message_to_request_check_face(
            merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
        )
        return {"data": {"session_id": session_id, "log_id": log_id}}

    def _validate_citizen_ids_read_only_information(self, body_request):
        rule_validate = {
            ConstantBodyIdentificationCard.DATA_DECRYPTION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyIdentificationCard.SDK_REQUEST_ID: [Required, InstanceOf(str), Length(1)],
            ConstantBodyIdentificationCard.SDK_REQUEST_ROUND: [Required, InstanceOf(str), Length(1)],
            ConstantBodyIdentificationCard.SDK_REQUEST_SESSION: [Required, InstanceOf(str), Length(1)],
            ConstantBodyIdentificationCard.REQUEST_TIMESTAMP: [Required, InstanceOf(str), Length(1)],
        }

        self.abort_if_validate_error(rule_validate, body_request)

    def citizen_ids_read_only_information(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        flow_name = request.args.get("flow_name", ConstantNameFlow.QUICK_SALES)
        if flow_name not in ConstantNameFlow.get_all_attribute():
            raise CustomError("Flow name {} not supported".format(flow_name))
        session_id = request.args.get("session")
        MobioLogging().info(
            "QuickSalesController :: citizen_ids_read_only_information :: merchant_id: %s, account_id: %s"
            % (merchant_id, account_id)
        )
        body = request.get_json()
        self._validate_citizen_ids_read_only_information(body)
        log_id = str(uuid.uuid1())
        action_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        MobioLogging().info("QuickSalesController :: citizen_ids_read_only_information :: log_id: %s" % (log_id))
        data_send = {
            ConstantMessageSendRequestToThirdParty.LOG_ID: log_id,
            ConstantMessageSendRequestToThirdParty.LOG_ID_START: session_id,
            ConstantMessageSendRequestToThirdParty.FLOW_NAME: flow_name,
            ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body,
            ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_READ_CARD,
        }
        
        if flow_name == ConstantNameFlow.QUICK_SALES:
            log_request_cif = LogRequestCheckCifModel().find_by_log_request_id(merchant_id, session_id)
            if not log_request_cif:
                raise CustomError("Session request check not found!!")

            log_request_cif_id = log_request_cif.get("_id")

            LogRequestCheckCifModel().update_one_query({"_id": log_request_cif_id}, {"request_body_read_card": body})
        elif flow_name == ConstantNameFlow.PRE_APPROVED:
            log_pre_approved = LogPreApprovedModel().insert_document(
                {
                    "merchant_id": merchant_id,
                    "log_id": log_id,
                    "request_body_read_card": body,
                    "session_id": session_id if session_id else log_id,
                    "created_at": action_time,
                    "updated_at": action_time,
                }
            )

        Producer().push_message_to_request_citizen_ids_read_only_information(
            merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
        )

        return {"data": {"session_id": session_id, "log_id": log_id}}

    def _validate_citizen_ids_validate(self, body_request):
        pass

    def citizen_ids_validate(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        flow_name = request.args.get("flow_name", ConstantNameFlow.QUICK_SALES)
        if flow_name not in ConstantNameFlow.get_all_attribute():
            raise CustomError("Flow name {} not supported".format(flow_name))
        session_id = request.args.get("session")
        MobioLogging().info(
            "QuickSalesController :: citizen_ids_validate :: merchant_id: %s, account_id: %s"
            % (merchant_id, account_id)
        )

        if flow_name == ConstantNameFlow.QUICK_SALES:
            log_request_cif = LogRequestCheckCifModel().find_by_log_request_id(merchant_id, session_id)
            if not log_request_cif:
                raise CustomError("Session request check not found!!")
            request_body_read_card = log_request_cif.get("request_body_read_card")
            if not request_body_read_card:
                raise CustomError("Request body read card not found!!")
        elif flow_name == ConstantNameFlow.PRE_APPROVED:
            log_pre_approved = LogPreApprovedModel().find_by_log_request_id(merchant_id, session_id)
            if not log_pre_approved:
                raise CustomError("Session request pre approved not found!!")
            request_body_read_card = log_pre_approved.get("request_body_read_card")
            if not request_body_read_card:
                raise CustomError("Request body read card not found!!")
        log_id = str(uuid.uuid1())
        data, config, sdk_request_id = SendRequestCheckCard._build_config_send_check_card(
            merchant_id, request_body_read_card
        )
        data_response, status_code, reasons = ThirdPartyEIB.check_card(
            log_id=log_id,
            config=config,
            data=data,
            merchant_id=merchant_id,
            account_id=account_id,
            log_id_start=session_id,
            sdk_request_id=sdk_request_id,
        )
        MobioLogging().info(
            "QuickSalesController :: citizen_ids_validate :: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        try:
            data_response_json = json.loads(data_response.get("dataResponse"))
        except Exception as ex:
            MobioLogging().error("QuickSalesController :: citizen_ids_validate :: error: {}".format(ex))
            data_response_json = {}
        resultCheckCard = data_response_json.get("result", False)
        data_result = {"is_auth_c06": resultCheckCard}

        return {"data": data_result}

    def _validate_quick_sales_retry(self, body_request):
        rule_validate = {
            "form_id": [Required, InstanceOf(str), Length(1)],
            "object_id": [Required, InstanceOf(str), Length(1)],
            "object_type": [Required, InstanceOf(str), Length(1), In(["profile"])],
        }
        self.abort_if_validate_error(rule_validate, body_request)

    def quick_sales_retry(self):
        merchant_id = get_merchant_header()
        account_id = get_param_value_temp("id")
        username = get_param_value_temp("username")

        MobioLogging().info(
            "QuickSalesController :: start quick_sales_retry :: merchant_id: %s, account_id: %s, username: %s"
            % (merchant_id, account_id, username)
        )
        flow_name = request.args.get("flow_name", ConstantNameFlow.QUICK_SALES)
        if flow_name not in ConstantNameFlow.get_all_attribute():
            raise CustomError("Flow name {} not supported".format(flow_name))
        body_request = request.get_json()
        self._validate_quick_sales_retry(body_request)

        form_id = body_request.get("form_id")
        object_id = body_request.get("object_id")
        object_type = body_request.get("object_type")

        RetryFormQuickSales.send_request_retry_form(merchant_id, account_id, form_id, object_id, object_type)

        return
