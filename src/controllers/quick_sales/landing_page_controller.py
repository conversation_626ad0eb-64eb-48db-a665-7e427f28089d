#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 20/02/2025
"""

import copy
import datetime
import random

from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import In, InstanceOf, Required
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from configs.kafka_config import KAFKA_TOPIC
from src.apis import UnprocessableEntityError
from src.common import (
    ConstantKeyConfigMappingFieldCrmEib,
    ConstantQuickSales,
    StatusCode,
)
from src.helpers.internal.mobio.form_builder import <PERSON><PERSON>uilderHelper
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_mapping_field_crm_to_eib import (
    ConfigMappingFieldCrmToEibModel,
)
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)
from src.models.mongo.log_otp_confirm_landing_page import LogOtpConfirmLandingPageModel


class LandingPageController(BaseController):

    def _validate_landing_page_otp(self, body_request):
        rule_validate = {
            "form_token": [Required, InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_validate, body_request)

    def _gen_otp(self):
        return "".join([str(random.randint(0, 9)) for _ in range(6)])

    def landing_page_otp(self):
        body_request = request.get_json()
        self._validate_landing_page_otp(body_request)

        form_token = body_request.get("form_token")

        """
        Call sang form builder để lấy thông tin số điền thoại để gửi OTP
        """

        status_form, data_form = FormBuilderHelper().get_detail_form_builder_by_form_token(form_token)

        if status_form == ConstantQuickSales.StatusFormBuilder.INACTIVE:
            raise CustomError("Form is inactive")

        phone_number = data_form.get("phone_number")
        merchant_id = data_form.get("merchant_id")
        profile_name = data_form.get("profile_name")
        otp = self._gen_otp()

        _, status_send_otp, reasons = QuickSalesHelper().send_otp_confirm_landing_page(
            merchant_id, phone_number, profile_name, otp
        )

        if status_send_otp == StatusCode.SUCCESS:
            # Trong trường hợp OTP được gửi thành công, thì cần check xem form_id đã có OTP nếu có rồi thì chuyển nó sang hết hạn.
            filter_otp = {
                "merchant_id": merchant_id,
                "phone_number": phone_number,
                "form_id": data_form.get("form_id"),
                "status": ConstantQuickSales.ConstantStatusLogOtpConfirmLandingPage.INIT,
                "profile_id": data_form.get("profile_id"),
            }
            status_update_otp_expired = LogOtpConfirmLandingPageModel().update_status_by_filter(
                filter_otp, ConstantQuickSales.ConstantStatusLogOtpConfirmLandingPage.EXPIRED
            )
            MobioLogging().info(
                "landing_page_otp :: update_otp_expired :: status_update_otp_expired: %s", status_update_otp_expired
            )

            result = LogOtpConfirmLandingPageModel().create_log(
                merchant_id,
                phone_number,
                profile_name,
                otp,
                data_form.get("form_id"),
                data_form.get("profile_id"),
                form_token,
            )
            MobioLogging().info("landing_page_otp :: create_log_otp_confirm_landing_page :: result: %s", result)
            return {"status": "success", "message": "OTP sent successfully"}
        raise CustomError(reasons)

    def _validate_landing_page_otp_verify(self, body_request):
        rule_validate = {
            "form_token": [Required, InstanceOf(str)],
            "otp": [Required, InstanceOf(str)],
        }
        self.abort_if_validate_error(rule_validate, body_request)

    def landing_page_otp_verify(self):
        body_request = request.get_json()
        self._validate_landing_page_otp_verify(body_request)

        form_token = body_request.get("form_token")
        otp = body_request.get("otp")

        """
        Call sang form builder để lấy thông tin số điền thoại để gửi OTP
        """

        status_form, data_form = FormBuilderHelper().get_detail_form_builder_by_form_token(form_token)

        if status_form == ConstantQuickSales.StatusFormBuilder.INACTIVE:
            raise CustomError("Form is inactive")

        phone_number = data_form.get("phone_number")
        merchant_id = data_form.get("merchant_id")

        filter_otp = {
            "merchant_id": merchant_id,
            "phone_number": phone_number,
            "otp": otp,
            "status": ConstantQuickSales.ConstantStatusLogOtpConfirmLandingPage.INIT,
            "form_id": data_form.get("form_id"),
            "profile_id": data_form.get("profile_id"),
            "form_token": form_token,
            "time_expired": {"$gte": datetime.datetime.now(datetime.UTC)},
        }
        MobioLogging().info("landing_page_otp_verify :: filter_otp: %s", filter_otp)

        result = LogOtpConfirmLandingPageModel().get_otp_latest(filter_otp)
        if not result:
            raise UnprocessableEntityError("Mã OTP không đúng, vui lòng thử lại.")
        otp_id = result.get("_id")

        status_verify = LogOtpConfirmLandingPageModel().update_status_by_otp_id(
            otp_id, ConstantQuickSales.ConstantStatusLogOtpConfirmLandingPage.VERIFY_OTP
        )
        MobioLogging().info("landing_page_otp_verify :: verify_otp :: status_verify: %s", status_verify)
        return {"status": "success", "message": "OTP verified successfully"}

    def _validate_landing_page_submit(self, data_request):

        rule_validate = {
            "form_token": [Required, InstanceOf(str)],
            "status": [
                Required,
                InstanceOf(str),
                In(ConstantQuickSales.ConstantStatusSubmitLandingPage.get_all_attribute()),
            ],
        }

        status = data_request.get("status")
        if status == ConstantQuickSales.ConstantStatusSubmitLandingPage.CONFIRM:
            rule_validate.update({"otp": [Required, InstanceOf(str)]})
        elif status == ConstantQuickSales.ConstantStatusSubmitLandingPage.CANCEL:
            rule_validate.update({"reason": [Required, InstanceOf(str)]})

        self.abort_if_validate_error(rule_validate, data_request)

    def landing_page_submit(self):
        body_request = request.get_json()
        self._validate_landing_page_submit(body_request)
        action_time_submit_form = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S.%f")

        form_token = body_request.get("form_token")
        status = body_request.get("status")
        status_form, data_form = FormBuilderHelper().get_detail_form_builder_by_form_token(form_token)
        if status_form == ConstantQuickSales.StatusFormBuilder.INACTIVE:
            raise CustomError("Form is inactive")

        merchant_id = data_form.get("merchant_id")
        form_id = data_form.get("form_id")
        profile_id = data_form.get("profile_id")
        staff_id = data_form.get("staff_id")

        # field config
        field_configs = ConfigMappingFieldCrmToEibModel().get_config_mapping_field_crm_to_eib_quick_sales(merchant_id)
        if not field_configs:
            raise CustomError("Field configs not found")

        field_configs = field_configs.get(ConstantKeyConfigMappingFieldCrmEib.LIST_FIELD_GET_FROM_CRM, [])
        form_data_submit = FormBuilderHelper().get_form_data_submit(
            merchant_id,
            form_id,
            profile_id,
            field_configs,
        )
        MobioLogging().info("landing_page_submit :: form_data_submit: %s", form_data_submit)

        profile_data = form_data_submit.get(ConstantQuickSales.ObjectType.PROFILE, {})
        if not profile_data:
            raise CustomError("Thông tin khách hàng không hợp lệ! Do thiếu dữ liệu khách hàng trong dữ liệu form.")

        phone_number = data_form.get("phone_number")

        if status == ConstantQuickSales.ConstantStatusSubmitLandingPage.CONFIRM:
            otp = body_request.get("otp")

            """
            Call sang form builder để lấy thông tin số điền thoại để gửi OTP
            """

            if status_form == ConstantQuickSales.StatusFormBuilder.INACTIVE:
                raise CustomError("Form is inactive")

            filter_otp = {
                "merchant_id": merchant_id,
                "phone_number": phone_number,
                "otp": otp,
                "status": ConstantQuickSales.ConstantStatusLogOtpConfirmLandingPage.VERIFY_OTP,
                "form_id": data_form.get("form_id"),
                "profile_id": data_form.get("profile_id"),
                "form_token": form_token,
            }

            result = LogOtpConfirmLandingPageModel().get_otp_latest(filter_otp)
            if not result:
                raise UnprocessableEntityError("OTP is incorrect")
            otp_id = result.get("_id")

            status_submit = LogOtpConfirmLandingPageModel().update_status_by_otp_id(
                otp_id, ConstantQuickSales.ConstantStatusLogOtpConfirmLandingPage.SUBMIT
            )
            MobioLogging().info("landing_page_submit :: submit :: status_submit: %s", status_submit)

        # get detail log customer full flow
        log_customer_full_flow = (
            LogCustomerFullFlowQuickSalesModel().get_log_customer_full_flow_quick_sales_by_object_id(
                merchant_id, profile_id, ConstantQuickSales.ObjectType.PROFILE, form_id
            )
        )
        if not log_customer_full_flow:
            """Lưu thông tin submit vào mongo"""
            log_customer_full_flow_id = self._save_log_customer_submit_form(
                merchant_id,
                form_id,
                profile_id,
                staff_id,
                form_data_submit,
                status,
                body_request.get("reason"),
            )
        else:
            MobioLogging().info("landing_page_submit :: upsert :: log_customer_full_flow")
            log_customer_full_flow_id = log_customer_full_flow.get("_id")
            previous_data_submit_form = log_customer_full_flow.get("form_data_submit")
            number_retry = previous_data_submit_form.get("number_retry", 0)
            number_retry += 1
            previous_data_submit_form.update({"number_retry": number_retry})

            data_update_form_submit = copy.deepcopy(previous_data_submit_form)
            data_update_form_submit.update(form_data_submit)

            status_update = LogCustomerFullFlowQuickSalesModel().update_by_set(
                {"_id": log_customer_full_flow_id},
                {
                    "previous_data_submit_form": previous_data_submit_form,
                    "number_retry": number_retry,
                    "form_data_submit": data_update_form_submit,
                },
            )
            MobioLogging().info("landing_page_submit :: upsert :: status_update: %s", status_update)
            form_data_submit = data_update_form_submit

        """
        PUSH thông tin submit vào topic kafka
        """
        data_push = {
            "merchant_id": merchant_id,
            "phone_number": phone_number,
            "profile_id": profile_id,
            "form_id": form_id,
            "form_data_submit": form_data_submit,
            "status": body_request.get("status"),
            "reason": body_request.get("reason"),
            "action_time": action_time_submit_form,
            "staff_id": staff_id,
            "log_customer_full_flow_id": str(log_customer_full_flow_id),
        }

        Producer().send_message_to_topic(KAFKA_TOPIC.QUICK_SALES_SUBMIT_LP, data_push)

        return

    @classmethod
    def _save_log_customer_submit_form(
        cls, merchant_id, form_id, profile_id, staff_id, form_data_submit, status, reason
    ):
        result_step_submit_form = {
            "status": status,
            "reason": reason,
        }
        log_customer_full_flow = LogCustomerFullFlowQuickSalesModel().init_log_customer_full_flow_quick_sales(
            merchant_id,
            staff_id,
            profile_id,
            ConstantQuickSales.ObjectType.PROFILE,
            form_id,
            result_step_submit_form,
            form_data_submit,
        )
        MobioLogging().info(
            "landing_page_submit :: save_log_customer_submit_form :: log_customer_full_flow: %s", log_customer_full_flow
        )
        return log_customer_full_flow
