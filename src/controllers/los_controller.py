#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/11/2024
"""
import datetime
import uuid

from flask import request
from mobio.libs.logging import MobioLogging
from mobio.libs.validator import Each, In, InstanceOf, Length, Required
from mobio.sdks.admin import MobioAdminSDK
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from src.common import ConstantInitializationTime, StatusCode
from src.common.handle_headers import get_merchant_header
from src.helpers.thirdparty.los.call import ThirdPartyLosHelper
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.los_config_model import LosConfigModel


class LosController(BaseController):

    def get_list_status(self):
        merchant_id = get_merchant_header()
        result = LosConfigModel().get_los_config_status(merchant_id)
        for item in result:
            item["title"] = item["title"].get(self.language, item["title"].get("vi"))
            if item.get("status") != "active":
                continue
        return {"data": result}

    def _validate_get_los_by_filter(self, data):
        rule_validate = {
            "assignee_id": [Required, InstanceOf(str), Length(1)],
            "sol_id": [Required, InstanceOf(str), Length(1)],
            "username": [Required, InstanceOf(str), Length(1)],
            "initialization_time": [Required, InstanceOf(dict), Length(1)],
            "list_status": [],
            "customer_type": [],
            "cif": [],
            "no_cccd": [],
            "tax_code": [],
        }
        self.validate_optional_err(rule_validate, data)

    def get_los_by_filter(self):
        log_id = str(uuid.uuid4())
        merchant_id = get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token("id")
        action_time = datetime.datetime.now(datetime.UTC)
        body = request.get_json()

        config_custom = ConfigInfoApiModel().get_config_info_custom_information_send_hpt(merchant_id)

        self._validate_get_los_by_filter(data=body)
        MobioLogging().info("{} :: LosController :: get_los_by_filter :: body :: {}".format(log_id, body))
        condition_query_hpt = {
            "sol": body.get("sol_id"),
            "cif": body.get("cif"),
            "cccd": body.get("no_cccd"),
            "mst": body.get("tax_code"),
            "LoaiKH": body.get("customer_type"),
        }
        list_status = body.get("list_status")
        if list_status:
            list_status = ",".join(list_status)
            condition_query_hpt["TrangThai"] = list_status
        username = body.get("username")
        if username:
            username = self._convert_username_send_hpt(config_custom, username)
            condition_query_hpt["username"] = username
        initialization_time = body.get("initialization_time")
        initialization_time = self._convert_initialization_time_send_hpt(initialization_time)
        if initialization_time:
            condition_query_hpt.update(**initialization_time)
        MobioLogging().info(
            "{} :: LosController :: get_los_by_filter :: condition_query_hpt :: {}".format(log_id, condition_query_hpt)
        )
        results, status_get, reasons = ThirdPartyLosHelper.send_request_pull_danh_sach(
            log_id, merchant_id, condition_query_hpt, account_id, action_time
        )
        if status_get == StatusCode.THIRD_PARTY_FAILURE:
            raise CustomError(reasons)
        result_return = []
        for item in results:
            result_return.append(self._convert_result_detail_los(config_custom, item, "list"))

        response_data = {"data": result_return}
        return response_data

    @classmethod
    def _convert_username_send_hpt(cls, config_custom, username):
        if not config_custom:
            return username
        replace_username = config_custom.get("replace_username")
        for item in replace_username:
            username = username.replace(item, "")
        return username

    @classmethod
    def _convert_datetime(cls, value):
        if "." in value:
            main_part, micro = value.split(".")
            # Remove the 'Z' and pad microseconds
            micro = micro.rstrip("Z").ljust(6, "0")
            value = f"{main_part}Z"

        return value

    @classmethod
    def _convert_initialization_time_send_hpt(cls, initialization_time):
        initialization_time_type = initialization_time.get(ConstantInitializationTime.TYPE)
        if initialization_time_type == ConstantInitializationTime.ValueType.CUSTOM_TIME:

            try:
                start_time = initialization_time.get("start_time")
                # start_time = cls._convert_datetime(start_time)
                start_time = datetime.datetime.strptime(start_time, "%Y-%m-%dT%H:%MZ")
                start_time = start_time.strftime("%d/%m/%Y")
            except Exception as ex:
                raise CustomError("Convert datetime fail, error :: {}".format(str(ex)))
            try:
                end_time = initialization_time.get("end_time")
                # end_time = cls._convert_datetime(end_time)
                end_time = datetime.datetime.strptime(end_time, "%Y-%m-%dT%H:%MZ")
                end_time = end_time.strftime("%d/%m/%Y")
            except Exception as ex:
                raise CustomError("Convert datetime fail, error :: {}".format(str(ex)))
            return {"NgayBatDau": start_time, "NgayKetThuc": end_time}
        elif initialization_time_type == ConstantInitializationTime.ValueType.NUMBER_31_DAY_AGO:
            end_time = datetime.datetime.now(datetime.UTC)
            start_time = end_time - datetime.timedelta(days=31)
            start_time = start_time.strftime("%d/%m/%Y")
            end_time = end_time.strftime("%d/%m/%Y")
            return {"NgayBatDau": start_time, "NgayKetThuc": end_time}
        return {}

    def detail_los(self):
        log_id = str(uuid.uuid4())
        los_code = request.args.get("code")
        customer_type = request.args.get("customer_type")
        if not los_code:
            raise CustomError("Mã LOS phải bắt buộc")
        merchant_id = get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token("id")
        action_time = datetime.datetime.now(datetime.UTC)
        config_custom = ConfigInfoApiModel().get_config_info_custom_information_send_hpt(merchant_id)
        MobioLogging().info("{} :: LosController :: detail_los :: code :: {}".format(log_id, los_code))
        condition_query_hpt = {"MaLOS": los_code, "LoaiKH": customer_type}
        MobioLogging().info("{} :: LosController :: detail_los :: code :: {}".format(log_id, los_code))
        results, status_get, reasons = ThirdPartyLosHelper.send_request_pull_trang_thai(
            log_id, merchant_id, condition_query_hpt, account_id, action_time
        )
        if status_get == StatusCode.THIRD_PARTY_FAILURE:
            raise CustomError(reasons)
        MobioLogging().info("{} :: LosController :: results :: {}".format(log_id, results))
        if results and isinstance(results, list) and len(results) >= 1:
            result = results[0]
            return {"data": self._convert_result_detail_los(config_custom, result)}
        return {"data": {}}

    @classmethod
    def _convert_result_detail_los(cls, config_custom, result, func="detail"):
        list_field_not_result = config_custom.get("list_field_not_result")
        MobioLogging().info("list_field_not_result :: {}".format(list_field_not_result))

        config_mapping_data_field = config_custom.get("config_mapping_field_result_{}".format(func))

        if list_field_not_result:
            for item in list_field_not_result:
                result.pop(item, None)
        if config_mapping_data_field:
            for key_pop, key_result in config_mapping_data_field.items():
                result[key_result] = result.pop(key_pop, None)
        return result

    def merchant_config_field(self):
        merchant_id = get_merchant_header()
        result = LosConfigModel().get_los_config_field(merchant_id)
        return {"data": result}

    def _validate_upsert_config_status(self, data_validate):
        rule_validate = {
            "data": [
                Required,
                InstanceOf(list),
                Length(1),
                Each(
                    {
                        "status_code": [Required, InstanceOf(str), Length(1)],
                        "title": [Required, InstanceOf(str), Length(1)],
                        "status": [Required, InstanceOf(str), Length(1), In(["inactive", "active", "delete"])],
                    }
                ),
            ]
        }
        self.validate_optional_err(rule_validate, data_validate)

    def upsert_config_status(self):
        merchant_id = get_merchant_header()

        body_request = request.get_json()
        self._validate_upsert_config_status(body_request)

        data_request = body_request.get("data")
        config_status = LosConfigModel().get_los_config_status(merchant_id)

        mapping_config_status = {item.get("key"): item for item in config_status}

        for item in data_request:
            status_code = item.get("status_code")
            title = item.get("title")
            color = item.get("color")
            status = item.get("status")

            item_by_status_code = mapping_config_status.get(status_code)

            if item_by_status_code:
                item_by_status_code["title"]["vi"] = title
                if not item_by_status_code.get("color") and color:
                    item_by_status_code["color"] = color

                item_by_status_code["status"] = status
                mapping_config_status[status_code] = item_by_status_code
            else:
                mapping_config_status[status_code] = {
                    "title": {"vi": title},
                    "key": status_code,
                    "color": color if color else "#FDD949",
                    "status": status,
                    "order": len(config_status) + 1,
                }
        data_update = list(mapping_config_status.values())
        MobioLogging().info("upsert_config_status :: data_update :: {}".format(data_update))
        status_update = LosConfigModel().update_by_set(
            {"merchant_id": merchant_id, "type": "status"}, {"config": data_update}
        )
        MobioLogging().info("upsert_config_status :: status_update :: {}".format(status_update))
        for item in data_update:
            title = item["title"]
            if isinstance(title, dict):
                item["title"] = title.get("vi")

        return {"data": data_update}


if __name__ == "__main__":
    print(
        LosController()._convert_initialization_time_send_hpt(
            {"type": ConstantInitializationTime.ValueType.NUMBER_31_DAY_AGO}
        )
    )
