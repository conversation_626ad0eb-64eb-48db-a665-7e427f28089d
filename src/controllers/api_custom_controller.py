#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/05/2024
"""


import os

from flask import Response, jsonify, request, stream_with_context
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import BaseController

from configs import MobileBackendApplicationConfig
from src.models.mongo.result_sample_model import ResultSampleModel


class ApiCustomController(BaseController):

    def get_result_sample(self, real_api):
        try:
            result_sample = ResultSampleModel().find_one({"path_api": real_api})
            if real_api in ["cic-gateway/api/v3/get-file", "api/ecms/download/CRM"] or real_api.startswith(
                "api/ecms/download"
            ):
                file_path = os.path.join(
                    MobileBackendApplicationConfig.RESOURCE_DIR, "sample_file/sample_report_cic.pdf"
                )
                mime_type = "application/pdf"

                # Hàm generator để đọc file theo từng chunk
                def generate():
                    with open(file_path, "rb") as f:
                        while True:
                            chunk = f.read(4096)  # Đọc 4KB mỗi lần
                            if not chunk:
                                break
                            yield chunk

                # Trả về phản hồi dạng stream
                return Response(
                    stream_with_context(generate()),
                    content_type=mime_type,
                    headers={"Content-Disposition": f'attachment; filename="x.pdf"'},
                )
        except Exception as e:
            raise CustomError("Error read database :: {}".format(str(e)))
        if result_sample:
            return jsonify(result_sample.get("data")), 200
        raise CustomError("Not sample path api {}".format(real_api))

    def insert_result(self):
        body = request.get_json()

        request_path_api = body.get("path_api")

        filter_query = {"path_api": request_path_api}

        ResultSampleModel().update_by_set(filter_query, body, upsert=True)

        return
