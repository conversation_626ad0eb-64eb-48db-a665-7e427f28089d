<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qu<PERSON>n lý thông tin khách hàng</title>
    <style>
        :root {
            /* Color Palette */
            --color-primary: #4361ee;
            --color-primary-dark: #3f37c9;
            --color-success: #4cc9f0;
            --color-error: #f72585;
            --color-background: #f8f9fa;
            --color-card: #ffffff;
            --color-text: #333333;
            --color-text-light: #666666;
            --color-border: #e0e0e0;

            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;

            /* Border Radius */
            --border-radius: 8px;

            /* Transitions */
            --transition-duration: 0.3s;
            --transition-timing: ease-in-out;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
            background-color: var(--color-background);
            color: var(--color-text);
            line-height: 1.6;
            padding: var(--spacing-lg);
        }

        h1 {
            color: var(--color-primary);
            border-bottom: 2px solid var(--color-border);
            padding-bottom: var(--spacing-sm);
            margin-bottom: var(--spacing-xl);
            font-size: 2rem;
            font-weight: 700;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* API Config Section */
        .api-config-container {
            background-color: rgba(76, 201, 240, 0.1);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-lg);
            border: 1px solid rgba(76, 201, 240, 0.3);
        }

        .api-config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            cursor: pointer;
        }

        .api-config-header h3 {
            margin: 0;
            font-size: 1.1rem;
            color: var(--color-primary);
        }

        .toggle-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 24px;
            height: 24px;
            position: relative;
        }

        .toggle-icon {
            display: block;
            position: relative;
            width: 100%;
            height: 2px;
            background-color: var(--color-primary);
            transition: transform var(--transition-duration) var(--transition-timing);
        }

        .toggle-icon:before,
        .toggle-icon:after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            background-color: var(--color-primary);
            transition: transform var(--transition-duration) var(--transition-timing);
        }

        .toggle-icon:before {
            transform: translateY(-6px);
        }

        .toggle-icon:after {
            transform: translateY(6px);
        }

        .toggle-btn[aria-expanded="true"] .toggle-icon {
            transform: rotate(45deg);
        }

        .toggle-btn[aria-expanded="true"] .toggle-icon:before {
            transform: translateY(0) rotate(90deg);
        }

        .toggle-btn[aria-expanded="true"] .toggle-icon:after {
            transform: translateY(0) rotate(90deg);
        }

        .api-config-content {
            padding: 0 var(--spacing-md) var(--spacing-md);
            max-height: 200px;
            overflow: hidden;
            transition: max-height var(--transition-duration) var(--transition-timing);
        }

        .api-config-content.collapsed {
            max-height: 0;
        }

        /* Search Container */
        .search-container {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--color-card);
            border-radius: var(--border-radius);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .search-form {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .search-form input[type="text"] {
            flex: 1;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: border-color var(--transition-duration) var(--transition-timing),
                box-shadow var(--transition-duration) var(--transition-timing);
        }

        .search-form input[type="text"]:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        .search-form button {
            background-color: var(--color-primary);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: background-color var(--transition-duration) var(--transition-timing),
                transform var(--transition-duration) var(--transition-timing);
        }

        .search-form button:hover {
            background-color: var(--color-primary-dark);
            transform: translateY(-1px);
        }

        .search-form button:active {
            transform: translateY(1px);
        }

        /* Form Grid Layout */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        @media (min-width: 768px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1200px) {
            .form-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        .form-section {
            background-color: var(--color-card);
            border-radius: var(--border-radius);
            padding: var(--spacing-lg);
            border: 1px solid var(--color-border);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .form-section legend {
            font-weight: 600;
            padding: 0 var(--spacing-sm);
            color: var(--color-primary);
            font-size: 1.1rem;
        }

        .form-group {
            margin-bottom: var(--spacing-md);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 500;
            color: var(--color-text);
        }

        .input-with-icon {
            position: relative;
        }

        .input-with-icon i {
            position: absolute;
            left: 12px;
            top: 12px;
            color: var(--color-text-light);
            width: 18px;
            height: 18px;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
            /* Ensure proper icon rendering */
            line-height: 1;
            font-style: normal;
        }

        .input-with-icon i svg {
            width: 100%;
            height: 100%;
            fill: none;
            stroke: currentColor;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
            /* Improve SVG rendering */
            display: block;
        }

        /* Ensure icons are visible and properly sized */
        .input-with-icon i::before {
            content: '';
            display: none;
        }

        /* Fix for potential icon display issues */
        .input-with-icon i svg path,
        .input-with-icon i svg circle,
        .input-with-icon i svg rect,
        .input-with-icon i svg line,
        .input-with-icon i svg polyline {
            stroke: currentColor;
            fill: none;
        }

        /* Ensure SVG icons are properly rendered in all browsers */
        .input-with-icon i svg {
            shape-rendering: geometricPrecision;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .input-with-icon input,
        .input-with-icon select,
        .input-with-icon textarea {
            width: 100%;
            padding: 12px 12px 12px 42px;
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            font-size: 1rem;
            line-height: 1.5;
            transition: border-color var(--transition-duration) var(--transition-timing),
                box-shadow var(--transition-duration) var(--transition-timing);
            position: relative;
            z-index: 1;
            background-color: #fff;
        }

        .input-with-icon input:focus,
        .input-with-icon select:focus,
        .input-with-icon textarea:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        /* Enhanced icon visibility on focus */
        .input-with-icon:focus-within i {
            color: var(--color-primary);
        }

        /* Ensure icons don't interfere with input interaction */
        .input-with-icon {
            position: relative;
            display: block;
        }

        /* Fix for select dropdown arrow positioning */
        .input-with-icon select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right var(--spacing-sm) center;
            background-size: 16px;
            padding-right: calc(var(--spacing-lg) + var(--spacing-sm));
        }

        .required:after {
            content: " *";
            color: var(--color-error);
        }

        .error-message {
            color: var(--color-error);
            font-size: 0.875rem;
            margin-top: var(--spacing-xs);
            min-height: 1.25rem;
        }

        .input-error {
            border-color: var(--color-error) !important;
        }

        /* Toggle Switches */
        .toggle-container {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
            margin-right: var(--spacing-sm);
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: var(--transition-duration);
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: var(--transition-duration);
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: var(--color-primary);
        }

        input:focus+.slider {
            box-shadow: 0 0 1px var(--color-primary);
        }

        input:checked+.slider:before {
            transform: translateX(26px);
        }

        .toggle-container label {
            font-weight: normal;
            margin-bottom: 0;
        }

        /* Buttons */
        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color var(--transition-duration) var(--transition-timing),
                transform var(--transition-duration) var(--transition-timing);
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn:active {
            transform: translateY(1px);
        }

        .btn-primary {
            background-color: var(--color-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--color-primary-dark);
        }

        .btn-primary:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
        }

        .btn-danger {
            background-color: var(--color-error);
            color: white;
        }

        .btn-danger:hover {
            background-color: #e91e63;
        }

        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        /* Customer List */
        .customer-list {
            background-color: var(--color-card);
            border-radius: var(--border-radius);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            padding: var(--spacing-lg);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table th,
        table td {
            padding: var(--spacing-sm) var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--color-border);
        }

        table th {
            background-color: var(--color-background);
            color: var(--color-text);
            font-weight: 600;
        }

        table tr:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }

        .action-buttons {
            display: flex;
            gap: var(--spacing-xs);
        }

        .message {
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            border-radius: var(--border-radius);
        }

        .message-success {
            background-color: rgba(76, 201, 240, 0.1);
            color: #0c5460;
            border: 1px solid rgba(76, 201, 240, 0.3);
        }

        .message-error {
            background-color: rgba(247, 37, 133, 0.1);
            color: #721c24;
            border: 1px solid rgba(247, 37, 133, 0.3);
        }

        .hidden {
            display: none;
        }

        /* Toast Notification */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .toast {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--color-success);
            color: white;
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: var(--spacing-md);
            transform: translateX(120%);
            transition: transform var(--transition-duration) var(--transition-timing);
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
        }

        .toast-content i {
            margin-right: var(--spacing-sm);
            width: 16px;
            height: 16px;
        }

        .toast-content i svg {
            width: 100%;
            height: 100%;
            fill: none;
            stroke: currentColor;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        .toast-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            margin-left: var(--spacing-md);
        }

        /* Responsive Design */
        @media (max-width: 576px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }

            .search-form {
                flex-direction: column;
            }

            .search-form input[type="text"] {
                margin-bottom: var(--spacing-sm);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Quản lý thông tin khách hàng</h1>

        {% if message %}
        <div class="message {% if success %}message-success{% else %}message-error{% endif %}">
            {{ message }}
        </div>
        {% endif %}

        <!-- API Configuration Section -->
        <div class="api-config-container">
            <div class="api-config-header">
                <h3>Cấu hình API Endpoint</h3>
                <button class="toggle-btn" id="toggleApiConfig" aria-expanded="true">
                    <span class="toggle-icon"></span>
                </button>
            </div>
            <div class="api-config-content" id="apiConfigContent">
                <div class="form-group">
                    <label for="apiEndpoint">Domain API</label>
                    <div class="input-with-icon">
                        <i class="icon-server">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                                <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                                <line x1="6" y1="6" x2="6.01" y2="6"></line>
                                <line x1="6" y1="18" x2="6.01" y2="18"></line>
                            </svg>
                        </i>
                        <input type="text" id="apiEndpoint" name="apiEndpoint" placeholder="https://api.my-service.com"
                            aria-describedby="apiEndpointHelp">
                    </div>
                    <small id="apiEndpointHelp">Nhập địa chỉ domain của API</small>
                </div>
                <button type="button" id="saveApiConfig" class="btn btn-primary">Lưu Cấu hình</button>
            </div>
        </div>

        <!-- Search Form -->
        <div class="search-container">
            <form class="search-form" action="/pbuser/mobilebackend/mockup-data/api/v1/customer-form" method="get">
                <input type="text" name="search" placeholder="Tìm kiếm theo tên, CCCD, CIF..."
                    value="{{ request.args.get('search', '') }}">
                <button type="submit">Tìm kiếm</button>
            </form>
        </div>

        <!-- Customer Form -->
        <div class="form-container">
            <h2 id="form-title">Tạo customer mới</h2>
            <form id="customer-form" action="/pbuser/mobilebackend/mockup-data/api/v1/customer-form" method="post">
                <input type="hidden" id="customer_id" name="customer_id" value="">

                <div class="form-grid">
                    <!-- Thông tin cá nhân -->
                    <fieldset class="form-section">
                        <legend>Thông tin cá nhân</legend>

                        <div class="form-group">
                            <label for="name" class="required">Họ và tên</label>
                            <div class="input-with-icon">
                                <i class="icon-user">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </i>
                                <input type="text" id="name" name="name" required aria-required="true">
                                <div class="error-message" id="name-error"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="dob" class="required">Ngày sinh</label>
                            <div class="input-with-icon">
                                <i class="icon-calendar">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                        <line x1="16" y1="2" x2="16" y2="6"></line>
                                        <line x1="8" y1="2" x2="8" y2="6"></line>
                                        <line x1="3" y1="10" x2="21" y2="10"></line>
                                    </svg>
                                </i>
                                <input type="text" id="dob" name="dob" placeholder="DD/MM/YYYY" required
                                    aria-required="true">
                                <div class="error-message" id="dob-error"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="date_of_issue" class="required">Ngày cấp CCCD</label>
                            <div class="input-with-icon">
                                <i class="icon-calendar">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                        <line x1="16" y1="2" x2="16" y2="6"></line>
                                        <line x1="8" y1="2" x2="8" y2="6"></line>
                                        <line x1="3" y1="10" x2="21" y2="10"></line>
                                    </svg>
                                </i>
                                <input type="text" id="date_of_issue" name="date_of_issue" placeholder="DD/MM/YYYY"
                                    required aria-required="true">
                                <div class="error-message" id="date_of_issue-error"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="origin" class="required">Nơi sinh</label>
                            <div class="input-with-icon">
                                <i class="icon-location">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                                        <circle cx="12" cy="10" r="3"></circle>
                                    </svg>
                                </i>
                                <input type="text" id="origin" name="origin" required aria-required="true">
                                <div class="error-message" id="origin-error"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="id_card" class="required">Số CCCD</label>
                            <div class="input-with-icon">
                                <i class="icon-id-card">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <rect x="2" y="4" width="20" height="16" rx="2"></rect>
                                        <path d="M8 12h8"></path>
                                        <path d="M8 16h8"></path>
                                        <circle cx="8" cy="8" r="2"></circle>
                                    </svg>
                                </i>
                                <input type="text" id="id_card" name="id_card" required aria-required="true">
                                <div class="error-message" id="id_card-error"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="cif">CIF</label>
                            <div class="input-with-icon">
                                <i class="icon-document">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                        <polyline points="14 2 14 8 20 8"></polyline>
                                        <line x1="16" y1="13" x2="8" y2="13"></line>
                                        <line x1="16" y1="17" x2="8" y2="17"></line>
                                        <polyline points="10 9 9 9 8 9"></polyline>
                                    </svg>
                                </i>
                                <input type="text" id="cif" name="cif">
                                <div class="error-message" id="cif-error"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="ethnicity">Dân tộc</label>
                            <div class="input-with-icon">
                                <i class="icon-users">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="9" cy="7" r="4"></circle>
                                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                    </svg>
                                </i>
                                <input type="text" id="ethnicity" name="ethnicity">
                                <div class="error-message" id="ethnicity-error"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="gender" class="required">Giới tính</label>
                            <div class="input-with-icon">
                                <i class="icon-user">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </i>
                                <select id="gender" name="gender" required aria-required="true">
                                    <option value="">Chọn giới tính</option>
                                    <option value="Nam">Nam</option>
                                    <option value="Nữ">Nữ</option>
                                    <option value="Khác">Khác</option>
                                </select>
                                <div class="error-message" id="gender-error"></div>
                            </div>
                        </div>
                    </fieldset>

                    <!-- Thông tin gia đình -->
                    <fieldset class="form-section">
                        <legend>Thông tin gia đình</legend>

                        <div class="form-group">
                            <label for="father_name">Tên cha (tùy chọn)</label>
                            <div class="input-with-icon">
                                <i class="icon-user">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </i>
                                <input type="text" id="father_name" name="father_name">
                                <div class="error-message" id="father_name-error"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="mother_name">Tên mẹ (tùy chọn)</label>
                            <div class="input-with-icon">
                                <i class="icon-user">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </i>
                                <input type="text" id="mother_name" name="mother_name">
                                <div class="error-message" id="mother_name-error"></div>
                            </div>
                        </div>
                    </fieldset>

                    <!-- Thông tin địa chỉ -->
                    <fieldset class="form-section">
                        <legend>Thông tin địa chỉ</legend>

                        <div class="form-group">
                            <label for="address">Địa chỉ chi tiết</label>
                            <div class="input-with-icon">
                                <i class="icon-location">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                                        <circle cx="12" cy="10" r="3"></circle>
                                    </svg>
                                </i>
                                <input type="text" id="address" name="address">
                                <div class="error-message" id="address-error"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="old_id_card">Số CMND cũ</label>
                            <div class="input-with-icon">
                                <i class="icon-id-card">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <rect x="2" y="4" width="20" height="16" rx="2"></rect>
                                        <path d="M8 12h8"></path>
                                        <path d="M8 16h8"></path>
                                        <circle cx="8" cy="8" r="2"></circle>
                                    </svg>
                                </i>
                                <input type="text" id="old_id_card" name="old_id_card">
                                <div class="error-message" id="old_id_card-error"></div>
                            </div>
                        </div>
                    </fieldset>

                    <!-- Cài đặt xác thực -->
                    <fieldset class="form-section">
                        <legend>Cài đặt xác thực</legend>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="face_verified" name="face_verified">
                                <span class="slider"></span>
                            </label>
                            <label for="face_verified">Xác thực khuôn mặt</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="id_verified" name="id_verified">
                                <span class="slider"></span>
                            </label>
                            <label for="id_verified">Xác thực CMND</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="check_card" name="check_card">
                                <span class="slider"></span>
                            </label>
                            <label for="check_card">Kiểm tra thẻ CCCD với CO6</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="check_aml" name="check_aml">
                                <span class="slider"></span>
                            </label>
                            <label for="check_aml">Kiểm tra AML</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="exist_cif_with_id_card" name="exist_cif_with_id_card">
                                <span class="slider"></span>
                            </label>
                            <label for="exist_cif_with_id_card">Tồn tại CIF với CMND</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="set_data_default" name="set_data_default">
                                <span class="slider"></span>
                            </label>
                            <label for="set_data_default">Dữ liệu mặc định</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="fail_when_create_cif" name="fail_when_create_cif">
                                <span class="slider"></span>
                            </label>
                            <label for="fail_when_create_cif">Fail khi tạo CIF</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="fail_when_create_account_indiv"
                                    name="fail_when_create_account_indiv">
                                <span class="slider"></span>
                            </label>
                            <label for="fail_when_create_account_indiv">Fail khi tạo tài khoản Thanh toán</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="fail_when_create_account_edigi"
                                    name="fail_when_create_account_edigi">
                                <span class="slider"></span>
                            </label>
                            <label for="fail_when_create_account_edigi">Fail khi tạo tài khoản EDI</label>
                        </div>
                    </fieldset>

                    <!-- Cấu hình hệ thống -->
                    <fieldset class="form-section">
                        <legend>Cấu hình hệ thống</legend>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_logging" name="enable_logging">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_logging">Bật ghi log hệ thống</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_debug_mode" name="enable_debug_mode">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_debug_mode">Chế độ debug</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_email_notification" name="enable_email_notification">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_email_notification">Thông báo qua email</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_sms_notification" name="enable_sms_notification">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_sms_notification">Thông báo qua SMS</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_auto_backup" name="enable_auto_backup">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_auto_backup">Tự động sao lưu dữ liệu</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_maintenance_mode" name="enable_maintenance_mode">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_maintenance_mode">Chế độ bảo trì</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_api_rate_limit" name="enable_api_rate_limit">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_api_rate_limit">Giới hạn tốc độ API</label>
                        </div>

                        <div class="toggle-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="enable_security_audit" name="enable_security_audit">
                                <span class="slider"></span>
                            </label>
                            <label for="enable_security_audit">Kiểm tra bảo mật</label>
                        </div>
                    </fieldset>
                </div>

                <div class="form-actions">
                    <button type="submit" id="submit-btn" class="btn btn-primary" disabled>Tạo khách hàng</button>
                    <button type="button" id="reset-btn" class="btn btn-danger">Reset</button>
                </div>
            </form>
        </div>

        <!-- Customer List -->
        <div class="customer-list">
            <h2>Danh sách khách hàng</h2>

            {% if customers %}
            <table>
                <thead>
                    <tr>
                        <th>Tên khách hàng</th>
                        <th>Số CMND</th>
                        <th>CIF</th>
                        <th>Ngày sinh</th>
                        <th>Ngày cấp CCCD</th>
                        <th>Xác thực khuôn mặt</th>
                        <th>Xác thực CCCD</th>
                        <th>Kiểm tra thẻ CCCD với CO6</th>
                        <th>Kiểm tra AML</th>
                        <th>Số CMND cũ</th>
                        <th>Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers %}
                    <tr>
                        <td>{{ customer.name }}</td>
                        <td>{{ customer.id_card }}</td>
                        <td>{{ customer.cif }}</td>
                        <td>{{ customer.dob }}</td>
                        <td>{{ customer.date_of_issue }}</td>
                        <td>{{ 'Yes' if customer.face_verified else 'No' }}</td>
                        <td>{{ 'Yes' if customer.id_verified else 'No' }}</td>
                        <td>{{ 'Yes' if customer.check_card else 'No' }}</td>
                        <td>{{ 'Yes' if customer.check_aml else 'No' }}</td>
                        <td>{{ customer.old_id_card }}</td>
                        <td class="action-buttons">
                            <button class="btn btn-primary" onclick="editCustomer(this)"
                                data-customer='{{ customer|tojson|safe }}'>Sửa khách hàng</button>
                            <button class="btn btn-danger" onclick="deleteCustomer('{{ customer.id }}')">Xóa</button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p>Không tìm thấy khách hàng.</p>
            {% endif %}
        </div>
    </div>

    <!-- Toast Notification -->
    <div class="toast-container">
        <div class="toast" id="toast" role="alert" aria-live="polite">
            <div class="toast-content">
                <i class="icon-check">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                </i>
                <span id="toast-message">Đã lưu cấu hình thành công!</span>
            </div>
            <button class="toast-close" aria-label="Đóng thông báo">×</button>
        </div>
    </div>

    <script>
        // Icon Display Debug Function
        function debugIconDisplay() {
            const icons = document.querySelectorAll('.input-with-icon i');
            console.log('Icon Debug Information:');
            icons.forEach((icon, index) => {
                const svg = icon.querySelector('svg');
                const computedStyle = window.getComputedStyle(icon);
                console.log(`Icon ${index + 1}:`, {
                    className: icon.className,
                    visible: computedStyle.display !== 'none',
                    width: computedStyle.width,
                    height: computedStyle.height,
                    color: computedStyle.color,
                    hasSVG: !!svg,
                    svgPaths: svg ? svg.querySelectorAll('path, circle, rect, line, polyline').length : 0
                });
            });
        }

        // Run icon debug on page load
        document.addEventListener('DOMContentLoaded', function () {
            // Debug icons after a short delay to ensure all styles are loaded
            setTimeout(debugIconDisplay, 100);
        });

        // API Configuration
        const toggleApiConfig = document.getElementById('toggleApiConfig');
        const apiConfigContent = document.getElementById('apiConfigContent');
        const saveApiConfig = document.getElementById('saveApiConfig');
        const apiEndpoint = document.getElementById('apiEndpoint');

        // Toggle API Config Section
        toggleApiConfig.addEventListener('click', function () {
            const expanded = this.getAttribute('aria-expanded') === 'true';
            this.setAttribute('aria-expanded', !expanded);
            if (expanded) {
                apiConfigContent.classList.add('collapsed');
            } else {
                apiConfigContent.classList.remove('collapsed');
            }
        });

        // Load saved endpoint on page load
        document.addEventListener('DOMContentLoaded', function () {
            const savedEndpoint = localStorage.getItem('customerFormApiEndpoint');
            if (savedEndpoint) {
                apiEndpoint.value = savedEndpoint;
            }
        });

        // Save API Endpoint to localStorage
        saveApiConfig.addEventListener('click', function () {
            const endpoint = apiEndpoint.value.trim();
            if (endpoint) {
                localStorage.setItem('customerFormApiEndpoint', endpoint);
                showToast('Đã lưu cấu hình thành công!');
            } else {
                showToast('Vui lòng nhập địa chỉ domain API!', 'error');
            }
        });

        // Form validation
        const form = document.getElementById('customer-form');
        const submitBtn = document.getElementById('submit-btn');
        const requiredFields = form.querySelectorAll('[required]');
        const dateFields = [document.getElementById('dob'), document.getElementById('date_of_issue')];
        const idCardField = document.getElementById('id_card');

        // Validate on input for all required fields
        requiredFields.forEach(field => {
            field.addEventListener('input', validateField);
            field.addEventListener('blur', validateField);
        });

        // Validate date fields
        dateFields.forEach(field => {
            field.addEventListener('blur', validateDateFormat);
        });

        // Validate ID card
        idCardField.addEventListener('blur', validateIdCard);

        function validateField() {
            const errorElement = document.getElementById(`${this.id}-error`);
            if (!this.value.trim()) {
                this.classList.add('input-error');
                errorElement.textContent = 'Trường này là bắt buộc';
                return false;
            } else {
                this.classList.remove('input-error');
                errorElement.textContent = '';
                checkFormValidity();
                return true;
            }
        }

        function validateDateFormat() {
            const errorElement = document.getElementById(`${this.id}-error`);
            const datePattern = /^(\d{2})\/(\d{2})\/(\d{4})$/;
            const dateStr = this.value.trim();

            if (!dateStr) {
                if (this.hasAttribute('required')) {
                    this.classList.add('input-error');
                    errorElement.textContent = 'Trường này là bắt buộc';
                    return false;
                }
                return true;
            }

            if (!datePattern.test(dateStr)) {
                this.classList.add('input-error');
                errorElement.textContent = 'Vui lòng nhập ngày theo định dạng DD/MM/YYYY';
                return false;
            }

            const matches = dateStr.match(datePattern);
            const day = parseInt(matches[1], 10);
            const month = parseInt(matches[2], 10);
            const year = parseInt(matches[3], 10);

            // Check if date is valid
            if (month < 1 || month > 12 || day < 1 || day > 31) {
                this.classList.add('input-error');
                errorElement.textContent = 'Ngày không hợp lệ. Vui lòng kiểm tra lại.';
                return false;
            }

            // Further validation for specific months
            const daysInMonth = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
            // Adjust February for leap years
            if (year % 400 === 0 || (year % 100 !== 0 && year % 4 === 0)) {
                daysInMonth[2] = 29;
            }

            if (day > daysInMonth[month]) {
                this.classList.add('input-error');
                errorElement.textContent = 'Ngày không hợp lệ cho tháng đã chọn.';
                return false;
            }

            this.classList.remove('input-error');
            errorElement.textContent = '';
            checkFormValidity();
            return true;
        }

        function validateIdCard() {
            const errorElement = document.getElementById(`${this.id}-error`);
            const idCardValue = this.value.trim();

            if (!idCardValue) {
                if (this.hasAttribute('required')) {
                    this.classList.add('input-error');
                    errorElement.textContent = 'Trường này là bắt buộc';
                    return false;
                }
                return true;
            }

            // Check if ID card is numeric and has 9 or 12 digits
            if (!/^\d+$/.test(idCardValue)) {
                this.classList.add('input-error');
                errorElement.textContent = 'Số CCCD chỉ được chứa các chữ số';
                return false;
            }

            if (idCardValue.length !== 9 && idCardValue.length !== 12) {
                this.classList.add('input-error');
                errorElement.textContent = 'Số CCCD phải có 9 hoặc 12 chữ số';
                return false;
            }

            this.classList.remove('input-error');
            errorElement.textContent = '';
            checkFormValidity();
            return true;
        }

        function checkFormValidity() {
            let isValid = true;

            // Check all required fields
            requiredFields.forEach(field => {
                if (!field.value.trim() || field.classList.contains('input-error')) {
                    isValid = false;
                }
            });

            // Enable/disable submit button
            submitBtn.disabled = !isValid;
        }

        // Form submission
        form.addEventListener('submit', function (event) {
            let isValid = true;

            // Validate all required fields
            requiredFields.forEach(field => {
                if (!validateField.call(field)) {
                    isValid = false;
                }
            });

            // Validate date fields
            dateFields.forEach(field => {
                if (!validateDateFormat.call(field)) {
                    isValid = false;
                }
            });

            // Validate ID card
            if (!validateIdCard.call(idCardField)) {
                isValid = false;
            }

            if (!isValid) {
                event.preventDefault();
            }
        });

        // Toast notification
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toast-message');

            toastMessage.textContent = message;

            if (type === 'error') {
                toast.style.backgroundColor = 'var(--color-error)';
            } else {
                toast.style.backgroundColor = 'var(--color-success)';
            }

            toast.classList.add('show');

            // Auto hide after 3 seconds
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // Close toast on click
        document.querySelector('.toast-close').addEventListener('click', function () {
            document.getElementById('toast').classList.remove('show');
        });

        // Reset form
        const resetBtn = document.getElementById('reset-btn');

        resetBtn.addEventListener('click', function () {
            document.getElementById('form-title').innerText = 'Tạo khách hàng mới';
            document.getElementById('submit-btn').innerText = 'Tạo khách hàng';
            form.reset();
            document.getElementById('customer_id').value = '';

            // Clear all error messages
            document.querySelectorAll('.error-message').forEach(element => {
                element.textContent = '';
            });

            // Remove error class from all inputs
            document.querySelectorAll('.input-error').forEach(element => {
                element.classList.remove('input-error');
            });

            // Disable submit button
            submitBtn.disabled = true;
        });

        function editCustomer(buttonElement) {
            // Get customer data from the button's data attribute
            const customer = JSON.parse(buttonElement.getAttribute('data-customer'));

            // Set form title
            document.getElementById('form-title').innerText = 'Sửa khách hàng';
            document.getElementById('submit-btn').innerText = 'Cập nhật khách hàng';

            // Fill form fields with customer data
            document.getElementById('customer_id').value = customer.id;
            document.getElementById('name').value = customer.name;

            // Format date as DD/MM/YYYY
            let dobValue = customer.dob;
            if (dobValue && dobValue.includes('-')) {
                // If date is in YYYY-MM-DD format, convert to DD/MM/YYYY
                dobValue = dobValue.split('-').reverse().join('/');
            }
            document.getElementById('dob').value = dobValue;

            let date_of_issueValue = customer.date_of_issue;
            if (date_of_issueValue && date_of_issueValue.includes('-')) {
                // If date is in YYYY-MM-DD format, convert to DD/MM/YYYY
                date_of_issueValue = date_of_issueValue.split('-').reverse().join('/');
            }
            document.getElementById('date_of_issue').value = date_of_issueValue;

            document.getElementById('origin').value = customer.origin;
            document.getElementById('id_card').value = customer.id_card;
            document.getElementById('cif').value = customer.cif;
            document.getElementById('ethnicity').value = customer.ethnicity;
            document.getElementById('gender').value = customer.gender;
            document.getElementById('father_name').value = customer.father_name || '';
            document.getElementById('mother_name').value = customer.mother_name || '';
            document.getElementById('address').value = customer.address || '';
            document.getElementById('old_id_card').value = customer.old_id_card || '';
            document.getElementById('face_verified').checked = customer.face_verified;
            document.getElementById('id_verified').checked = customer.id_verified;
            document.getElementById('check_card').checked = customer.check_card;
            document.getElementById('check_aml').checked = customer.check_aml;
            document.getElementById('exist_cif_with_id_card').checked = customer.exist_cif_with_id_card;
            document.getElementById('set_data_default').checked = customer.set_data_default;
            document.getElementById('fail_when_create_cif').checked = customer.fail_when_create_cif;
            document.getElementById('fail_when_create_account_indiv').checked = customer.fail_when_create_account_indiv;
            document.getElementById('fail_when_create_account_edigi').checked = customer.fail_when_create_account_edigi;

            // Cấu hình hệ thống
            document.getElementById('enable_logging').checked = customer.enable_logging || false;
            document.getElementById('enable_debug_mode').checked = customer.enable_debug_mode || false;
            document.getElementById('enable_email_notification').checked = customer.enable_email_notification || false;
            document.getElementById('enable_sms_notification').checked = customer.enable_sms_notification || false;
            document.getElementById('enable_auto_backup').checked = customer.enable_auto_backup || false;
            document.getElementById('enable_maintenance_mode').checked = customer.enable_maintenance_mode || false;
            document.getElementById('enable_api_rate_limit').checked = customer.enable_api_rate_limit || false;
            document.getElementById('enable_security_audit').checked = customer.enable_security_audit || false;

            // Clear error messages and re-validate
            document.querySelectorAll('.error-message').forEach(element => {
                element.textContent = '';
            });
            document.querySelectorAll('.input-error').forEach(element => {
                element.classList.remove('input-error');
            });

            // Re-validate form
            checkFormValidity();

            // Scroll to form
            document.querySelector('.form-container').scrollIntoView({ behavior: 'smooth' });
        }

        function deleteCustomer(customerId) {
            if (confirm('Are you sure you want to delete this customer?')) {
                // Create a form to submit the delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = "/pbuser/mobilebackend/mockup-data/api/v1/delete-customer";

                const idField = document.createElement('input');
                idField.type = 'hidden';
                idField.name = 'customer_id';
                idField.value = customerId;

                form.appendChild(idField);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>

</html>