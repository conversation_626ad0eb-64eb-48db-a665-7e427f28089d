<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Visualization Flow QuickSales</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f7f9fc;
            color: #333;
        }

        h1 {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .workflow-container {
            display: flex;
            margin-bottom: 40px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            background-color: white;
        }

        .workflow-info {
            flex: 0 0 300px;
            padding: 15px;
            background-color: #f8f9fa;
            border-right: 1px solid #eee;
        }

        .workflow-tree {
            flex: 1;
            padding: 15px;
            overflow-x: auto;
        }

        .step-tree {
            position: relative;
        }

        .step-container {
            margin-bottom: 15px;
            border-left: 2px dashed #ddd;
            padding-left: 20px;
            position: relative;
        }

        .step-container:before {
            content: "";
            position: absolute;
            left: 0;
            top: 10px;
            width: 12px;
            height: 12px;
            background-color: #4CAF50;
            border-radius: 50%;
            margin-left: -6px;
        }

        .step-header {
            background-color: #e9f5fe;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
            border-left: 5px solid #2196F3;
        }

        .step-header:hover {
            background-color: #d3eafd;
        }

        .step-header h3 {
            margin: 0;
            font-size: 16px;
            color: #1a73e8;
        }

        .step-status {
            font-size: 14px;
            font-weight: bold;
        }

        .status-success {
            color: #4CAF50;
        }

        .status-fail {
            color: #f44336;
        }

        .step-content {
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            overflow: auto;
            max-height: 250px;
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .result-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }

        .result-item.fail {
            border-left-color: #f44336;
        }

        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
            font-family: 'Courier New', Courier, monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .step-time {
            color: #777;
            font-size: 13px;
            margin-top: 5px;
        }

        .workflow-details {
            margin-bottom: 15px;
        }

        .workflow-details div {
            margin-bottom: 8px;
        }

        .property-label {
            font-weight: bold;
            color: #555;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #777;
        }

        .next-step {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px dashed #eee;
        }

        .next-step-label {
            color: #777;
            font-size: 14px;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .next-step-value {
            font-weight: bold;
            color: #1a73e8;
            margin-bottom: 6px;
        }

        .next-step div {
            margin-bottom: 5px;
        }

        /* Search form styles */
        .search-container {
            margin-bottom: 30px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .search-form {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-form input[type="text"] {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }

        .search-form button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.2s;
        }

        .search-form button:hover {
            background-color: #0b7dda;
        }

        .search-message {
            margin-top: 15px;
            padding: 10px 15px;
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
        }

        /* Popup modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            width: 80%;
            max-width: 900px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
        }

        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-radius: 4px 4px 0 0;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
            font-size: 16px;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #2196F3;
            color: white;
        }

        .tabcontent {
            display: none;
            padding: 15px;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }

        .api-log-link {
            color: #2196F3;
            text-decoration: underline;
            cursor: pointer;
            margin-left: 10px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }

        /* Curl container styles */
        .curl-container {
            position: relative;
        }

        .curl-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .copy-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .copy-btn:hover {
            background-color: #0b7dda;
        }
    </style>
</head>

<body>
    <!-- Auth Modal -->
    <div id="authModal" class="modal" style="display: block;">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h2>Đăng nhập</h2>
            </div>
            <div class="modal-body">
                <div id="authError" style="color: red; margin-bottom: 10px; display: none;">Sai tên đăng nhập hoặc mật
                    khẩu</div>
                <div style="margin-bottom: 15px;">
                    <label for="username" style="display: block; margin-bottom: 5px;">Tên đăng nhập:</label>
                    <input type="text" id="username" style="width: 100%; padding: 8px; box-sizing: border-box;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label for="password" style="display: block; margin-bottom: 5px;">Mật khẩu:</label>
                    <input type="password" id="password" style="width: 100%; padding: 8px; box-sizing: border-box;">
                </div>
                <button onclick="authenticate()"
                    style="background-color: #2196F3; color: white; border: none; padding: 10px 15px; cursor: pointer; width: 100%;">Đăng
                    nhập</button>
            </div>
        </div>
    </div>

    <div id="mainContent" style="display: none;">
        <h1>Log Visualization Flow QuickSales</h1>

        <!-- Search Form -->
        <div class="search-container">
            <form class="search-form" action="" method="get">
                <input type="text" name="object_id" placeholder="Nhập Profile ID hoặc Số CCCD để tìm kiếm..."
                    value="{{ request.args.get('object_id', '') }}">
                <button type="submit">Tìm kiếm</button>
            </form>
            {% if search_message %}
            <div class="search-message">
                {{ search_message }}
            </div>
            {% endif %}
        </div>

        {# Define a macro for step label translation #}
        {% macro translate_step(step_key) %}
        {% set step_labels = {
        'step_submit_form': 'Bước submit form',
        'step_create_cif': 'Bước tạo CIF',
        'step_create_account_indiv': 'Bước mở tài khoản thanh toán',
        'step_register_edigi_account': 'Bước đăng ký tài khoản EDIGI',
        'step_insert_in4_dsa': 'Bước ghi nhận KPI',
        'step_update_data_to_crm': 'Bước cập nhật dữ liệu về CRM'
        } %}
        {% if step_key in step_labels %}
        {{ step_labels[step_key] }}
        {% else %}
        {{ step_key }}
        {% endif %}
        {% endmacro %}

        {% if log_quick_sales %}
        {% for workflow in log_quick_sales %}
        <div class="workflow-container">
            <div class="workflow-info">
                <div class="workflow-details">
                    <h2>Thông tin</h2>
                    <div>
                        <span class="property-label">Object ID:</span>
                        <span>{{ workflow.object_id }}</span>
                    </div>
                    <div>
                        <span class="property-label">Object Type:</span>
                        <span>{{ workflow.object_type }}</span>
                    </div>
                    <div>
                        <span class="property-label">Merchant ID:</span>
                        <span>{{ workflow.merchant_id }}</span>
                    </div>
                    <div>
                        <span class="property-label">Tên nhân viên:</span>
                        <span>{{ workflow.staff_name }}</span>
                    </div>
                    <div>
                        <span class="property-label">Form ID:</span>
                        <span>{{ workflow.form_id }}</span>
                    </div>
                    <div>
                        <span class="property-label">Trạng thái:</span>
                        <span
                            class="{% if workflow.status == 'success' %}status-success{% else %}status-fail{% endif %}">
                            {{ workflow.status.upper() }}
                        </span>
                    </div>
                    {% if workflow.reason %}
                    <div>
                        <span class="property-label">Lý do lỗi (Nếu có):</span>
                        <span>{{ workflow.reason }}</span>
                    </div>
                    {% endif %}
                    <div>
                        <span class="property-label">Thời gian tạo:</span>
                        <span>{{ workflow.created_time }}</span>
                    </div>
                </div>
            </div>

            <div class="workflow-tree">
                <h2>Bước thực hiện</h2>
                <div class="step-tree">
                    {% for step_key, step_data in workflow.items() %}
                    {% if step_key.startswith('step_') %}
                    <div class="step-container">
                        <div class="step-header" onclick="toggleStepContent(this)">
                            <h3>{{ translate_step(step_key) }}</h3>
                            <div class="step-status">
                                {% set has_result = step_data.get('result_step_current', [])|length > 0 %}
                                {% set success = true %}
                                {% for result in step_data.get('result_step_current', []) %}
                                {% if result.get('status') == 'fail' %}
                                {% set success = false %}
                                {% endif %}
                                {% endfor %}

                                {% if has_result %}
                                <span class="{% if success %}status-success{% else %}status-fail{% endif %}">
                                    {{ 'SUCCESS' if success else 'FAIL' }}
                                </span>
                                {% else %}
                                <span>No result</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="step-content">
                            {% if step_data.get('action_time') %}
                            <div class="step-time">
                                Action Time: {{ step_data.action_time }}
                            </div>
                            {% endif %}

                            {% if step_data.get('result_step_current') %}
                            <h4>Results:</h4>
                            {% for result in step_data.result_step_current %}
                            <div class="result-item {% if result.get('status') == 'fail' %}fail{% endif %}">
                                <div><strong>Step:</strong> {{ translate_step(result.get('step', 'N/A')) }}</div>
                                <div><strong>Status:</strong> {{ result.get('status', 'N/A') }}</div>
                                <div>
                                    <strong>Type:</strong> {{ result.get('type', 'N/A') }}
                                    {% if result.get('type') == 'api' and result.get('log_id') %}
                                    <span class="api-log-link" onclick="viewLogDetail('{{ result.log_id }}')">Xem chi
                                        tiết
                                        API</span>
                                    {% endif %}
                                    {% if result.get('step') == 'step_update_status_form_builder' and
                                    result.get('result')
                                    %}
                                    <span class="api-log-link"
                                        onclick="viewFormBuilderDetail('{{ result.result|tojson|safe }}')">Xem chi tiết
                                        Form
                                        Builder</span>
                                    {% endif %}
                                </div>
                                {% if result.get('log_id') %}
                                <div><strong>Log ID:</strong> {{ result.log_id }}</div>
                                {% endif %}

                                {% if result.get('result') %}
                                <div><strong>Result:</strong></div>
                                <pre>{{ result.result|tojson(indent=2) }}</pre>
                                {% endif %}
                            </div>
                            {% endfor %}
                            {% else %}
                            <div>No results available for this step.</div>
                            {% endif %}

                            {% if step_data.get('next_step') %}
                            <div class="next-step">
                                <div class="next-step-label">Next Step:</div>
                                <div class="next-step-value">
                                    {{ translate_step(step_data.next_step.get('step', 'N/A')) }}
                                </div>
                                {% if step_data.next_step.get('type') %}
                                <div><strong>Type:</strong> {{ step_data.next_step.type }}</div>
                                {% endif %}
                                {% if step_data.next_step.get('log_id') %}
                                <div><strong>Log ID:</strong> {{ step_data.next_step.log_id }}</div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="no-data">
            {% if search_message %}
            {{ search_message }}
            {% else %}
            No data logs available. Please check your database or try a different search.
            {% endif %}
        </div>
        {% endif %}

        <!-- Modal for log details -->
        <div id="logDetailModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Chi tiết log</h2>
                    <span class="close" onclick="closeModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="tab">
                        <button class="tablinks active" onclick="openTab(event, 'headerTab')">Headers</button>
                        <button class="tablinks" onclick="openTab(event, 'payloadTab')">Payload</button>
                        <button class="tablinks" onclick="openTab(event, 'responseTab')">Response</button>
                        <button class="tablinks" onclick="openTab(event, 'infoTab')">Thông tin</button>
                        <button class="tablinks" onclick="openTab(event, 'curlTab')">CURL</button>
                    </div>

                    <div id="headerTab" class="tabcontent" style="display: block;">
                        <div id="loading-headers" class="loading">Đang tải...</div>
                        <pre id="headersContent"></pre>
                    </div>

                    <div id="payloadTab" class="tabcontent">
                        <div id="loading-payload" class="loading">Đang tải...</div>
                        <pre id="payloadContent"></pre>
                    </div>

                    <div id="responseTab" class="tabcontent">
                        <div id="loading-response" class="loading">Đang tải...</div>
                        <pre id="responseContent"></pre>
                    </div>

                    <div id="infoTab" class="tabcontent">
                        <div id="loading-info" class="loading">Đang tải dữ liệu...</div>
                        <div id="infoContent"></div>
                    </div>

                    <div id="curlTab" class="tabcontent">
                        <div id="loading-curl" class="loading">Đang tải dữ liệu...</div>
                        <div class="curl-container">
                            <div class="curl-header">
                                <h3>CURL Command</h3>
                                <button id="copy-curl-btn" onclick="copyCurlCommand()" class="copy-btn">Copy
                                    CURL</button>
                            </div>
                            <pre id="curlContent"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Step key to friendly name mapping
            const stepLabels = {
                'step_submit_form': 'Bước submit form',
                'step_create_cif': 'Bước tạo CIF',
                'step_create_account_indiv': 'Bước mở tài khoản thanh toán',
                'step_register_edigi_account': 'Bước đăng ký tài khoản EDIGI',
                'step_insert_in4_dsa': 'Bước ghi nhận KPI',
                'step_update_data_to_crm': 'Bước cập nhật dữ liệu về CRM',
                'step_end_flow': 'Kết thúc',
                // Add more mappings as needed
            };

            function getStepLabel(stepKey) {
                return stepLabels[stepKey] || stepKey;
            }

            function toggleStepContent(el) {
                const content = el.nextElementSibling;
                content.classList.toggle('active');
            }

            // Automatically expand the first step
            document.addEventListener('DOMContentLoaded', function () {
                const firstHeader = document.querySelector('.step-header');
                if (firstHeader) {
                    const firstContent = firstHeader.nextElementSibling;
                    if (firstContent) {
                        firstContent.classList.add('active');
                    }
                }
            });

            // Modal functions
            function closeModal() {
                document.getElementById('logDetailModal').style.display = 'none';
            }

            function openTab(evt, tabName) {
                // Hide all tabcontent
                const tabcontents = document.getElementsByClassName('tabcontent');
                for (let i = 0; i < tabcontents.length; i++) {
                    tabcontents[i].style.display = 'none';
                }

                // Remove active class from all tablinks
                const tablinks = document.getElementsByClassName('tablinks');
                for (let i = 0; i < tablinks.length; i++) {
                    tablinks[i].className = tablinks[i].className.replace(' active', '');
                }

                // Show the current tab and add an active class
                document.getElementById(tabName).style.display = 'block';
                evt.currentTarget.className += ' active';
            }

            // Function to fetch and display log details
            function viewLogDetail(logId) {
                // Show modal
                document.getElementById('logDetailModal').style.display = 'block';

                // Show loading indicators
                document.getElementById('loading-headers').style.display = 'block';
                document.getElementById('loading-payload').style.display = 'block';
                document.getElementById('loading-response').style.display = 'block';
                document.getElementById('loading-info').style.display = 'block';
                document.getElementById('loading-curl').style.display = 'block';

                // Clear previous content
                document.getElementById('headersContent').textContent = '';
                document.getElementById('payloadContent').textContent = '';
                document.getElementById('responseContent').textContent = '';
                document.getElementById('infoContent').innerHTML = '';
                document.getElementById('curlContent').textContent = '';

                // Cách đơn giản hơn: Gọi trực tiếp API bằng URL tương đối
                const apiUrl = `/pbuser/mobilebackend/mockup-data/api/v1/log-detail?log_id=${logId}`;
                console.log('Calling API:', window.location.origin + apiUrl);

                // Hiển thị URL đang gọi để debugging
                document.getElementById('infoContent').innerHTML = `<p>Calling API: ${window.location.origin}${apiUrl}</p>`;

                // Thêm header Accept để yêu cầu server trả về JSON
                const fetchOptions = {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                };

                fetch(apiUrl, fetchOptions)
                    .then(response => {
                        console.log('Response status:', response.status);
                        console.log('Response headers:', response.headers);

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        // Kiểm tra contentType để đảm bảo nhận được JSON
                        const contentType = response.headers.get('content-type');
                        console.log('Content-Type:', contentType);

                        if (!contentType || !contentType.includes('application/json')) {
                            console.error('Response is not JSON:', contentType);
                            return response.text().then(text => {
                                console.error('Response text:', text.substring(0, 500));
                                throw new Error(`Response is not JSON. Content-Type: ${contentType}, Body preview: ${text.substring(0, 100)}...`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Hide loading indicators
                        document.getElementById('loading-headers').style.display = 'none';
                        document.getElementById('loading-payload').style.display = 'none';
                        document.getElementById('loading-response').style.display = 'none';
                        document.getElementById('loading-info').style.display = 'none';
                        document.getElementById('loading-curl').style.display = 'none';

                        console.log('API response:', data);

                        if (data.success === true || data.status === 'success') {
                            // Lấy dữ liệu từ phản hồi (có thể nằm trong data.data hoặc chỉ là data)
                            const responseData = data.data || data;

                            // Display headers
                            document.getElementById('headersContent').textContent =
                                JSON.stringify(responseData.headers || {}, null, 2);

                            // Display payload
                            document.getElementById('payloadContent').textContent =
                                JSON.stringify(responseData.payload || {}, null, 2);

                            // Display response
                            document.getElementById('responseContent').textContent =
                                JSON.stringify(responseData.response || {}, null, 2);

                            // Display info
                            const infoHtml = `
                            <p><strong>Loại:</strong> ${responseData.type || 'N/A'}</p>
                            <p><strong>Thời gian response:</strong> ${responseData.time_request || 'N/A'}</p>
                            <p><strong>Thời gian thực hiện:</strong> ${responseData.action_time || 'N/A'}</p>
                            <p><strong>URI Request:</strong> ${responseData.config?.uri || 'N/A'}</p>
                        `;
                            document.getElementById('infoContent').innerHTML = infoHtml;

                            // Display curl command
                            const curlCommand = responseData.curl_command || 'Không có CURL command';
                            document.getElementById('curlContent').textContent = curlCommand;
                        } else {
                            // Display error message
                            const errorMessage = data.message || data.error || 'Unknown error';
                            document.getElementById('headersContent').textContent = 'Error loading data: ' + errorMessage;
                            document.getElementById('payloadContent').textContent = 'Error loading data: ' + errorMessage;
                            document.getElementById('responseContent').textContent = 'Error loading data: ' + errorMessage;
                            document.getElementById('infoContent').innerHTML = `<p>Error: ${errorMessage}</p>`;
                            document.getElementById('curlContent').textContent = 'Error loading data: ' + errorMessage;
                        }
                    })
                    .catch(error => {
                        // Hide loading indicators
                        document.getElementById('loading-headers').style.display = 'none';
                        document.getElementById('loading-payload').style.display = 'none';
                        document.getElementById('loading-response').style.display = 'none';
                        document.getElementById('loading-info').style.display = 'none';
                        document.getElementById('loading-curl').style.display = 'none';

                        console.error('Error fetching log details:', error);

                        // Display error message
                        const errorMessage = `Error fetching log details: ${error.message}`;
                        document.getElementById('headersContent').textContent = errorMessage;
                        document.getElementById('payloadContent').textContent = errorMessage;
                        document.getElementById('responseContent').textContent = errorMessage;
                        document.getElementById('infoContent').innerHTML = `<p>${errorMessage}</p>`;
                        document.getElementById('curlContent').textContent = errorMessage;
                    });
            }

            // Function to display Form Builder detail directly from the result data
            function viewFormBuilderDetail(resultData) {
                // Show modal
                document.getElementById('logDetailModal').style.display = 'block';

                // Hide loading indicators
                document.getElementById('loading-headers').style.display = 'none';
                document.getElementById('loading-payload').style.display = 'none';
                document.getElementById('loading-response').style.display = 'none';
                document.getElementById('loading-info').style.display = 'none';
                document.getElementById('loading-curl').style.display = 'none';

                // Clear previous content
                document.getElementById('headersContent').textContent = '';
                document.getElementById('payloadContent').textContent = '';
                document.getElementById('responseContent').textContent = '';
                document.getElementById('infoContent').innerHTML = '';
                document.getElementById('curlContent').textContent = '';

                console.log('Form Builder data:', resultData);

                // Trích xuất dữ liệu form builder
                const requestData = resultData?.request || {};
                const responseData = resultData?.response || {};

                // Display request payload
                if (requestData) {
                    document.getElementById('payloadContent').textContent =
                        JSON.stringify(requestData, null, 2);
                } else {
                    document.getElementById('payloadContent').textContent = 'Không có dữ liệu request';
                }

                // Display response
                if (responseData) {
                    document.getElementById('responseContent').textContent =
                        JSON.stringify(responseData, null, 2);
                } else {
                    document.getElementById('responseContent').textContent = 'Không có dữ liệu response';
                }

                // Display headers (if available)
                if (requestData?.headers) {
                    document.getElementById('headersContent').textContent =
                        JSON.stringify(requestData.headers, null, 2);
                } else {
                    document.getElementById('headersContent').textContent = 'Không có thông tin headers';
                }

                // Display info
                const infoHtml = `
                <p><strong>Loại:</strong> Form Builder</p>
                <p><strong>Step:</strong> step_update_status_form_builder</p>
                <p><strong>Form ID:</strong> ${requestData?.id || requestData?.formId || 'N/A'}</p>
                <p><strong>Status:</strong> ${responseData?.status || 'N/A'}</p>
            `;
                document.getElementById('infoContent').innerHTML = infoHtml;
            }

            // Function to copy curl command to clipboard
            function copyCurlCommand() {
                const curlContent = document.getElementById('curlContent');
                const btnCopy = document.getElementById('copy-curl-btn');
                const originalText = btnCopy.textContent;

                // Create a temporary textarea element to copy text
                const textarea = document.createElement('textarea');
                textarea.value = curlContent.textContent;
                document.body.appendChild(textarea);
                textarea.select();

                try {
                    // Execute copy command
                    document.execCommand('copy');
                    btnCopy.textContent = 'Copied!';
                    setTimeout(() => {
                        btnCopy.textContent = originalText;
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy: ', err);
                    btnCopy.textContent = 'Failed to copy';
                    setTimeout(() => {
                        btnCopy.textContent = originalText;
                    }, 2000);
                }

                // Remove the temporary textarea
                document.body.removeChild(textarea);
            }

            // Close modal when clicking outside of it
            window.onclick = function (event) {
                const modal = document.getElementById('logDetailModal');
                if (event.target == modal) {
                    modal.style.display = 'none';
                }
            }
        </script>
    </div>

    <script>
        // Kiểm tra xác thực ngay khi trang được tải
        document.addEventListener('DOMContentLoaded', function () {
            checkAuth();
        });

        // Hàm kiểm tra xác thực
        function checkAuth() {
            const isAuthenticated = localStorage.getItem('logFlowAuthenticated');
            if (isAuthenticated === 'true') {
                document.getElementById('authModal').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
            } else {
                document.getElementById('authModal').style.display = 'block';
                document.getElementById('mainContent').style.display = 'none';
            }
        }

        // Hàm xác thực người dùng
        function authenticate() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            const credentials = btoa(username + ':' + password);
            const validToken = 'bW9iaW86TXVhdGh1QDIwMjU=';

            if (credentials === validToken) {
                localStorage.setItem('logFlowAuthenticated', 'true');
                document.getElementById('authModal').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                document.getElementById('authError').style.display = 'none';
            } else {
                document.getElementById('authError').style.display = 'block';
                localStorage.removeItem('logFlowAuthenticated');
            }
        }

        // Thêm xử lý phím Enter cho form đăng nhập
        document.getElementById('password').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                authenticate();
            }
        });
    </script>
</body>

</html>