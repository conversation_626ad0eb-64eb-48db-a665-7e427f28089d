#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 15/04/2024
"""


import uuid

from mobio.libs.logging import MobioLogging
from mobio.sdks.admin.utils import KafkaProducerManager

from configs.kafka_config import KAFKA_TOPIC


class Producer(KafkaProducerManager):

    def send_message_to_topic(self, topic_name, data_send, key_uuid=None):
        if not key_uuid:
            key_uuid = str(uuid.uuid1())
        self.flush_message(topic=topic_name, value=data_send, key=key_uuid)
        return key_uuid

    def push_message_push_socket_notify_mobile(self, merchant_id, account_id, message_type, data_send):
        try:
            message_build = {
                "merchant_id": merchant_id,
                "account_id": account_id,
                "message_type": message_type,
                "data_send": data_send,
            }
            self.send_message_to_topic(KAFKA_TOPIC.PUSH_SOCKET_NOTIFY_MOBILE, message_build)
        except Exception as ex:
            MobioLogging().error("HandleQueue :: push_message_profile_reply_sms_confirm_email :: error :: %s" % ex)

    def push_message_to_request_third_party(self, merchant_id, account_id, action_time, data_send):
        try:
            data_send = {
                "merchant_id": merchant_id,
                "account_id": account_id,
                "action_time": action_time,
                **data_send,
            }
            self.send_message_to_topic(KAFKA_TOPIC.REQUEST_TO_THIRD_PARTY, data_send)
            # MobioLogging().info("HandleQueue :: message :: {}".format(data_send))
        except Exception as ex:
            MobioLogging().error("HandleQueue :: push_message_to_request_third_party :: error :: %s" % ex)

    def push_message_to_request_check_face(self, merchant_id, account_id, action_time, data_send):
        try:
            data_send = {
                "merchant_id": merchant_id,
                "account_id": account_id,
                "action_time": action_time,
                **data_send,
            }
            self.send_message_to_topic(KAFKA_TOPIC.REQUEST_TO_THIRD_PARTY, data_send)
            # MobioLogging().info("HandleQueue :: message :: {}".format(data_send))
        except Exception as ex:
            MobioLogging().error("HandleQueue :: push_message_to_request_check_face :: error :: %s" % ex)

    def push_message_to_request_citizen_ids_read_only_information(
        self, merchant_id, account_id, action_time, data_send
    ):
        try:
            data_send = {
                "merchant_id": merchant_id,
                "account_id": account_id,
                "action_time": action_time,
                **data_send,
            }
            self.send_message_to_topic(KAFKA_TOPIC.QUICK_SALES_READ_CARD, data_send)
            # MobioLogging().info("HandleQueue :: message :: {}".format(data_send))
        except Exception as ex:
            MobioLogging().error(
                "HandleQueue :: push_message_to_request_citizen_ids_read_only_information :: error :: %s" % ex
            )

    def push_message_to_build_information_related_profile(self, merchant_id, account_id, action_time, data_send):
        try:
            data_send = {
                "merchant_id": merchant_id,
                "account_id": account_id,
                "action_time": action_time,
                **data_send,
            }
            self.send_message_to_topic(KAFKA_TOPIC.HANDLE_BUILD_INFORMATION_RELATED_PROFILE, data_send)
            # MobioLogging().info("HandleQueue :: message :: {}".format(data_send))
        except Exception as ex:
            MobioLogging().error("HandleQueue :: push_message_to_build_information_related_profile :: error :: %s" % ex)
