#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 18/02/2025
"""

from copy import copy
import time
import uuid

from mobio.libs.logging import MobioLogging
from mobio.sdks.notify import MobioNotifySDK

from configs.kafka_config import KAFKA_TOPIC
from src.common import ConstantQuickSales, KeyConfigNotifySDK, StatusCode
from src.common.utils import get_datetime_utc_to_format_datetime
from src.helpers.internal.mobio.form_builder import FormBuilderHelper
from src.helpers.internal.mobio.profiling import ProfilingHelper
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.helpers.thirdparty.eib.send_request_save_customer import (
    SendRequestSaveCustomer,
)
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.helpers.thirdparty.quick_sales.func_helper_transform_data import (
    FuncHelperTransformData,
)
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)


class CsmResCifConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        CsmResCifConsumer.handler_data(payload)

    @staticmethod
    def _build_body_save_customer_to_edigi(merchant_id, staff_id, action_time, cif_id, form_data_submit):

        body_save_customer = form_data_submit.get("body_save_customer", {})

        if not body_save_customer:
            return {}
        body_save_customer["cif"] = cif_id
        if "placeOfIssuance" in body_save_customer:
            body_save_customer.pop("placeOfIssuance")
        if "codePlaceOfIssuance" in body_save_customer:
            body_save_customer.pop("codePlaceOfIssuance")
        if "codePlaceOfIssuance" in body_save_customer:
            body_save_customer.pop("codePlaceOfIssuance")

        data_result = FuncHelperTransformData(merchant_id).transform_data_save_customer_to_edigi(form_data_submit)
        body_save_customer.update(data_result)
        return body_save_customer

    @staticmethod
    def handler_data(payload):
        reason = None
        status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.PROCESSING
        result_step_handle_create_cif = {
            "result_step_current": [],
            "action_time": get_datetime_utc_to_format_datetime(),
            "next_step": {
                "step": ConstantQuickSales.ConstantStepFlow.STEP_END_FLOW,
                "action_time": get_datetime_utc_to_format_datetime(),
            },
        }
        profile_name = ""

        try:
            MobioLogging().info("CsmResCifConsumer :: payload %s" % payload)
            # TODO: send to topic res create cif
            merchant_id = payload.get("merchant_id")
            staff_id = payload.get("staff_id")
            action_time = payload.get("action_time")

            log_customer_full_flow_id = payload.get("log_customer_full_flow_id")

            detail_log_customer_full_flow = (
                LogCustomerFullFlowQuickSalesModel().get_log_customer_full_flow_quick_sales_by_id_cache(
                    log_customer_full_flow_id
                )
            )
            if not detail_log_customer_full_flow:
                raise Exception("detail_log_customer_full_flow is required")
            profile_id = detail_log_customer_full_flow.get("object_id")
            form_id = detail_log_customer_full_flow.get("form_id")

            form_data_submit = detail_log_customer_full_flow.get("form_data_submit", {})
            profile_name = form_data_submit.get("profile", {}).get("name", "")

            info_customer_data = FuncHelperTransformData(merchant_id).transform_data_from_submit_data_to_create_cif(
                form_data_submit,
            )
            
            dsa_id = FuncHelperTransformData(merchant_id).get_dsa_id_from_staff_info(form_data_submit.get("staff_info", {}))
            if not dsa_id:
                raise Exception("Không tìm thấy mã DSAID trong hệ thống, CBBH vui lòng kiểm tra lại thông tin")
            info_customer_data.update({"DSAId": dsa_id})

            MobioLogging().info("CsmResCifConsumer :: info_customer_data %s" % info_customer_data)

            config_send_request_create_cif = QuickSalesHelper()._get_create_cif_config(merchant_id)
            MobioLogging().info(
                "CsmResCifConsumer :: config_send_request_create_cif %s" % config_send_request_create_cif
            )
            second_delay_next_request = config_send_request_create_cif.get("second_delay_next_request", 0)
            number_retry_request = config_send_request_create_cif.get("number_retry_request", 3)

            response_create_cif, status_code, message, log_request_id = QuickSalesHelper().send_request_create_cif(
                merchant_id, staff_id, action_time, info_customer_data
            )
            MobioLogging().info("CsmResCifConsumer :: response_create_cif %s" % response_create_cif)
            MobioLogging().info("CsmResCifConsumer :: status_code %s" % status_code)
            MobioLogging().info("CsmResCifConsumer :: message %s" % message)
            MobioLogging().info("CsmResCifConsumer :: log_request_id %s" % log_request_id)

            # TODO: retry send request create cif
            while True and status_code != StatusCode.SUCCESS and (number_retry_request and number_retry_request > 0):
                MobioLogging().info("CsmResCifConsumer :: retry send request create cif :: %s" % number_retry_request)
                response_create_cif, status_code, message, log_request_id = QuickSalesHelper().send_request_create_cif(
                    merchant_id, staff_id, action_time, info_customer_data
                )
                MobioLogging().info("CsmResCifConsumer :: info_customer_data %s" % info_customer_data)
                number_retry_request -= 1
                time.sleep(1)

            if status_code == StatusCode.SUCCESS:
                profile_cif = response_create_cif.get("CustId")
                result_update_profile = {
                    "status": ConstantQuickSales.ConstantStatusFormBuilder.SUCCESS,
                    "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                }
                if profile_cif:
                    r_update_profile = ProfilingHelper().update_cif_profile_by_profile_id(
                        merchant_id, profile_id, profile_cif
                    )
                    try:
                        r_update_profile = r_update_profile.json()
                        MobioLogging().info(
                            "CsmQuickSalesUpsertDataToCrmConsumer :: r_update_profile :: {}".format(r_update_profile)
                        )
                    except Exception:
                        MobioLogging().error("CsmQuickSalesUpsertDataToCrmConsumer :: r_update_profile not found")
                        r_update_profile = None

                    if not r_update_profile:
                        MobioLogging().error("CsmQuickSalesUpsertDataToCrmConsumer :: r_update_profile not found")
                        result_update_profile = {
                            "status": ConstantQuickSales.ConstantStatusFormBuilder.FAIL,
                            "reasons": "Lỗi khi cập nhật profile",
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                        }
                try:
                    body_save_customer = CsmResCifConsumer._build_body_save_customer_to_edigi(
                        merchant_id, staff_id, action_time, profile_cif, form_data_submit
                    )
                    data_response_save_customer = None
                    status_code_save_customer = StatusCode.THIRD_PARTY_FAILURE
                    reasons_save_customer = None

                    if body_save_customer:
                        # TODO: send to topic res save customer
                        data, config = SendRequestSaveCustomer()._build(
                            merchant_id, {"body_save_customer": body_save_customer}
                        )
                        (
                            data_response_save_customer,
                            status_code_save_customer,
                            reasons_save_customer,
                        ) = ThirdPartyEIB().save_customer(
                            str(uuid.uuid4()), config, data, log_id_start=str(uuid.uuid4())
                        )
                except Exception as e:
                    MobioLogging().error("CsmResCifConsumer :: error %s" % e)
                    data_response_save_customer = None
                    status_code_save_customer = StatusCode.THIRD_PARTY_FAILURE
                    reasons_save_customer = str(e)

                if second_delay_next_request and second_delay_next_request > 0:
                    MobioLogging().info(
                        "CsmResCifConsumer :: sleep %s seconds before send to topic res create account"
                        % second_delay_next_request
                    )
                    time.sleep(second_delay_next_request)

                # TODO: send to topic res create account
                log_topic_kafka_id = Producer().send_message_to_topic(
                    KAFKA_TOPIC.QUICK_SALES_CREATE_ACC,
                    {
                        "log_customer_full_flow_id": log_customer_full_flow_id,
                        "merchant_id": merchant_id,
                        "staff_id": staff_id,
                        "action_time": action_time,
                        "result_of_previous_step": response_create_cif,
                    },
                )
                
                # Add action_time to response_create_cif
                response_create_cif["action_time"] = action_time
                form_data_submit["response_create_cif"] = response_create_cif
                MobioLogging().info("CsmResCifConsumer :: log_topic_kafka_id {}".format(log_topic_kafka_id))
                
                result_step_handle_create_cif.update(
                    {
                        "status_step": ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS,
                        "result_step_current": [
                            {
                                "result": response_create_cif,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "log_id": log_request_id,
                                "status": ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS,
                                "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF,
                            },
                            {
                                "result": r_update_profile,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "log_id": log_request_id,
                                "status": result_update_profile.get(
                                    "status", ConstantQuickSales.ConstantStatusStepFlowQuickSales.FAIL
                                ),
                                "step": "update_cif_to_profile",
                            },
                            {
                                "result": data_response_save_customer,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "log_id": log_request_id,
                                "status": (
                                    ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS
                                    if status_code_save_customer == StatusCode.SUCCESS
                                    else ConstantQuickSales.ConstantStatusStepFlowQuickSales.FAIL
                                ),
                                "reasons": reasons_save_customer,
                                "step": "save_customer_to_edigi",
                            },
                        ],
                        "next_step": {
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_ACCOUNT_INDIV,
                            "log_id": log_topic_kafka_id,
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.QUEUE_KAFKA,
                        },
                    }
                )
                LogCustomerFullFlowQuickSalesModel().update_log_customer_full_flow_quick_sales(
                    log_customer_full_flow_id,
                    {
                        ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF: result_step_handle_create_cif,
                        "status": status_handle_full_flow,
                        "form_data_submit": form_data_submit,
                    },
                )

            else:
                status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL
                # Update trạng thái tạo cif bị fail cho FormBuilder
                result_update_form_builder = FormBuilderHelper().update_status_form_landing_page_ekyc_profile(
                    merchant_id,
                    form_id,
                    ConstantQuickSales.ConstantStatusFormBuilder.CIF_CREATE_ERROR,
                    profile_id,
                    message,
                )
                result_step_handle_create_cif.update(
                    {
                        "status_step": ConstantQuickSales.ConstantStatusStepFlowQuickSales.FAIL,
                        "result_step_current": [
                            {
                                "status": ConstantQuickSales.ConstantStatusStepFlowQuickSales.FAIL,
                                "reasons": message,
                                "log_id": log_request_id,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF,
                            },
                            {
                                "status": ConstantQuickSales.ConstantStatusFormBuilder.SUCCESS,
                                "reasons": message,
                                "log_id": log_request_id,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "step": ConstantQuickSales.ConstantStepFlow.STEP_UPDATE_STATUS_FORM_BUILDER,
                                "result": {
                                    "headersContent": {},
                                    "payloadContent": {
                                        "merchant_id": merchant_id,
                                        "form_id": form_id,
                                        "profile_id": profile_id,
                                        "message": message,
                                        "status": ConstantQuickSales.ConstantStatusFormBuilder.CIF_CREATE_ERROR,
                                    },
                                    "responseContent": result_update_form_builder,
                                    "infoContent": {},
                                },
                            },
                        ],
                        "result_update_form_builder": result_update_form_builder,
                    }
                )
        except Exception as e:
            MobioLogging().error("CsmResCifConsumer :: error %s" % e)
            status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL
            reason = str(e)
        LogCustomerFullFlowQuickSalesModel().update_log_customer_full_flow_quick_sales(
            log_customer_full_flow_id,
            {
                ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF: result_step_handle_create_cif,
                "status": status_handle_full_flow,
                "reason": reason,
                "form_data_submit": form_data_submit,
            },
        )

        if status_handle_full_flow == ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL:

            text_send = "Khách hàng {} đã đăng ký CIF và SPDV không thành công. Vui lòng hỗ trợ khách hàng đăng ký CIF và SPDV tại quầy.".format(
                profile_name
            )

            data_send = {
                "staff_id": staff_id,
                "form_id": detail_log_customer_full_flow.get("form_id"),
                "status": status_handle_full_flow,
                "object_id": detail_log_customer_full_flow.get("object_id"),
                "object_type": detail_log_customer_full_flow.get("object_type"),
                "object_name": profile_name,
            }

            # Push socket
            MobioNotifySDK().send_message_notify_push_id_mobile_app(
                merchant_id=merchant_id,
                key_config="notify_default",
                account_ids=[staff_id],
                socket_type=KeyConfigNotifySDK.MOBILEBACKEND_RESULT_QUICK_SALES,
                title=text_send,
                content=text_send,
                **data_send,
            )


if __name__ == "__main__":
    CsmResCifConsumer.handler_data(
        {
            "log_customer_full_flow_id": "6848ee4dd71d47204cff878f",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "staff_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "action_time": "2025-06-07 07:49:58",
        }
    )
