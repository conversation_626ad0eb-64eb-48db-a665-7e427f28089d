#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/04/2024
"""


from mobio.libs.logging import MobioLogging

from src.common import ConstantMessageSendRequestToThirdParty, ThirdPartyType
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer
from src.helpers.thirdparty.eib.send_request_check_card import SendRequestCheckCard
from src.helpers.thirdparty.eib.send_request_check_customer_exist import (
    SendRequestCheckCustomerExist,
)
from src.helpers.thirdparty.eib.send_request_check_face import SendRequestCheckFace
from src.helpers.thirdparty.eib.send_request_check_unique_id import (
    SendRequestCheckUniqueId,
)
from src.helpers.thirdparty.eib.send_request_id_check_insert_data_log import (
    SendRequestIdCheckInsertDataLog,
)
from src.helpers.thirdparty.eib.send_request_insert_id_check import (
    SendRequestInsertIdCheck,
)
from src.helpers.thirdparty.eib.send_request_read_card import SendRequestReadCard
from src.helpers.thirdparty.eib.send_request_save_customer import (
    SendRequestSaveCustomer,
)
from src.helpers.thirdparty.eib.send_request_sms_confirm_consent import (
    SendSmsConfirmConsent,
)


class SendRequestToThirdPartyConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        SendRequestToThirdPartyConsumer.handler_data(payload)

    @staticmethod
    def handler_data(payload):
        MobioLogging().info("SendRequestToThirdPartyConsumer :: payload %s" % payload)
        merchant_id = payload.get(ConstantMessageSendRequestToThirdParty.MERCHANT_ID)
        request_type = payload.get(ConstantMessageSendRequestToThirdParty.REQUEST_TYPE)
        account_id = payload.get(ConstantMessageSendRequestToThirdParty.ACCOUNT_ID)
        action_time = payload.get(ConstantMessageSendRequestToThirdParty.ACTION_TIME)
        log_id = payload.get(ConstantMessageSendRequestToThirdParty.LOG_ID)
        log_id_start = payload.get(ConstantMessageSendRequestToThirdParty.LOG_ID_START)
        request_body = payload.get(ConstantMessageSendRequestToThirdParty.REQUEST_BODY)
        flow_name = payload.get(ConstantMessageSendRequestToThirdParty.FLOW_NAME)
        try:
            if request_type not in ThirdPartyType.get_all_attribute():
                raise Exception("Request type {} not supported".format(request_type))
            if request_type == ThirdPartyType.SEND_SMS_CONFIRM_CONSENT:
                SendSmsConfirmConsent._send(
                    log_id=log_id,
                    merchant_id=merchant_id,
                    request_body=request_body,
                    account_id=account_id,
                    action_time=action_time,
                    flow_name=flow_name,
                )

            if request_type == ThirdPartyType.SEND_REQUEST_READ_CARD:
                SendRequestReadCard._send(
                    log_id=log_id,
                    merchant_id=merchant_id,
                    request_body=request_body,
                    account_id=account_id,
                    action_time=action_time,
                    flow_name=flow_name,
                    log_id_start=log_id_start,
                )

            if request_type == ThirdPartyType.SEND_REQUEST_CHECK_CARD:
                SendRequestCheckCard._send(
                    log_id=log_id,
                    merchant_id=merchant_id,
                    request_body=request_body,
                    account_id=account_id,
                    action_time=action_time,
                    log_id_start=log_id_start,
                    flow_name=flow_name,
                )

            if request_type == ThirdPartyType.SEND_REQUEST_CHECK_FACE:
                SendRequestCheckFace._send(
                    log_id=log_id,
                    merchant_id=merchant_id,
                    request_body=request_body,
                    account_id=account_id,
                    action_time=action_time,
                    log_id_start=log_id_start,
                    flow_name=flow_name,
                )

            if request_type == ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_EXIST:
                SendRequestCheckCustomerExist._send(
                    log_id=log_id,
                    merchant_id=merchant_id,
                    request_body=request_body,
                    account_id=account_id,
                    action_time=action_time,
                    log_id_start=log_id_start,
                    flow_name=flow_name,
                )
            if request_type == ThirdPartyType.SEND_REQUEST_SAVE_CUSTOMER:
                SendRequestSaveCustomer._send(
                    log_id=log_id,
                    merchant_id=merchant_id,
                    request_body=request_body,
                    account_id=account_id,
                    action_time=action_time,
                    log_id_start=log_id_start,
                    flow_name=flow_name,
                )
            if request_type == ThirdPartyType.SEND_REQUEST_ID_CHECK_INSERT_DATA_LOG:
                SendRequestIdCheckInsertDataLog._send(
                    log_id=log_id,
                    merchant_id=merchant_id,
                    request_body=request_body,
                    account_id=account_id,
                    log_id_start=log_id_start,
                )
            if request_type == ThirdPartyType.SEND_REQUEST_INSERT_ID_CHECK:
                SendRequestInsertIdCheck._send(
                    log_id=log_id,
                    merchant_id=merchant_id,
                    request_body=request_body,
                    account_id=account_id,
                    action_time=action_time,
                    log_id_start=log_id_start,
                    flow_name=flow_name,
                )
            if request_type == ThirdPartyType.SEND_REQUEST_CHECK_UNIQUE_ID:
                SendRequestCheckUniqueId._send(
                    log_id=log_id,
                    merchant_id=merchant_id,
                    request_body=request_body,
                    account_id=account_id,
                    action_time=action_time,
                    log_id_start=log_id_start,
                    flow_name=flow_name,
                )
        except Exception as ex:
            MobioLogging().error("SendRequestToThirdPartyConsumer :: error: %s", str(ex))


if __name__ == "__main__":
    x8 = {
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "account_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "action_time": "2025-06-10 23:32:16",
        "log_id": "2508017b-4653-11f0-b251-fb6b23261e00",
        "log_id_start": "e5662693-13ee-436a-9d1b-701733902cae",
        "flow_name": "quick_sales",
        "request_body": {
            "data_decryption": "+3WZEsyAdiNv3ci/V0Wnk/ZtHahE7MhTX1rrUmKCFzpVmg3ClznW1FcbW8LgbBo8I2UWpBKS1wFXMSoirNQZgXXy3i28wed8mot+JR7gpC85jdd/EN/kFvYfYgdfGG6Gl0nV0/l2AVAmRvfqxV0/HjOVr2d2hoqT3wvEv230NBX2AJajYq49uOR1WgbARApYgi2mSrYjV3fJafNng16I4oI7tYBzWk7RyhZUyBBTvHInm53KDqCY6XCl3v/wIBs32NRCeZ4fol8PyOkXKT5A6ih50nXip/KncT2rTLnM5QZ0D/mAYt31PN0ILrjmFcx1+QWLfiRfUg2OphKankrd68c+fSAT7IWAY4zGH1c2FA2eKsX+180pMgrN6qCmWzZnfOg1divL/lzu4VJMiOOhjkpzb/8yDcWyFBkjLbdb/rtx4Wxk7WjsFaxNzS9wyyVR0Xif",
            "sdk_request_round": "428=",
            "sdk_request_session": "eec959be-e624-4edc-8945-dc6352aa1e83",
            "request_timestamp": "*************",
            "sdk_request_id": "*************",
        },
        "request_type": "send_request_check_face",
    }
    SendRequestToThirdPartyConsumer.handler_data(x8)
