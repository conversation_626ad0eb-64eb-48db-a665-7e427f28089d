#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 15/04/2024
"""

from mobio.libs.logging import MobioLogging

from src.apis import MobioNotifySDK
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer


class ConstantMessageKey:
    MERCHANT_ID = "merchant_id"
    ACCOUNT_ID = "account_id"
    MESSAGE_TYPE = "message_type"
    DATA_SEND = "data_send"
    DATA_BUILD_FILE_EVIDENT = "data_build_file_evident"


class PushSocketNotifyMobileConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        PushSocketNotifyMobileConsumer.handler_data(payload)

    @staticmethod
    def handler_data(payload):
        MobioLogging().info("PushSocketNotifyMobileConsumer() :: process_msg :: %s" % payload)

        message_type = payload.get(ConstantMessageKey.MESSAGE_TYPE)

        data_send = payload.get(ConstantMessageKey.DATA_SEND)
        log_id = data_send.get("log_id")
        merchant_id = payload.get(ConstantMessageKey.MERCHANT_ID)
        account_id = payload.get(ConstantMessageKey.ACCOUNT_ID)
        MobioNotifySDK().send_message_notify_socket(
            merchant_id=merchant_id,
            key_config="notify_default",
            account_ids=[account_id],
            socket_type=message_type,
            **data_send,
        )
        MobioLogging().info("PushSocketNotifyMobileConsumer() :: log_id :: %s" % log_id)


if __name__ == "__main__":
    x = {
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "account_id": "2b81b084-2ecd-4548-aa08-64a509a79265",
        "message_type": "mobilebackend_successful_decoded_citizen_id",
        "data_send": {
            "data": {
                "status": "fail",
                "reason": "Connection broken: IncompleteRead(65429 bytes read, 316718 more expected), IncompleteRead(65429 bytes read, 316718 more expected)",
                "log_id": "dfc7be7a-0c2b-11ef-89e9-c3bd023e141c",
                "func_visit": "SendRequestReadCard",
            }
        },
        "data_build_file_evident": None,
    }
    PushSocketNotifyMobileConsumer.handler_data(x)