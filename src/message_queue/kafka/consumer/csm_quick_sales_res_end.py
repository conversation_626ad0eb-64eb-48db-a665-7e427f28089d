#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 18/02/2025
"""

from mobio.libs.logging import MobioLogging
from mobio.sdks.notify import MobioNotifySDK

from src.common import ConstantQuickSales, KeyConfigNotifySDK, StatusCode
from src.common.utils import get_datetime_utc_to_format_datetime
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.helpers.thirdparty.quick_sales.func_helper_transform_data import (
    FuncHelperTransformData,
)
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)

MobioNotifySDK().config(source="mobile-backend")


class CsmResEndConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        CsmResEndConsumer.handler_data(payload)

    @staticmethod
    def handler_data(payload):
        reason = None
        status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.SUCCESS
        result_step_handle = {
            "result_step_current": [],
            "action_time": get_datetime_utc_to_format_datetime(),
            "next_step": {
                "step": ConstantQuickSales.ConstantStepFlow.STEP_END_FLOW,
            },
        }

        try:
            MobioLogging().info("CsmResEndConsumer :: payload %s" % payload)

            merchant_id = payload.get("merchant_id")
            staff_id = payload.get("staff_id")
            action_time = payload.get("action_time")
            log_customer_full_flow_id = payload.get("log_customer_full_flow_id")

            detail_log_customer_full_flow = (
                LogCustomerFullFlowQuickSalesModel().get_log_customer_full_flow_quick_sales_by_id_cache(
                    log_customer_full_flow_id
                )
            )
            if not detail_log_customer_full_flow:
                raise Exception("detail_log_customer_full_flow is required")

            sol_id = InternalAdminHelper().get_sol_id_by_account_id(merchant_id, staff_id)
            if not sol_id:
                raise Exception("sol_id not found")

            form_data_submit = detail_log_customer_full_flow.get("form_data_submit", {})

            profile_data = form_data_submit.get(ConstantQuickSales.ObjectType.PROFILE, {})
            staff_info = form_data_submit.get("staff_info", {})

            data_insert_in4_dsa = {
                "sol_id": sol_id,
                "cif_id": form_data_submit.get("response_create_cif", {}).get("CustId", ""),
                "flg_id": "0",
                "emp_id": staff_info.get("staff_code", ""),
                "rbo_id": "",
            }

            data_transform = FuncHelperTransformData(merchant_id).transform_data_insert_in4_dsa(data_insert_in4_dsa)

            if data_transform:
                data_insert_in4_dsa.update(data_transform)

            # TODO: call api ghi nhận KPI
            data_response, status_code, message, log_request_id = QuickSalesHelper().send_request_insert_in4_dsa(
                merchant_id, staff_id, action_time, data_insert_in4_dsa
            )
            if status_code == StatusCode.SUCCESS:
                pass

            text_send = "Khách hàng {} đã đăng ký CIF và SPDV thành công. Vui lòng hướng dẫn khách hàng tải và đăng nhập Eximbank EDigi.".format(profile_data.get("name"))

            data_send = {
                "staff_id": staff_id,
                "form_id": detail_log_customer_full_flow.get("form_id"),
                "status": status_handle_full_flow,
                "object_id": detail_log_customer_full_flow.get("object_id"),
                "object_type": detail_log_customer_full_flow.get("object_type"),
                "object_name": profile_data.get("name"),
            }

            # Push socket
            MobioNotifySDK().send_message_notify_push_id_mobile_app(
                merchant_id=merchant_id,
                key_config="notify_default",
                account_ids=[staff_id],
                socket_type=KeyConfigNotifySDK.MOBILEBACKEND_RESULT_QUICK_SALES,
                title=text_send,
                content=text_send,
                **data_send,
            )
            result_step_handle.update(
                {
                    "status_step": ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS,
                    "result_step_current": [
                        {
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_INSERT_IN4_DSA,
                            "status": status_code,
                            "data": data_response,
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                            "log_id": log_request_id,
                        },
                        {
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_PUSH_NOTIFY_TO_ACCOUNT,
                            "status": ConstantQuickSales.ConstantStatusFlowQuickSales.SUCCESS,
                        },
                    ],
                },
            )
        except Exception as e:
            MobioLogging().error("CsmResEndConsumer :: error %s" % e)
            status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL
            reason = str(e)

        LogCustomerFullFlowQuickSalesModel().update_log_customer_full_flow_quick_sales(
            log_customer_full_flow_id,
            {
                ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_INSERT_IN4_DSA: result_step_handle,
                "status": status_handle_full_flow,
                "reason": reason,
            },
        )


if __name__ == "__main__":
    CsmResEndConsumer.handler_data(
        {
            "log_customer_full_flow_id": "68428f8501d26ca2ac007330",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "staff_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "action_time": "2025-06-06 13:58:30.705950",
        }
    )
