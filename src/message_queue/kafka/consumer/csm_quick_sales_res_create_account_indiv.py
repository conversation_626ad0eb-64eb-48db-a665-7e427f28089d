#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 18/02/2025
"""

import time

from mobio.libs.logging import MobioLogging
from mobio.sdks.notify import MobioNotifySDK

from configs.kafka_config import KAFKA_TOPIC
from src.common import ConstantQuickSales, KeyConfigNotifySDK, StatusCode
from src.common.utils import get_datetime_utc_to_format_datetime
from src.helpers.internal.mobio.form_builder import Form<PERSON>uilderHelper
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.helpers.thirdparty.quick_sales.func_helper_transform_data import (
    FuncHelperTransformData,
)
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)


class CsmResCreateAccConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        CsmResCreateAccConsumer.handler_data(payload)

    @staticmethod
    def validate_payload(payload):
        if not payload:
            raise Exception("payload is required")

        if not payload.get("log_customer_full_flow_id"):
            raise Exception("log_customer_full_flow_id is required")

    @staticmethod
    def handler_data(payload):
        reason = None
        status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.PROCESSING

        result_step_handle = {
            "result_step_current": [],
            "action_time": get_datetime_utc_to_format_datetime(),
            "next_step": {
                "step": ConstantQuickSales.ConstantStepFlow.STEP_END_FLOW,
                "action_time": get_datetime_utc_to_format_datetime(),
            },
        }
        log_customer_full_flow_id = payload.get("log_customer_full_flow_id")
        merchant_id = payload.get("merchant_id")
        staff_id = payload.get("staff_id")
        action_time = payload.get("action_time")
        profile_name = ""
        try:
            MobioLogging().info("CsmResCreateAccConsumer :: payload %s" % payload)

            CsmResCreateAccConsumer.validate_payload(payload)

            detail_log_customer_full_flow = (
                LogCustomerFullFlowQuickSalesModel().get_log_customer_full_flow_quick_sales_by_id_cache(
                    log_customer_full_flow_id
                )
            )

            if not detail_log_customer_full_flow:
                raise Exception("detail_log_customer_full_flow is required")

            form_data_submit = detail_log_customer_full_flow.get("form_data_submit", {})

            profile_id = detail_log_customer_full_flow.get("object_id")
            form_id = detail_log_customer_full_flow.get("form_id")
            profile_name = form_data_submit.get("profile", {}).get("name", "")

            # TODO: send to topic res create account
            data_create_account = FuncHelperTransformData(
                merchant_id
            ).transform_data_from_create_cif_to_create_account_indiv(form_data_submit)
            
            dsa_id = FuncHelperTransformData(merchant_id).get_dsa_id_from_staff_info(form_data_submit.get("staff_info", {}))
            if not dsa_id:
                raise Exception("Không tìm thấy mã DSAID trong hệ thống, CBBH vui lòng kiểm tra lại thông tin")
            data_create_account.update({"DSAID": dsa_id})

            account_number = data_create_account.get("LuckyAccNum")
            status_code_unlock = StatusCode.SUCCESS
            result_step_current = []
            if account_number:
                (
                    data_response_unlock,
                    status_code_unlock,
                    reasons_unlock,
                    log_request_id,
                ) = QuickSalesHelper().lock_unlock_account(
                    merchant_id,
                    staff_id,
                    account_number,
                    ConstantQuickSales.ConstantActionAccount.UNLOCK,
                )
                MobioLogging().info(
                    f"data_response_unlock: {data_response_unlock}, status_code_unlock: {status_code_unlock}, reasons_unlock: {reasons_unlock}"
                )
                result_step_current.append(
                    {
                        "result": data_response_unlock,
                        "status": status_code_unlock,
                        "reasons": reasons_unlock,
                        "step": "unlock_account",
                        "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                        "log_id": log_request_id,
                    }
                )
            # TODO: retry send request create account

            config_send_request_create_account = QuickSalesHelper()._get_config_ca_acct_add_indiv(merchant_id)
            second_delay_next_request = config_send_request_create_account.get("second_delay_next_request", 3)
            number_retry_request = config_send_request_create_account.get("number_retry_request", 3)
            number_retry_request = None

            (
                response_create_account,
                status_code,
                message,
                log_request_ca_acct_add_indiv_id,
            ) = QuickSalesHelper().send_request_ca_acct_add_indiv(
                merchant_id, staff_id, action_time, data_create_account
            )
            MobioLogging().info("CsmResCreateAccConsumer :: response_create_account %s" % response_create_account)
            MobioLogging().info("CsmResCreateAccConsumer :: status_code %s" % status_code)
            MobioLogging().info("CsmResCreateAccConsumer :: message %s" % message)
            # MobioLogging().info("CsmResCreateAccConsumer :: log_request_id %s" % log_request_id)

            while True and status_code != StatusCode.SUCCESS and (number_retry_request and number_retry_request > 0):
                MobioLogging().info(
                    "CsmResCreateAccConsumer :: retry send request create account :: %s" % number_retry_request
                )
                (
                    response_create_account,
                    status_code,
                    message,
                    log_request_ca_acct_add_indiv_id,
                ) = QuickSalesHelper().send_request_ca_acct_add_indiv(
                    merchant_id, staff_id, action_time, data_create_account
                )

                number_retry_request -= 1
                time.sleep(1)

            if status_code == StatusCode.SUCCESS:
                # TODO: send to topic res create account
                if second_delay_next_request and second_delay_next_request > 0:
                    MobioLogging().info(
                        "CsmResCreateAccConsumer :: sleep %s seconds before send to topic res register edigi account"
                        % second_delay_next_request
                    )
                    time.sleep(second_delay_next_request)

                log_topic_kafka_id = Producer().send_message_to_topic(
                    KAFKA_TOPIC.QUICK_SALES_REGISTER_EDIGI_ACC,
                    {
                        "log_customer_full_flow_id": log_customer_full_flow_id,
                        "merchant_id": merchant_id,
                        "staff_id": staff_id,
                        "action_time": action_time,
                        "result_of_previous_step": response_create_account,
                    },
                )
                # Add action_time to response_create_account
                response_create_account["action_time"] = action_time
                form_data_submit["response_create_account"] = response_create_account
                result_step_current.append(
                    {
                        "result": response_create_account,
                        "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                        "log_id": log_request_ca_acct_add_indiv_id,
                        "status": status_code,
                        "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_ACCOUNT_INDIV,
                    }
                )
                result_step_handle.update(
                    {
                        "status_step": ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS,
                        "result_step_current": result_step_current,
                        "next_step": {
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_REGISTER_EDIGI_ACCOUNT,
                            "log_id": log_topic_kafka_id,
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.QUEUE_KAFKA,
                        },
                    }
                )
                LogCustomerFullFlowQuickSalesModel().update_log_customer_full_flow_quick_sales(
                    log_customer_full_flow_id,
                    {
                        ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_ACCOUNT_INDIV: result_step_handle,
                        "status": status_handle_full_flow,
                        "form_data_submit": form_data_submit,
                    },
                )

            else:
                status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL
                # Update trạng thái tạo cif bị fail cho FormBuilder
                result_update_form_builder = FormBuilderHelper().update_status_form_landing_page_ekyc_profile(
                    merchant_id,
                    form_id,
                    ConstantQuickSales.ConstantStatusFormBuilder.ACCOUNT_CREATE_ERROR,
                    profile_id,
                    message,
                )

                result_step_current.extend(
                    [
                        {
                            "status": ConstantQuickSales.ConstantStatusStepFlowQuickSales.FAIL,
                            "reasons": message,
                            "log_id": log_request_ca_acct_add_indiv_id,
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_ACCOUNT_INDIV,
                        },
                        {
                            "status": ConstantQuickSales.ConstantStatusFormBuilder.SUCCESS,
                            "reasons": message,
                            "log_id": log_request_ca_acct_add_indiv_id,
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_UPDATE_STATUS_FORM_BUILDER,
                            "result": {
                                "headersContent": {},
                                "payloadContent": {
                                    "merchant_id": merchant_id,
                                    "form_id": form_id,
                                    "profile_id": profile_id,
                                    "message": message,
                                    "status": ConstantQuickSales.ConstantStatusFormBuilder.ACCOUNT_CREATE_ERROR,
                                },
                                "responseContent": result_update_form_builder,
                                "infoContent": {},
                            },
                        },
                    ]
                )
                result_step_handle.update(
                    {
                        "status_step": ConstantQuickSales.ConstantStatusStepFlowQuickSales.FAIL,
                        "result_step_current": result_step_current,
                        "result_update_form_builder": result_update_form_builder,
                    }
                )
        except Exception as e:
            MobioLogging().error("CsmResCreateAccConsumer :: error {}".format(str(e)))
            status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL
            reason = str(e)

        LogCustomerFullFlowQuickSalesModel().update_log_customer_full_flow_quick_sales(
            log_customer_full_flow_id,
            {
                ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_ACCOUNT_INDIV: result_step_handle,
                "status": status_handle_full_flow,
                "reason": reason,
                "form_data_submit": form_data_submit,
            },
        )
        if status_handle_full_flow == ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL:
            text_send = "Khách hàng {} đã đăng ký CIF và SPDV không thành công. Vui lòng hỗ trợ khách hàng đăng ký CIF và SPDV tại quầy.".format(profile_name)
            data_send = {
                "staff_id": staff_id,
                "form_id": detail_log_customer_full_flow.get("form_id"),
                "status": status_handle_full_flow,
                "object_id": detail_log_customer_full_flow.get("object_id"),
                "object_type": detail_log_customer_full_flow.get("object_type"),
                "object_name": profile_name,
            }

            # Push socket
            MobioNotifySDK().send_message_notify_push_id_mobile_app(
                merchant_id=merchant_id,
                key_config="notify_default",
                account_ids=[staff_id],
                socket_type=KeyConfigNotifySDK.MOBILEBACKEND_RESULT_QUICK_SALES,
                title=text_send,
                content=text_send,
                **data_send,
            )


if __name__ == "__main__":
    CsmResCreateAccConsumer.handler_data(
        {
            "log_customer_full_flow_id": "68424dd5b002715f9b366fa3",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "staff_id": "c3e5097b-777c-42a0-9e2b-448668c15953",
            "action_time": "2025-06-06 06:55:30.705950",
        }
    )
