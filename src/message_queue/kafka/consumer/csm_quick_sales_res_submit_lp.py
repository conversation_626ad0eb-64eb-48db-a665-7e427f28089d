#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 18/02/2025
"""


from mobio.libs.logging import MobioLogging
from mobio.sdks.notify import MobioNotifySDK

from configs.kafka_config import KAFKA_TOPIC
from src.common import ConstantQuickSales, KeyConfigNotifySDK
from src.common.utils import get_datetime_utc_to_format_datetime
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.helpers.internal.mobio.form_builder import FormBuilderHelper
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.helpers.thirdparty.quick_sales.func_helper_transform_data import (
    FuncHelperTransformData,
)
from src.helpers.thirdparty.quick_sales.retry_form_quick_sales import RetryFormQuickSales
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)
from src.models.mongo.log_request_card_model import LogRequestCardModel


class CsmResSubmitLpConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        CsmResSubmitLpConsumer.handler_data(payload)

    @staticmethod
    def handler_data(payload):
        reason = None
        result_step_handle_submit_form = {
            "action_time": get_datetime_utc_to_format_datetime(),
            "next_step": {
                "step": ConstantQuickSales.ConstantStepFlow.STEP_END_FLOW,
                "action_time": get_datetime_utc_to_format_datetime(),
            },
            "result_step_current": [],
        }
        status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.PROCESSING
        merchant_id = payload.get("merchant_id")
        profile_id = payload.get("profile_id")
        form_id = payload.get("form_id")
        profile_name = ""
        try:
            MobioLogging().info("CsmResSubmitLpConsumer :: payload %s" % payload)

            form_data_submit = payload.get("form_data_submit")
            status = payload.get("status")
            reason = payload.get("reason")
            action_time = payload.get("action_time")
            staff_id = payload.get("staff_id")
            log_customer_full_flow_id = payload.get("log_customer_full_flow_id")
            MobioLogging().info("Start update status form builder")
            # result_update_form_builder = FormBuilderHelper().update_status_form_landing_page_ekyc_profile(
            #     merchant_id, form_id, status, profile_id, reason
            # )
            staff_detail = InternalAdminHelper().get_account_detail_info(merchant_id, staff_id)
            if not staff_detail:
                raise Exception("Staff detail not found")
            form_data_submit.update({"staff_info": staff_detail})

            MobioLogging().info("End update status form builder")

            identify_value = FuncHelperTransformData.get_identify_value_from_form_data(form_data_submit)
            if identify_value:
                # filter card information
                filter_card_info = {
                    "cardInformation.idCard": identify_value,
                    "merchant_id": merchant_id,
                    "flow_name": "quick_sales",
                }
                lst_card_info = LogRequestCardModel().find(filter_card_info).sort("_id", -1).limit(1)
                lst_card_info = [*lst_card_info]
                if lst_card_info:
                    card_info = lst_card_info[0]
                    item_card_information = card_info.get("cardInformation")
                    body_save_customer = card_info.get("body_save_customer", {})
                    form_data_submit.update(
                        {
                            "card_information": item_card_information,
                            "body_save_customer": body_save_customer,
                        }
                    )

            if status == ConstantQuickSales.ConstantStatusSubmitLandingPage.CANCEL:
                # TODO: Call API nhả số do customer huỷ đăng ký
                account_number, _ = FuncHelperTransformData.get_account_number_from_form_data_submit(
                    form_data_submit.get(ConstantQuickSales.ObjectType.WEB_FORM, {})
                )
                (
                    data_response_unlock,
                    status_code_unlock,
                    reasons_unlock,
                    log_request_id,
                ) = QuickSalesHelper().lock_unlock_account(
                    merchant_id, staff_id, account_number, ConstantQuickSales.ConstantActionAccount.UNLOCK
                )
                MobioLogging().info(
                    f"data_response_unlock: {data_response_unlock}, status_code_unlock: {status_code_unlock}, reasons_unlock: {reasons_unlock}"
                )

                result_update_form_builder = FormBuilderHelper().update_status_form_landing_page_ekyc_profile(
                    merchant_id,
                    form_id,
                    ConstantQuickSales.ConstantStatusFormBuilder.LANDING_PAGE_CANCEL,
                    profile_id,
                    reason,
                )
                result_step_handle_submit_form.update(
                    {
                        "result_step_current": [
                            {
                                "status": status_code_unlock,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "step": "unlock_account",
                                "reasons": reasons_unlock,
                                "result": data_response_unlock,
                            },
                            {
                                "status": ConstantQuickSales.ConstantStatusStepFlowQuickSales.FAIL,
                                "reasons": reason,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "step": ConstantQuickSales.ConstantStepFlow.STEP_SUBMIT_FORM,
                            },
                            {
                                "status": ConstantQuickSales.ConstantStatusFormBuilder.SUCCESS,
                                "reasons": reason,
                                "log_id": log_request_id,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "step": ConstantQuickSales.ConstantStepFlow.STEP_UPDATE_STATUS_FORM_BUILDER,
                                "result": {
                                    "headersContent": {},
                                    "payloadContent": {
                                        "merchant_id": merchant_id,
                                        "form_id": form_id,
                                        "profile_id": profile_id,
                                        "message": reason,
                                        "status": ConstantQuickSales.ConstantStatusFormBuilder.LANDING_PAGE_CANCEL,
                                    },
                                    "responseContent": result_update_form_builder,
                                    "infoContent": {},
                                },
                            },
                        ],
                        "result_update_form_builder": result_update_form_builder,
                    }
                )

            if status == ConstantQuickSales.ConstantStatusSubmitLandingPage.CONFIRM:
                result_update_form_builder = FormBuilderHelper().update_status_form_landing_page_ekyc_profile(
                    merchant_id,
                    form_id,
                    ConstantQuickSales.ConstantStatusFormBuilder.LANDING_PAGE_ACCEPT,
                    profile_id,
                    reason,
                )

                # log_id = Producer().send_message_to_topic(
                #     KAFKA_TOPIC.QUICK_SALES_RES_CIF,
                #     {
                #         "log_customer_full_flow_id": log_customer_full_flow_id,
                #         "merchant_id": merchant_id,
                #         "staff_id": staff_id,
                #         "action_time": action_time,
                #         # "data_register_cif": info_customer_data,
                #     },
                # )

                log_customer_full_flow_quick_sales = LogCustomerFullFlowQuickSalesModel().get_log_customer_full_flow_quick_sales_by_id(
                    log_customer_full_flow_id
                )
                if not log_customer_full_flow_quick_sales:
                    raise Exception("Log customer full flow quick sales not found!!")

                RetryFormQuickSales.process_retry_form_to_third_party(log_customer_full_flow_quick_sales)

                # MobioLogging().info("CsmResSubmitLpConsumer :: log_id %s" % log_id)
                # Update next step and current step info in one operation
                result_step_handle_submit_form.update(
                    {
                        "next_step": {
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF,
                            "log_id": None,
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.QUEUE_KAFKA,
                        },
                        "result_step_current": [
                            {
                                "step": ConstantQuickSales.ConstantStepFlow.STEP_SUBMIT_FORM,
                                "status": ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.QUEUE_KAFKA,
                                "log_id": None,
                            }
                        ],
                    }
                )
        except Exception as e:
            MobioLogging().error("CsmResSubmitLpConsumer :: error %s" % e)
            status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL
            reason = str(e)
        LogCustomerFullFlowQuickSalesModel().update_log_customer_full_flow_quick_sales(
            log_customer_full_flow_id,
            {
                ConstantQuickSales.ConstantStepFlow.STEP_SUBMIT_FORM: result_step_handle_submit_form,
                "status": status_handle_full_flow,
                "reason": reason,
                "form_data_submit": form_data_submit,
            },
        )
        if status_handle_full_flow == ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL:
            text_send = "Khách hàng {} đã đăng ký CIF và SPDV không thành công. Vui lòng hỗ trợ khách hàng đăng ký CIF và SPDV tại quầy.".format(
                profile_name
            )
            data_send = {
                "staff_id": staff_id,
                "form_id": form_id,
                "status": status_handle_full_flow,
                "object_id": profile_id,
                "object_type": ConstantQuickSales.ObjectType.PROFILE,
                "object_name": profile_name,
            }

            # Push socket
            MobioNotifySDK().send_message_notify_push_id_mobile_app(
                merchant_id=merchant_id,
                key_config="notify_default",
                account_ids=[staff_id],
                socket_type=KeyConfigNotifySDK.MOBILEBACKEND_RESULT_QUICK_SALES,
                title=text_send,
                content=text_send,
                **data_send,
            )


if __name__ == "__main__":
    CsmResSubmitLpConsumer.handler_data(
        {
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "phone_number": "+***********",
            "profile_id": "d5566c1f-3055-4977-9ead-676ee61bd9ea",
            "form_id": "67c13217fc018eabed5a9b7a",
            "form_data_submit": {
                "profile": {
                    "_dyn_chuc_vu_1638341818684": "dffgt",
                    "_dyn_ngay_cap_cccd_1714017181392": "2022-09-11T17:00:00.000Z",
                    "_dyn_ngay_het_han_cccd_1714017181394": "2035-09-09T17:00:00.000Z",
                    "_dyn_nghe_nghiep_cua_khach_hang_1696581120694": ["Sinh viên"],
                    "birthday": "1988-03-21T17:00:00.000Z",
                    "gender": 3,
                    "name": "Vũ Thị Ngoan",
                    "nationality": "Việt Nam",
                    "primary_email": "<EMAIL>",
                    "primary_phone": "+84986490460",
                    "profile_identify": [{"identify_type": "citizen_identity", "identify_value": "034188003235"}],
                },
                "web_form": {
                    "133b7018-2bd5-4b20-b945-9705acdd5b6f": ["Thanh toán hàng hóa, dịch vụ", "Nhận lương"],
                    "address_contact": [
                        {
                            "city": "Quận Liên Chiểu",
                            "city_code": 490,
                            "country": "Việt Nam",
                            "country_code": "VNM",
                            "county": "Thành phố Đà Nẵng",
                            "county_code": 48,
                            "detail": "Ttyy",
                            "district": "Phường Hòa Minh",
                            "district_code": 20200,
                            "state": None,
                            "state_code": None,
                            "subdistrict": None,
                            "subdistrict_code": None,
                            "type": "DISTRICT",
                            "type_address": "custom",
                            "unique_value": "VNM#48#490#20200#",
                            "value_cccd": "số 10 Kim Mã Ba Đình Hà Nội",
                        }
                    ],
                    "ekyc_select_product_bank": [
                        {"field_key": "product_line", "key": "65a0bc1abd24020205a491dc", "name": "Eximbank EDigi"},
                        {
                            "field_key": "product",
                            "key": "67ff14c86295342bb248cd56",
                            "name": "E-Plus Online Banking (IB+MB) – eKYC Plus",
                        },
                    ],
                },
            },
            "status": "confirm",
            "staff_id": "c3e5097b-777c-42a0-9e2b-448668c15953",
            "reason": None,
            "action_time": "2025-03-01 07:06:13.156441",
            "log_customer_full_flow_id": "6800e4363b93052adab72720",
        }
    )
