#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 27/02/2025
"""

from mobio.libs.logging import MobioLogging
from mobio.sdks.notify import MobioNotifySDK

from configs.kafka_config import KAFKA_TOPIC
from src.common import ConstantQuickSales, KeyConfigNotifySDK
from src.common.utils import get_datetime_utc_to_format_datetime
from src.helpers.internal.mobio.form_builder import FormBuilderHelper
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)


class CsmQuickSalesUpsertDataToCrmConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        CsmQuickSalesUpsertDataToCrmConsumer.handler_data(payload)

    @staticmethod
    def handler_data(payload):
        """
        - Logic phần này cần call update profile và upsert cơ hội bán
        """
        reason = None
        status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.PROCESSING
        result_step_handle = {
            "result_step_current": [],
            "action_time": get_datetime_utc_to_format_datetime(),
            "next_step": {
                "step": ConstantQuickSales.ConstantStepFlow.STEP_END_FLOW,
            },
        }
        profile_name = ""
        merchant_id = payload.get("merchant_id")
        staff_id = payload.get("staff_id")
        action_time = payload.get("action_time")
        try:
            MobioLogging().info("CsmQuickSalesUpsertDataToCrmConsumer :: payload %s" % payload)

            log_customer_full_flow_id = payload.get("log_customer_full_flow_id")

            detail_log_customer_full_flow = (
                LogCustomerFullFlowQuickSalesModel().get_log_customer_full_flow_quick_sales_by_id_cache(
                    log_customer_full_flow_id
                )
            )
            if not detail_log_customer_full_flow:
                MobioLogging().error("CsmQuickSalesUpsertDataToCrmConsumer :: detail_log_customer_full_flow not found")
                return

            profile_id = detail_log_customer_full_flow.get("object_id")
            form_id = detail_log_customer_full_flow.get("form_id")
            form_data_submit = detail_log_customer_full_flow.get("form_data_submit", {})
            profile_name = form_data_submit.get("profile", {}).get("name", "")

            # TODO: call api update profile
            # profile_cif = CsmQuickSalesUpsertDataToCrmConsumer.get_cif_by_flow_quick_sales(
            #     detail_log_customer_full_flow
            # )

            # TODO: call api upsert cơ hội bán
            result_create_deal = {}

            # Call API update status form builder
            result_update_form_builder = FormBuilderHelper().update_status_form_landing_page_ekyc_profile(
                merchant_id,
                form_id,
                ConstantQuickSales.ConstantStatusFormBuilder.SUCCESS,
                profile_id,
                None,
            )
            log_push_message_id = ""
            result_step_handle["result_step_current"] = [
                # {
                #     "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_UPSERT_CIF_TO_PROFILE,
                #     "status": status_code_upsert_cif_to_profile,
                #     "result": result_update_profile,
                #     "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                # },
                {
                    "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_DEAL,
                    "status": ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS,
                    "result": result_create_deal,
                    "type": ConstantQuickSales.ConstantTypeHandleLogic.QUEUE_KAFKA,
                    "log_id": log_push_message_id,
                },
                {
                    "step": ConstantQuickSales.ConstantStepFlow.STEP_UPDATE_STATUS_FORM_BUILDER,
                    "status": ConstantQuickSales.ConstantStatusFormBuilder.SUCCESS,
                    # "result": result_update_form_builder,
                    "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                    "result": {
                        "headersContent": {},
                        "payloadContent": {
                            "merchant_id": merchant_id,
                            "form_id": form_id,
                            "profile_id": profile_id,
                            "message": "Cập nhật status form builder thành công",
                            "status": ConstantQuickSales.ConstantStatusFormBuilder.SUCCESS,
                        },
                        "responseContent": result_update_form_builder,
                        "infoContent": {},
                    },
                },
            ]

            # Push message to topic end flow
            log_push_message_id = Producer().send_message_to_topic(
                topic_name=KAFKA_TOPIC.QUICK_SALES_END,
                data_send={
                    "log_customer_full_flow_id": log_customer_full_flow_id,
                    "merchant_id": merchant_id,
                    "staff_id": staff_id,
                    "action_time": action_time,
                },
            )
            result_step_handle["next_step"].update(
                {
                    "log_id": log_push_message_id,
                    "type": ConstantQuickSales.ConstantTypeHandleLogic.QUEUE_KAFKA,
                }
            )
        except Exception as e:
            MobioLogging().error("CsmQuickSalesUpsertDataToCrmConsumer :: error %s" % e)
            status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL
            reason = str(e)

        LogCustomerFullFlowQuickSalesModel().update_log_customer_full_flow_quick_sales(
            log_customer_full_flow_id,
            {
                ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_UPDATE_DATA_TO_CRM: result_step_handle,
                "status": status_handle_full_flow,
                "reason": reason,
            },
        )
        if status_handle_full_flow == ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL:
            text_send = "Khách hàng {} đã đăng ký CIF và SPDV không thành công. Vui lòng hỗ trợ khách hàng đăng ký CIF và SPDV tại quầy.".format(profile_name)
            data_send = {
                "staff_id": staff_id,
                "form_id": detail_log_customer_full_flow.get("form_id"),
                "status": status_handle_full_flow,
                "object_id": detail_log_customer_full_flow.get("object_id"),
                "object_type": detail_log_customer_full_flow.get("object_type"),
                "object_name": profile_name,
            }

            # Push socket
            MobioNotifySDK().send_message_notify_push_id_mobile_app(
                merchant_id=merchant_id,
                key_config="notify_default",
                account_ids=[staff_id],
                socket_type=KeyConfigNotifySDK.MOBILEBACKEND_RESULT_QUICK_SALES,
                title=text_send,
                content=text_send,
                **data_send,
            )

    @staticmethod
    def get_cif_by_flow_quick_sales(detail_log_customer_full_flow):
        result_step_create_cif = detail_log_customer_full_flow.get(
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF
        )
        if not result_step_create_cif:
            MobioLogging().error("CsmQuickSalesUpsertDataToCrmConsumer :: result_step_create_cif not found")
            return

        result_step_current = result_step_create_cif.get("result_step_current", {})
        if not result_step_current:
            MobioLogging().error("CsmQuickSalesUpsertDataToCrmConsumer :: result_step_current not found")
            return
        result_step_current = result_step_current[0]

        status = result_step_current.get("status", {})
        if status != ConstantQuickSales.ConstantStatusFormBuilder.SUCCESS:
            MobioLogging().error("CsmQuickSalesUpsertDataToCrmConsumer :: status not success")
            return

        cif_data = result_step_current.get("result", {})
        if not cif_data:
            MobioLogging().error("CsmQuickSalesUpsertDataToCrmConsumer :: cif_data not found")
            return

        return cif_data.get("CustId")


if __name__ == "__main__":
    CsmQuickSalesUpsertDataToCrmConsumer.handler_data(
        {
            "log_customer_full_flow_id": "67ea3c627d3519232ae72c69",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "staff_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "action_time": "2025-03-31 06:55:30.705950",
        }
    )
