#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 26/04/2024
"""

import datetime
import os
import uuid
from mimetypes import guess_type

from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from mobio.libs.logging import Mo<PERSON>Logging
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from configs import MobileBackendApplicationConfig, RedisConfig
from src.common import SHARE_FOLDER_EVIDENT_STATIC, ConstantAttachmentType
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer
from src.models.mongo.attachments_model import AttachmentsModel

MobioMediaSDK().config(admin_host=MobileBackendApplicationConfig.ADMIN_HOST, cache_prefix=RedisConfig.CACHE_PREFIX)


class ConstantMessageKey:
    MERCHANT_ID = "merchant_id"
    ACCOUNT_ID = "account_id"
    MESSAGE_TYPE = "message_type"
    DATA_BUILD_FILE_EVIDENT = "data_build_file_evident"
    DATA_BUILD_AVATAR = "data_build_avatar"


class ConstantMessageType:
    BUILD_FILE_EVIDENT_PROFILE = "build_file_evident_profile"
    BUILD_AVATAR_PROFILE = "build_avatar_profile"


class BuildInformationRelatedProfileConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        BuildInformationRelatedProfileConsumer.handler_data(payload)

    @staticmethod
    def handler_data(payload):
        MobioLogging().info("BuildFileEvidentProfileConsumer() :: process_msg :: %s" % payload)

        message_type = payload.get(ConstantMessageKey.MESSAGE_TYPE)
        log_id = payload.get("log_id")

        merchant_id = payload.get(ConstantMessageKey.MERCHANT_ID)
        account_id = payload.get(ConstantMessageKey.ACCOUNT_ID)

        if message_type == ConstantMessageType.BUILD_FILE_EVIDENT_PROFILE:
            BuildInformationRelatedProfileConsumer._gen_evident_confirm_consent_sms(
                merchant_id,
                account_id,
                log_id,
                data_build_file_evident=payload.get(ConstantMessageKey.DATA_BUILD_FILE_EVIDENT),
            )

        if message_type == ConstantMessageType.BUILD_AVATAR_PROFILE:
            BuildInformationRelatedProfileConsumer._gen_avatar_profile(
                merchant_id, account_id, payload.get(ConstantMessageKey.DATA_BUILD_AVATAR), log_id
            )

    @staticmethod
    def _build_time_from_string_to_format(string_time):
        date_time_obj = datetime.datetime.strptime(string_time, "%Y-%m-%d %H:%M:%S") + datetime.timedelta(hours=7)
        return "{}:{} ngày {}/{}/{}".format(
            date_time_obj.hour, date_time_obj.minute, date_time_obj.day, date_time_obj.month, date_time_obj.year
        )

    @staticmethod
    def _gen_evident_confirm_consent_sms(merchant_id, account_id, log_id, data_build_file_evident):

        filename = "{}.docx".format(str(uuid.uuid4()))
        filepath = os.path.join(SHARE_FOLDER_EVIDENT_STATIC, filename)
        MobioLogging().info("_gen_evident_confirm_consent_sms :: filepath :: %s")

        phone_number = data_build_file_evident.get("phone_number")
        profile_name = data_build_file_evident.get("profile_name")
        sms_brand_name_send = data_build_file_evident.get("sms_brand_name_send")
        sms_brand_name_receiver = data_build_file_evident.get("sms_brand_name_receiver")
        time_sent_sms = data_build_file_evident.get("time_sent_sms")
        time_receiver_sms = data_build_file_evident.get("time_receiver_sms")
        message_content_reply = data_build_file_evident.get("message_content_reply")
        message_content_request = data_build_file_evident.get("message_content_request")
        account_username = data_build_file_evident.get("account_username")

        document = Document()

        p1 = document.add_paragraph()
        p1.alignment = WD_ALIGN_PARAGRAPH.CENTER  # Căn giữa
        p1.add_run(
            "Biên bản xác nhận yêu cầu cung cấp dữ liệu cá nhân cho Ngân hàng thương mại cổ phần Xuất Nhập khẩu Việt Nam (Eximbank)."
        ).bold = True

        document.add_paragraph()

        p2 = document.add_paragraph()
        p2.add_run("Hình thức xác nhận").bold = True
        p2.add_run(": Xác nhận qua SMS")

        document.add_paragraph()

        p3 = document.add_paragraph()
        p3.add_run("Chi tiết nội dung xác nhận:").bold = True

        p4 = document.add_paragraph(
            "Cán bộ bán hàng thực hiện hỗ trợ khách hàng xác nhận Consent qua ESale+:", style="List Number"
        )

        p41 = document.add_paragraph("Tên tài khoản: {}".format(account_username), style="List Bullet")
        p42 = document.add_paragraph("ID tài khoản: {}".format(account_id), style="List Bullet")

        p5 = document.add_paragraph("Thông tin tin nhắn SMS xác nhận", style="List Number")
        p51 = document.add_paragraph("Số điện thoại khách hàng: ", style="List Bullet")
        p51.add_run("{}".format(phone_number)).bold = True
        p52 = document.add_paragraph("Thông tin SMS Eximbank gửi yêu cầu xác nhận:", style="List Bullet")
        p521 = document.add_paragraph(
            "Đầu số gửi SMS (SMS brandname): {}".format(sms_brand_name_send), style="List Bullet 2"
        )
        p522 = document.add_paragraph(
            "Thời gian gửi SMS: {}".format(
                BuildInformationRelatedProfileConsumer._build_time_from_string_to_format(time_sent_sms)
            ),
            style="List Bullet 2",
        )
        document.add_paragraph("Mã tham chiếu tin nhắn: {}".format(log_id), style="List Bullet 2")
        p523 = document.add_paragraph(
            'Nội dung tin nhắn: "{}"'.format(message_content_request),
            style="List Bullet 2",
        )
        p53 = document.add_paragraph(
            "Thông tin SMS khách hàng phản hồi để xác nhận cung cấp dữ liệu cá nhân:", style="List Bullet"
        )
        p531 = document.add_paragraph(
            "Đầu số nhận SMS (SMS brandname): {}".format(sms_brand_name_receiver), style="List Bullet 2"
        )
        p532 = document.add_paragraph(
            "Thời gian nhận SMS khách hàng phản hồi: {}".format(
                BuildInformationRelatedProfileConsumer._build_time_from_string_to_format(time_receiver_sms)
            ),
            style="List Bullet 2",
        )
        p533 = document.add_paragraph(
            'Nội dung SMS KH phản hồi: "{}"'.format(message_content_reply), style="List Bullet 2"
        )

        document.add_page_break()
        document.save(filepath)
        MobioLogging().info("_gen_evident_confirm_consent_sms :: filepath :: %s" % filepath)
        info_upload = MobioMediaSDK().upload_without_kafka(
            merchant_id=merchant_id,
            file_path=filepath,
            filename=filename,
            do_not_delete=True,
        )
        MobioLogging().debug("upload_file :: info_upload %s " % info_upload)
        info_upload["filename"] = "Evident Profile {}.docx".format(profile_name)
        data_insert = {
            "merchant_id": merchant_id,
            "log_id": str(log_id),
            "type": ConstantAttachmentType.EVIDENT_CONSENT,
            "created_time": datetime.datetime.now(datetime.UTC),
        }
        data_insert.update(info_upload)
        MobioLogging().debug("upload_file :: data_insert %s " % data_insert)
        AttachmentsModel().insert(data_insert)

    @staticmethod
    def _gen_avatar_profile(merchant_id, account_id, data_gen_avatar_profile, log_id):
        from mimetypes import guess_extension

        avatar_guess_type = guess_type(data_gen_avatar_profile)
        MobioLogging().info("_gen_avatar_profile :: avatar_guess_type :: %s" % str(avatar_guess_type))
        get_extension_file = ".jpg"
        if avatar_guess_type:
            avatar_guess_type = avatar_guess_type[0]
            if avatar_guess_type:
                get_extension_file = guess_extension(avatar_guess_type)
        filename = "{}{}".format(str(uuid.uuid4()), get_extension_file)
        filepath = os.path.join(SHARE_FOLDER_EVIDENT_STATIC, filename)
        import base64

        base64_string = data_gen_avatar_profile
        if "data:image" in data_gen_avatar_profile:
            base64_string = data_gen_avatar_profile.split(",")[1]

        img_data = base64.b64decode(base64_string)
        with open(filepath, "wb") as f:
            f.write(img_data)
        MobioLogging().info("_gen_avatar_profile :: filepath :: %s" % filepath)
        info_upload = MobioMediaSDK().upload_without_kafka(
            merchant_id=merchant_id,
            file_path=filepath,
            filename=filename,
            do_not_delete=True,
        )
        MobioLogging().debug("upload_file :: info_upload %s " % info_upload)
        data_insert = {
            "merchant_id": merchant_id,
            "log_id": str(log_id),
            "type": ConstantAttachmentType.AVATAR,
            "created_time": datetime.datetime.now(datetime.UTC),
        }
        data_insert.update(info_upload)
        MobioLogging().debug("upload_file :: data_insert %s " % data_insert)
        AttachmentsModel().insert(data_insert)


if __name__ == "__main__":
    BuildInformationRelatedProfileConsumer._gen_evident_confirm_consent_sms(
        "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "1",
        "********",
        {
            "account_username": "tungdd@eib",
            "phone_number": "***********",
            "profile_name": "\u0110\u00e0o \u0110\u1ee9c T\u00f9ng",
            "sms_brand_name_send": "8142",
            "sms_brand_name_receiver": "8142",
            "time_sent_sms": "2024-05-19 22:18:33",
            "time_receiver_sms": "2024-05-19 22:18:36",
            "message_content_reply": "YES",
            "message_content_request": "Quy khach Dao Duc Tung dang cung cap thong tin CCCD de dang ky SPDV tai Eximbank. Soan tin YES gui 8149 de dong y hoac goi ******** de duoc ho tro.",
        },
    )
