#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 18/02/2025
"""

import datetime
import time
import uuid

from mobio.libs.logging import MobioLogging
from mobio.sdks.notify import MobioNotifySDK

from configs.kafka_config import KAFKA_TOPIC
from src.common import (
    ConstantQuickSales,
    KeyConfigNotifySDK,
    StatusCode,
    ThirdPartyType,
)
from src.common.utils import get_datetime_utc_to_format_datetime
from src.helpers.internal.mobio.form_builder import FormBuilderHelper
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.helpers.thirdparty.quick_sales.func_helper_transform_data import (
    FuncHelperTransformData,
)
from src.helpers.thirdparty.quick_sales.send_email import Send<PERSON><PERSON><PERSON><PERSON>per
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)


class CsmResEdigiAccConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        CsmResEdigiAccConsumer.handler_data(payload)

    @staticmethod
    def validate_payload(payload):
        if not payload:
            raise Exception("payload is required")

        if not payload.get("log_customer_full_flow_id"):
            raise Exception("log_customer_full_flow_id is required")

    @staticmethod
    def send_email_to_profile(
        merchant_id,
        payload_send,
        path_template_send_email_res_edigi_account,
        log_id,
        email_to,
    ):
        MobioLogging().info("CsmResEdigiAccConsumer :: send_email_to_profile :: payload_send %s" % payload_send)
        """
        payload_send: dict
        - profile_name: str
        - profile_cif: str
        - account_number: str
        - date_account_active
        - username_edigi
        - package_code


        path_template_send_email_res_edigi_account: dict
        log_id: str
        email_to: str
        """

        subject = "Thông tin đăng ký tài khoản thanh toán tại Eximbank"
        content_send_email = ""
        with open(path_template_send_email_res_edigi_account, "r") as file:
            content_send_email = file.read()

        for key, value in payload_send.items():
            content_send_email = content_send_email.replace(f"**{key}**", value)

        response_send_email = SendEmailHelper(merchant_id).send_email_smtp(
            log_id, {"to": email_to}, subject, content_send_email, {}
        )

        MobioLogging().info(
            "CsmResEdigiAccConsumer :: send_email_to_profile :: response_send_email %s" % response_send_email
        )
        return response_send_email

    @staticmethod
    def convert_status_send_email_to_status_dynamic_event(
        response_send_email,
    ):
        if response_send_email.get("code") == 200:
            return "Thành công"
        return "Thất bại"

    @staticmethod
    def send_dynamic_event_to_profile(
        merchant_id,
        profile_id,
        payload_send,
    ):

        event_key = payload_send.pop("event_key") if payload_send.get("event_key") else ""
        dynamic_event_id = payload_send.pop("dynamic_event_id") if payload_send.get("dynamic_event_id") else ""

        message_data = {
            "code": "success",
            "data": {
                "profile_id": profile_id,
                "merchant_id": [merchant_id],
            },
            "data_callback": {
                "event_key": event_key,
                "event_data": payload_send,
                "merchant_id": merchant_id,
                "tracking_code": "",
                "dynamic_event_id": dynamic_event_id,
            },
        }
        MobioLogging().info("CsmResEdigiAccConsumer :: send_dynamic_event_to_profile :: message_data %s" % message_data)
        Producer().send_message_to_topic("profile-event-dynamic-p2", message_data)

    @staticmethod
    def handler_data(payload):
        result_step_handle = {
            "result_step_current": [],
            "action_time": get_datetime_utc_to_format_datetime(),
            "next_step": {
                "step": ConstantQuickSales.ConstantStepFlow.STEP_END_FLOW,
            },
        }
        reason = None
        message = None
        status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.PROCESSING
        is_send_email_to_profile_when_res_edigi_account_fail = False
        profile_name = ""
        merchant_id = payload.get("merchant_id")
        staff_id = payload.get("staff_id")
        action_time = payload.get("action_time")
        try:
            MobioLogging().info("CsmResEdigiAccConsumer :: payload %s" % payload)
            CsmResEdigiAccConsumer.validate_payload(payload)
            log_customer_full_flow_id = payload.get("log_customer_full_flow_id")

            detail_log_customer_full_flow = (
                LogCustomerFullFlowQuickSalesModel().get_log_customer_full_flow_quick_sales_by_id_cache(
                    log_customer_full_flow_id
                )
            )

            if not detail_log_customer_full_flow:
                raise Exception("detail_log_customer_full_flow is required")

            profile_id = detail_log_customer_full_flow.get("object_id")
            form_id = detail_log_customer_full_flow.get("form_id")
            form_data_submit = detail_log_customer_full_flow.get("form_data_submit", {})
            profile_name = form_data_submit.get("profile", {}).get("name", "")
            profile_email = form_data_submit.get("profile", {}).get("primary_email", "")

            # TODO: send to topic res create account
            data_res_account_edigi = FuncHelperTransformData(
                merchant_id
            ).transform_data_from_create_account_to_res_account_edigi(form_data_submit)

            MobioLogging().info("CsmResEdigiAccConsumer :: data_res_account_edigi %s" % data_res_account_edigi)

            config_send_request_res_account_edigi = QuickSalesHelper()._get_config_res_account_edigi(merchant_id)

            config_send_email = ConfigInfoApiModel().get_config_info_api(
                merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_SEND_EMAIL
            )

            number_retry_request = config_send_request_res_account_edigi.get("number_retry_request", 3)

            path_template_send_email_res_edigi_account_fail = config_send_email.get("path_template_send_email_fail", {})
            path_template_send_email_res_edigi_account_success = config_send_email.get(
                "path_template_send_email_success", {}
            )

            is_send_email_to_profile_when_res_edigi_account_fail = detail_log_customer_full_flow.get(
                "send_email_to_profile_when_res_edigi_account_fail", False
            )

            # Step call API unlock account
            result_step_current = []
            (
                response_res_edigi_account,
                status_code_res_edigi_account,
                message_res_edigi_account,
                log_request_id,
            ) = QuickSalesHelper().send_request_res_account_edigi(
                merchant_id, staff_id, action_time, data_res_account_edigi
            )
            while (
                True
                and status_code_res_edigi_account != StatusCode.SUCCESS
                and (number_retry_request and number_retry_request > 0)
            ):
                MobioLogging().info(
                    "CsmResEdigiAccConsumer :: retry send request res edigi account :: %s" % number_retry_request
                )
                (
                    response_res_edigi_account,
                    status_code_res_edigi_account,
                    message_res_edigi_account,
                    log_request_id,
                ) = QuickSalesHelper().send_request_res_account_edigi(
                    merchant_id, staff_id, action_time, data_res_account_edigi
                )
                number_retry_request -= 1
                time.sleep(1)

            if status_code_res_edigi_account == StatusCode.SUCCESS:

                # Send email to profile success
                payload_send_email_to_profile = FuncHelperTransformData(
                    merchant_id
                ).transform_data_send_email_request_edigi(
                    form_data_submit, data_res_account_edigi, response_res_edigi_account
                )

                log_send_email_id = str(uuid.uuid4())

                response_send_email = CsmResEdigiAccConsumer.send_email_to_profile(
                    merchant_id,
                    payload_send_email_to_profile,
                    path_template_send_email_res_edigi_account_success,
                    log_send_email_id,
                    profile_email,
                )
                status_send_email = CsmResEdigiAccConsumer.convert_status_send_email_to_status_dynamic_event(
                    response_send_email
                )

                data_send_email = {
                    "status_send_email": status_send_email,
                    "action_time": datetime.datetime.now(datetime.timezone.utc).timestamp(),
                }

                payload_send_dynamic_event = FuncHelperTransformData(
                    merchant_id
                ).transform_data_send_dynamic_event_to_profile(
                    form_data_submit, data_res_account_edigi, response_res_edigi_account, data_send_email
                )
                CsmResEdigiAccConsumer.send_dynamic_event_to_profile(
                    merchant_id, profile_id, payload_send_dynamic_event
                )

                # push message to topic res end
                log_push_kafka_id = Producer().send_message_to_topic(
                    KAFKA_TOPIC.QUICK_SALES_UPDATE_TO_CRM,
                    {
                        "log_customer_full_flow_id": log_customer_full_flow_id,
                        "merchant_id": merchant_id,
                        "staff_id": staff_id,
                        "action_time": action_time,
                    },
                )
                # Add action_time to response_res_edigi_account
                response_res_edigi_account["action_time"] = action_time
                form_data_submit["response_res_edigi_account"] = response_res_edigi_account
                result_step_current.extend(
                    [
                        {
                            "result": response_res_edigi_account,
                            "log_id": log_request_id,
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                            "status": ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS,
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_REGISTER_EDIGI_ACCOUNT,
                        },
                        {
                            "status": ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS,
                            "reasons": response_send_email,
                            "log_id": log_send_email_id,
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_SEND_EMAIL_TO_PROFILE,
                            "result": response_send_email,
                        },
                    ]
                )
                result_step_handle.update(
                    {
                        "status_step": ConstantQuickSales.ConstantStatusStepFlowQuickSales.SUCCESS,
                        "result_step_current": result_step_current,
                        "next_step": {
                            "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_UPDATE_DATA_TO_CRM,
                            "log_id": log_push_kafka_id,
                            "type": ConstantQuickSales.ConstantTypeHandleLogic.QUEUE_KAFKA,
                        },
                    }
                )
            if status_code_res_edigi_account == StatusCode.THIRD_PARTY_FAILURE:
                if not is_send_email_to_profile_when_res_edigi_account_fail:
                    is_send_email_to_profile_when_res_edigi_account_fail = True
                    payload_send_email_to_profile = FuncHelperTransformData(
                        merchant_id
                    ).transform_data_send_email_request_edigi(form_data_submit, data_res_account_edigi, {})

                    log_send_email_id = str(uuid.uuid4())

                    response_send_email = CsmResEdigiAccConsumer.send_email_to_profile(
                        merchant_id,
                        payload_send_email_to_profile,
                        path_template_send_email_res_edigi_account_fail,
                        log_send_email_id,
                        profile_email,
                    )

                    status_send_email = CsmResEdigiAccConsumer.convert_status_send_email_to_status_dynamic_event(
                        response_send_email
                    )

                    data_send_email = {
                        "status_send_email": status_send_email,
                        "action_time": datetime.datetime.now(datetime.timezone.utc).timestamp(),
                    }

                    payload_send_dynamic_event = FuncHelperTransformData(
                        merchant_id
                    ).transform_data_send_dynamic_event_to_profile(
                        form_data_submit, data_res_account_edigi, response_res_edigi_account, data_send_email
                    )
                    CsmResEdigiAccConsumer.send_dynamic_event_to_profile(
                        merchant_id, profile_id, payload_send_dynamic_event
                    )

                message = message_res_edigi_account
                status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL
                result_update_form_builder = FormBuilderHelper().update_status_form_landing_page_ekyc_profile(
                    merchant_id,
                    form_id,
                    ConstantQuickSales.ConstantStatusFormBuilder.EDIGI_CREATE_ERROR,
                    profile_id,
                    message,
                )
                result_step_handle.update(
                    {
                        "status_step": ConstantQuickSales.ConstantStatusStepFlowQuickSales.FAIL,
                        "result_step_current": [
                            {
                                "status": ConstantQuickSales.ConstantStatusStepFlowQuickSales.FAIL,
                                "reasons": message,
                                "log_id": log_request_id,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "step": ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_REGISTER_EDIGI_ACCOUNT,
                            },
                            {
                                "status": ConstantQuickSales.ConstantStatusFormBuilder.SUCCESS,
                                "reasons": message,
                                "log_id": log_request_id,
                                "type": ConstantQuickSales.ConstantTypeHandleLogic.API,
                                "step": ConstantQuickSales.ConstantStepFlow.STEP_UPDATE_STATUS_FORM_BUILDER,
                                "result": {
                                    "headersContent": {},
                                    "payloadContent": {
                                        "merchant_id": merchant_id,
                                        "form_id": form_id,
                                        "profile_id": profile_id,
                                        "message": message,
                                        "status": ConstantQuickSales.ConstantStatusFormBuilder.EDIGI_CREATE_ERROR,
                                    },
                                    "responseContent": result_update_form_builder,
                                    "infoContent": {},
                                },
                            },
                        ],
                        "result_update_form_builder": result_update_form_builder,
                    }
                )
        except Exception as e:
            MobioLogging().error("CsmResEdigiAccConsumer :: error :: {}".format(str(e)))
            status_handle_full_flow = ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL
            reason = str(e)

        LogCustomerFullFlowQuickSalesModel().update_log_customer_full_flow_quick_sales(
            log_customer_full_flow_id,
            {
                ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_REGISTER_EDIGI_ACCOUNT: result_step_handle,
                "status": status_handle_full_flow,
                "reason": reason,
                "form_data_submit": form_data_submit,
                "send_email_to_profile_when_res_edigi_account_fail": is_send_email_to_profile_when_res_edigi_account_fail,
            },
        )
        if status_handle_full_flow == ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL:
            text_send = "Khách hàng {} đã đăng ký CIF và SPDV không thành công. Vui lòng hỗ trợ khách hàng đăng ký CIF và SPDV tại quầy.".format(profile_name)
            data_send = {
                "staff_id": staff_id,
                "form_id": detail_log_customer_full_flow.get("form_id"),
                "status": status_handle_full_flow,
                "object_id": detail_log_customer_full_flow.get("object_id"),
                "object_type": detail_log_customer_full_flow.get("object_type"),
                "object_name": profile_name,
            }

            # Push socket
            MobioNotifySDK().send_message_notify_push_id_mobile_app(
                merchant_id=merchant_id,
                key_config="notify_default",
                account_ids=[staff_id],
                socket_type=KeyConfigNotifySDK.MOBILEBACKEND_RESULT_QUICK_SALES,
                title=text_send,
                content=text_send,
                **data_send,
            )


if __name__ == "__main__":
    CsmResEdigiAccConsumer.handler_data(
        payload={
            "log_customer_full_flow_id": "6848ee4dd71d47204cff878f",
            "staff_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "action_time": "2025-05-28 06:55:30.705950",
        }
    )
