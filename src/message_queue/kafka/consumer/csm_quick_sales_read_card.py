#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 14/03/2025
"""

from src.common import ConstantMessageSendRequestToThirdParty
from src.helpers.kafka.confluent_kafka_base import ConfluentKafkaConsumer
from src.helpers.thirdparty.quick_sales.send_request_only_read_card import (
    SendRequestOnlyReadCard,
)


class QuickSalesReadCardConsumer(ConfluentKafkaConsumer):
    def __init__(self, topic_name, group_id):
        super().__init__(topic_name, group_id)

    def process_msg(self, payload):
        QuickSalesReadCardConsumer.handler_data(payload)

    @staticmethod
    def handler_data(payload):
        merchant_id = payload.get(ConstantMessageSendRequestToThirdParty.MERCHANT_ID)
        account_id = payload.get(ConstantMessageSendRequestToThirdParty.ACCOUNT_ID)
        action_time = payload.get(ConstantMessageSendRequestToThirdParty.ACTION_TIME)
        log_id = payload.get(ConstantMessageSendRequestToThirdParty.LOG_ID)
        log_id_start = payload.get(ConstantMessageSendRequestToThirdParty.LOG_ID_START)
        request_body = payload.get(ConstantMessageSendRequestToThirdParty.REQUEST_BODY)
        flow_name = payload.get(ConstantMessageSendRequestToThirdParty.FLOW_NAME)

        # TODO: Implement the logic to handle the message
        SendRequestOnlyReadCard.send_request(
            log_id=log_id,
            merchant_id=merchant_id,
            request_body=request_body,
            account_id=account_id,
            action_time=action_time,
            flow_name=flow_name,
            log_id_start=log_id_start,
        )
