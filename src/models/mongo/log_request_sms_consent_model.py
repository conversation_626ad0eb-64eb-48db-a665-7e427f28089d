#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 21/04/2024
"""


import datetime

from src.common import ConstantStatusRequestSmsConsent
from src.models.mongo import BaseModel


class ConstantLogRequestSmsConsentField:
    MERCHANT_ID = "merchant_id"
    LOG_ID = "log_id"
    ACCOUNT_ID = "account_id"
    ACCOUNT_USERNAME = "account_username"
    ACTION_TIME = "action_time"
    PHONE_NUMBER = "phone_number"
    PROFILE_NAME = "profile_name"
    STATUS = "status"
    TYPE = "type"
    REASON = "reason"
    MESSAGE_CONTENT_REQUEST = "message_content_request"
    BODY_RESPONSE = "body_response"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"


class LogRequestSmsConsentModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "log_request_sms_consent"

    def insert_request_sms_consent(
        self, merchant_id, log_id, account_id, account_username, action_time, phone_number, profile_name
    ):
        data_insert = {
            ConstantLogRequestSmsConsentField.MERCHANT_ID: merchant_id,
            ConstantLogRequestSmsConsentField.LOG_ID: log_id,
            ConstantLogRequestSmsConsentField.ACCOUNT_ID: account_id,
            ConstantLogRequestSmsConsentField.ACCOUNT_USERNAME: account_username,
            ConstantLogRequestSmsConsentField.ACTION_TIME: action_time,
            ConstantLogRequestSmsConsentField.PHONE_NUMBER: phone_number,
            ConstantLogRequestSmsConsentField.PROFILE_NAME: profile_name,
            ConstantLogRequestSmsConsentField.STATUS: ConstantStatusRequestSmsConsent.INIT,
            ConstantLogRequestSmsConsentField.CREATED_TIME: datetime.datetime.now(datetime.UTC),
            ConstantLogRequestSmsConsentField.UPDATED_TIME: datetime.datetime.now(datetime.UTC),
        }
        log_request_id = self.insert(data_insert).inserted_id
        return log_request_id

    def get_number_request_to_phone_in_range_time(self, merchant_id, phone_number, start_time, end_time):
        filter_option = {
            ConstantLogRequestSmsConsentField.MERCHANT_ID: merchant_id,
            ConstantLogRequestSmsConsentField.PHONE_NUMBER: phone_number,
            # ConstantLogRequestSmsConsentField.STATUS: ConstantStatusRequestSmsConsent.INIT,
            "$and": [
                {ConstantLogRequestSmsConsentField.ACTION_TIME: {"$gte": start_time}},
                {
                    ConstantLogRequestSmsConsentField.ACTION_TIME: {"$lte": end_time},
                },
            ],
        }
        return self.count(filter_option)

    def get_info_request_by_phone_number(self, merchant_id, phone_number):
        filter_option = {
            ConstantLogRequestSmsConsentField.MERCHANT_ID: merchant_id,
            ConstantLogRequestSmsConsentField.PHONE_NUMBER: phone_number,
            ConstantLogRequestSmsConsentField.STATUS: ConstantStatusRequestSmsConsent.INIT,
        }
        records = self.find(filter_option).sort({"_id": -1}).limit(1)
        records = [*records]
        if records:
            return records[0]
        return {}

    def update_status_request_sms_consent(self, request_sms_id, status, reason, body_response):
        data_update = {
            ConstantLogRequestSmsConsentField.STATUS: status,
            ConstantLogRequestSmsConsentField.UPDATED_TIME: datetime.datetime.now(datetime.UTC),
            ConstantLogRequestSmsConsentField.REASON: reason,
            ConstantLogRequestSmsConsentField.BODY_RESPONSE: body_response,
        }

        return self.update_dictionary(request_sms_id, {"$set": data_update})

    def update_message_content_request(self, merchant_id, log_id, message_content_request):
        data_update = {
            ConstantLogRequestSmsConsentField.MESSAGE_CONTENT_REQUEST: message_content_request,
        }

        return self.update_one_query(
            {
                ConstantLogRequestSmsConsentField.MERCHANT_ID: merchant_id,
                ConstantLogRequestSmsConsentField.LOG_ID: log_id,
            },
            data_update,
        )
