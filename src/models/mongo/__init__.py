#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TungDD
    Company: Mobio
    Date Created: 05/10/2021
"""
import json
import re

from bson.objectid import ObjectId
from pymongo import MongoClient

from configs import MongoConfig
from src.common.mobio_json_encode import MobioJSONEncoderV2
from src.common.utils import Base64

base_client = MongoClient(MongoConfig.MONGO_URI)
db_name = re.search(r"^mongodb://[^@]+@[^/]+/([^?$]+).*$", MongoConfig.MONGO_URI).group(1)


class BaseModel:
    client = base_client
    collection_name = ""

    def _db(self):
        return self.client[db_name]

    def get_db(self):
        collection = self._db()[self.collection_name]
        return collection

    def insert(self, dictionary):
        return self.get_db().insert_one(dictionary)

    def insert_many(self, document):
        return self.get_db().insert_many(document)

    def insert_document(self, dictionary):
        return self.get_db().insert_one(dictionary)

    def update_set_dictionary(self, search_option, dictionary):

        document = self.get_db().find_one(search_option)
        if document:
            return (
                self.get_db().update_one(filter=search_option, update={"$set": dictionary}, upsert=True).matched_count
                >= 1
            )
        return None

    def update_set_dictionary_by_id(self, entity_id, dictionary):
        if isinstance(entity_id, str):
            entity_id = ObjectId(entity_id)

        document = self.get_db().find_one({"_id": entity_id})
        if document:
            return (
                self.get_db()
                .update_one(filter={"_id": entity_id}, update={"$set": dictionary}, upsert=True)
                .matched_count
                >= 1
            )
        return None

    def update_dictionary(self, id, dictionary):
        if isinstance(id, str):
            id = ObjectId(id)
        return self.get_db().update_one({"_id": id}, dictionary).matched_count

    def update_one_query(self, query, data):
        return self.client.get_database(db_name)[self.collection_name].update_one(query, {"$set": data}).matched_count

    def update_many(self, filter_option, update_option):
        self.get_db().update_many(filter_option, update_option)

    def update_many_and_return_data(self, filter_option, update_option):
        return self.get_db().update_many(filter_option, update_option)

    def update(self, filter_option, update_option, upsert=False):
        self.get_db().update_many(filter_option, update_option, upsert=upsert)

    def update_by_set(self, filter_option, update_option, upsert=False):
        return self.get_db().update_many(filter_option, {"$set": update_option}, upsert=upsert)

    def delete_one(self, delete_options):
        return self.get_db().delete_one(delete_options)

    def delete_many(self, delete_options):
        return self.get_db().delete_many(delete_options).deleted_count

    def upsert(self, search_option, dictionary):
        out = True
        document = self.get_db().find_one(search_option)
        if document:
            document.update(dictionary)
            self.get_db().replace_one({"_id": document.get("_id")}, dictionary, upsert=True)
            out = False
        else:
            self.get_db().insert_one(dictionary)
        return out

    def find(self, search_option, obj_field_select: dict = None):
        if obj_field_select:
            return self.get_db().find(search_option, obj_field_select)
        return self.get_db().find(search_option)

    def find_one(self, search_option, fields_select: dict = None):
        if fields_select:
            return self.get_db().find_one(search_option, fields_select)
        return self.get_db().find_one(search_option)

    def collector(self):
        return self._db()[self.collection_name]

    def count_by_query(self, count_option):
        return self.get_db().count_documents(count_option)

    def count(self, search_option=None):
        if not search_option:
            search_option = {}
        return self._db()[self.collection_name].count_documents(search_option)

    def select_all(self, search_option, projection=None):
        return self.get_db().find(search_option, projection)

    def find_paginate(self, search_option, page=0, per_page=None, sort_option=None, projection=None):
        collection = self.get_db().find(search_option, projection)
        if sort_option:
            collection = collection.sort(sort_option)

        if page != -1:
            if per_page:
                collection = collection.limit(per_page)
            if page > 0:
                page -= 1
                offset = int(page) * int(per_page)
                collection = collection.skip(offset)

        return collection

    def _aggregate(self, group, match: object, sort=None, project=None, skip=None, limit=None):
        pipeline = []
        if match:
            pipeline.append({"$match": match})
        pipeline.append({"$group": group})
        if sort:
            pipeline.append({"$sort": sort})
        if project:
            pipeline.append({"$project": project})
        if skip and limit:
            pipeline.append({"$skip": skip})
            pipeline.append({"$limit": limit})
        return self.get_db().aggregate(pipeline)

    def process_aggregate_by_pipeline(self, pipeline):
        return self.get_db().aggregate(pipeline)

    def distinct(self, fields, query):

        if type(fields) is str:
            return self.get_db().distinct(fields, query)

        return None

    def find_paginate_load_more(self, search_option, per_page=10, after_token=None, sort_option=None, projection=None):
        """
        Support sort field type: number, _id
        :param search_option:
        :param per_page:
        :param after_token:
        :param sort_option: (chỉ support sort 1 field)
        :param projection: (Bao gồm field được sort)
        :return:
        """
        if not sort_option:
            sort_option = [("_id", 1)]
        else:
            check_id = next((sort for sort in sort_option if sort[0] == "_id"), None)
            order_type = next(sort[1] for sort in sort_option)
            if not check_id:
                sort_option.append(("_id", order_type))
        sort_keys = [k[0] for k in sort_option]

        if after_token:
            token = self.parse_token(after_token)
            if len(sort_option) == 1:
                if sort_option[0][0] == "_id":
                    token[0] = ObjectId(token[0])
                if sort_option[0][1] == 1:
                    search_extend = {sort_option[0][0]: {"$gt": token[0]}}
                else:
                    search_extend = {sort_option[0][0]: {"$lt": token[0]}}
            else:
                filter_id = {}
                for index, sort in enumerate(sort_option):
                    if not token[index]:
                        continue
                    if sort[0] == "_id":
                        token[index] = ObjectId(token[index])
                        if sort[1] == 1:
                            filter_id = {sort[0]: {"$gt": token[index]}}
                        else:
                            filter_id = {sort[0]: {"$lt": token[index]}}

                condition_compare = {}
                condition_combine = {}
                for index, sort in enumerate(sort_option):
                    if sort[0] != "_id":
                        condition_combine.update(
                            {
                                sort[0]: token[index],
                            }
                        )

                        if sort[1] == 1:
                            condition_compare.update({sort[0]: {"$gt": token[index]}})
                        else:
                            condition_compare.update({sort[0]: {"$lt": token[index]}})
                condition_combine.update(filter_id)
                lst_search_extend = [condition_compare, condition_combine]

                search_extend = {"$or": lst_search_extend}

            if search_option:
                search_option.update(search_extend)
            else:
                search_option = search_extend

        collection = self.collector().find(search_option, projection).limit(per_page)
        if sort_option:
            collection = collection.sort(sort_option)
        data = [x for x in collection]
        next_token = None
        if data:
            last_item = data[-1]
            next_token = self.generate_after_token(last_item, sort_keys)

        return data, next_token

    @classmethod
    def generate_after_token(cls, item, sort_keys):
        """
        Hàm dùng để generate token cho page tiếp theo. (dùng cho Mongo)
        :param item: item cuối cùng của page trước.
        :param sort_keys: Danh sách các field được sắp xếp (tuple)
        :return: token: giá trị dùng để client request lấy page tiếp theo.
        """
        data_gen_token = []
        for key in sort_keys:
            data_gen_token.append(item.get(key))
        if data_gen_token:
            data_token = MobioJSONEncoderV2().json_loads(data_gen_token)
            next_token = Base64.encode(json.dumps(data_token, ensure_ascii=False))
            return next_token

        return ""

    @classmethod
    def parse_token(cls, token):
        if not token:
            return None
        condition = json.loads(Base64.decode(token))
        return condition
