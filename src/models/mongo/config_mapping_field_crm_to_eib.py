#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 04/03/2025
"""

from src.models.mongo import BaseModel


class ConfigMappingFieldCrmToEibModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "config_mapping_field_crm_to_eib"

    def get_config_mapping_field_crm_to_eib_quick_sales(self, merchant_id):
        filter_option = {"merchant_id": merchant_id, "type": "quick_sales"}
        return self.find_one(filter_option)
