#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 26/04/2024
"""

from src.common import ConstantAttachmentType
from src.models.mongo import BaseModel


class AttachmentsModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "attachments"

    def get_attachment_by_type(self, merchant_id, log_id, attachment_type):
        filter_option = {"merchant_id": merchant_id, "log_id": log_id, "type": attachment_type}
        return self.find_one(filter_option)

    def get_attachments_by_avatar(self, merchant_id, log_id):
        return self.get_attachment_by_type(merchant_id, log_id, ConstantAttachmentType.AVATAR)

    def get_attachments_evident(self, merchant_id, log_id):
        return self.get_attachment_by_type(merchant_id, log_id, ConstantAttachmentType.EVIDENT_CONSENT)
