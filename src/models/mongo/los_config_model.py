#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/11/2024
"""

from src.common import TypeConfigLos
from src.common.init_lib import lru_redis_cache
from src.models.mongo import BaseModel


class LosConfigModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "los_config"

    @lru_redis_cache.add_for_class(expiration=10800)
    def get_los_config_status(self, merchant_id):
        filter_option = {
            "merchant_id": merchant_id,
            "type": TypeConfigLos.STATUS,
        }
        result_config = self.find_one(filter_option)
        if result_config:
            return result_config.get("config", [])
        return []
    @lru_redis_cache.add_for_class(expiration=10800)
    def get_los_config_field(self, merchant_id):
        filter_option = {
            "merchant_id": merchant_id,
            "type": TypeConfigLos.CONFIG_FIELD,
        }
        result_config = self.find_one(filter_option)
        if result_config:
            return result_config.get("config", [])
        return []
