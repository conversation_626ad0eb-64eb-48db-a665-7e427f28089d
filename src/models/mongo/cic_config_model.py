#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 22/11/2024
"""
from src.common import TypeConfigCic
from src.common.init_lib import lru_redis_cache
from src.models.mongo import BaseModel


class CicConfigModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "cic_config"

    @lru_redis_cache.add_for_class(expiration=10800)
    def get_config_status(self, merchant_id):
        filter_option = {
            "merchant_id": merchant_id,
            "type": TypeConfigCic.STATUS,
        }
        result_config = self.find_one(filter_option)
        if result_config:
            return result_config.get("config", [])
        return []

    @lru_redis_cache.add_for_class(expiration=10800)
    def get_config_products(self, merchant_id):
        filter_option = {
            "merchant_id": merchant_id,
            "type": TypeConfigCic.PRODUCTS,
        }
        result_config = self.find_one(filter_option)
        if result_config:
            return result_config.get("config", [])
        return []
