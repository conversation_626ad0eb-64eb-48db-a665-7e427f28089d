#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 20/07/2024
"""

from src.models.mongo import BaseModel


class LogRequestVerifyImageModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "log_request_verify_image"

    def count_by_log_request_id(self, merchant_id, log_request_id):

        filter_option = {
            "merchant_id": merchant_id,
            "log_id_request": log_request_id,
        }
        return self.count(filter_option)

    def insert_log_verify_image(self, merchant_id, log_request_id, action_time, account_id):

        filter_option = {
            "merchant_id": merchant_id,
            "log_id_request": log_request_id,
            "account_id": account_id,
            "action_time": action_time,
        }
        return self.insert(filter_option)
