#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 16/04/2024
"""


import datetime
import uuid

from src.common import ConstantKeyConfigSendThirdParty
from src.models.mongo import BaseModel


class LogRequestSendThirdPartyModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "log_request_send_third_party"

    def save_log_send_eib(self, request_type, config, data, info_request, response, log_id, log_id_start, time_request):
        data_insert = {
            "log_id": log_id,
            "log_id_start": log_id_start,
            "merchant_type": "eib",
            "time_request": time_request,
            "type": request_type,
            "config": {"uri": config.get(ConstantKeyConfigSendThirdParty.URI)},
            "input_data": data,
            "info_request": info_request,
            "info_response": response,
            "action_time": datetime.datetime.now(datetime.UTC),
        }

        return self.insert_document(data_insert).inserted_id

    def save_log_response_eib(self, response_type, info_response):
        data_insert = {
            "log_id": str(uuid.uuid1()),
            "merchant_type": "eib",
            "type": response_type,
            "config": None,
            "input_data": None,
            "info_request": None,
            "info_response": info_response,
            "action_time": datetime.datetime.now(datetime.UTC),
        }

        return self.insert_document(data_insert)

    def find_by_id(self, object_id):
        """
        Find a document by its ObjectId

        Args:
            object_id: The ObjectId of the document to find

        Returns:
            The document or None if not found
        """
        return self.find_one({"_id": object_id})
