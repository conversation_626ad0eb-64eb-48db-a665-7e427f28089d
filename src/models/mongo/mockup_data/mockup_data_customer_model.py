#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 13/02/2025
"""

from bson import ObjectId

from src.models.mongo import BaseModel


class MockupDataCustomerModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "mockup_data_customers"

    def get_customer_by_id_card(self, id_card):
        filter_option = {"id_card": id_card}
        return self.find_one(filter_option)

    def get_customer_by_old_id_card_no(self, old_id_card_no):
        filter_option = {"old_id_card": old_id_card_no}
        return self.find_one(filter_option)

    def get_customer_by_id(self, customer_id):
        """Get a customer by their MongoDB ObjectId"""
        try:
            filter_option = {"_id": ObjectId(customer_id)}
            return self.find_one(filter_option)
        except Exception:
            return None

    def get_customers(self, search_query=None, limit=100):
        """Get all customers with optional search filtering"""
        filter_option = {}

        if search_query:
            # Search in multiple fields
            filter_option = {
                "$or": [
                    {"name": {"$regex": search_query, "$options": "i"}},
                    {"id_card": {"$regex": search_query, "$options": "i"}},
                    {"cif": {"$regex": search_query, "$options": "i"}},
                ]
            }

        return self.find(filter_option).limit(limit)

    def create_customer(self, customer_data):
        """Create a new customer"""
        # Check for required fields
        required_fields = ["name", "dob", "id_card"]
        for field in required_fields:
            if not customer_data.get(field):
                return None, f"Missing required field: {field}"

        # Ensure proper data format
        customer_data = self._format_customer_data(customer_data)

        # Check if customer already exists with same ID card
        existing = self.get_customer_by_id_card(customer_data.get("id_card"))
        if existing:
            return None, "Customer with this ID card already exists"

        # Insert new customer
        result = self.insert(customer_data)
        if result:
            return str(result), "Customer created successfully"

        return None, "Failed to create customer"

    def update_customer(self, customer_id, customer_data):
        """Update an existing customer"""
        try:
            # Check if customer exists
            existing_customer = self.get_customer_by_id(customer_id)
            if not existing_customer:
                return False, "Customer not found"

            # Check for required fields
            required_fields = ["name", "dob", "id_card"]
            for field in required_fields:
                if not customer_data.get(field):
                    return False, f"Missing required field: {field}"

            # Ensure proper data format
            customer_data = self._format_customer_data(customer_data)

            # Check if updating would create a duplicate ID card
            existing = self.get_customer_by_id_card(customer_data.get("id_card"))
            if existing and str(existing.get("_id")) != customer_id:
                return False, "Another customer with this ID card already exists"

            # Update the customer
            filter_option = {"_id": ObjectId(customer_id)}
            result = self.update_one_query(filter_option, customer_data)

            if result:
                return True, "Customer updated successfully"

            return False, "Failed to update customer"
        except Exception as e:
            return False, f"Error updating customer: {str(e)}"

    def delete_customer(self, customer_id):
        """Delete a customer by their MongoDB ObjectId"""
        try:
            filter_option = {"_id": ObjectId(customer_id)}
            result = self.delete_one(filter_option)

            if result:
                return True, "Customer deleted successfully"

            return False, "Failed to delete customer"
        except Exception as e:
            return False, f"Error deleting customer: {str(e)}"

    def _format_customer_data(self, data):
        """Format and validate customer data"""
        # Convert checkbox values to boolean
        face_verified = False
        if "face_verified" in data and data["face_verified"]:
            face_verified = True

        id_verified = False
        if "id_verified" in data and data["id_verified"]:
            id_verified = True

        check_card = False
        if "check_card" in data and data["check_card"]:
            check_card = True

        check_aml = False
        if "check_aml" in data and data["check_aml"]:
            check_aml = True

        set_data_default = False
        if "set_data_default" in data and data["set_data_default"]:
            set_data_default = True

        # Create a sanitized customer data object
        formatted_data = {
            "name": data.get("name"),
            "dob": data.get("dob"),
            "date_of_issue": data.get("date_of_issue"),
            "gender": data.get("gender"),
            "origin": data.get("origin"),
            "ethnicity": data.get("ethnicity"),
            "id_card": data.get("id_card"),
            "cif": data.get("cif"),
            "father_name": data.get("father_name", ""),
            "mother_name": data.get("mother_name", ""),
            "face_verified": face_verified,
            "id_verified": id_verified,
            "check_card": check_card,
            "check_aml": check_aml,
            "address": data.get("address", ""),
            "old_id_card": data.get("old_id_card", ""),
            "exist_cif_with_id_card": data.get("exist_cif_with_id_card", False),
            "set_data_default": set_data_default,
            "fail_when_create_cif": data.get("fail_when_create_cif", False),
            "fail_when_create_account_indiv": data.get("fail_when_create_account_indiv", False),
            "fail_when_create_account_edigi": data.get("fail_when_create_account_edigi", False),
        }

        return formatted_data
