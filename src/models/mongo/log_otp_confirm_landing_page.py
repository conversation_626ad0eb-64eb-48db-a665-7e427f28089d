#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 20/02/2025
"""

import datetime

from src.common import ConstantQuickSales
from src.models.mongo import BaseModel


class LogOtpConfirmLandingPageModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "log_otp_confirm_landing_page"

    def create_log(self, merchant_id, phone_number, profile_name, otp, form_id, profile_id, form_token):
        time_expired = datetime.datetime.now(datetime.UTC) + datetime.timedelta(minutes=6)

        data_insert = {
            "merchant_id": merchant_id,
            "phone_number": phone_number,
            "profile_name": profile_name,
            "otp": otp,
            "form_id": form_id,
            "profile_id": profile_id,
            "form_token": form_token,
            "created_at": datetime.datetime.now(datetime.UTC),
            "status": ConstantQuickSales.ConstantStatusLogOtpConfirmLandingPage.INIT,
            "time_expired": time_expired,
        }
        return self.insert(data_insert)

    def get_otp_latest(self, filter_otp):
        result = self.find(filter_otp).sort("created_at", -1).limit(1)
        result = [*result]
        if not result:
            return None
        return result[0]

    def update_status_by_otp_id(self, otp_id, status):
        return self.update_one_query({"_id": otp_id}, {"status": status})

    def update_status_by_filter(self, filter, status):
        return self.update_one_query(filter, {"status": status})
