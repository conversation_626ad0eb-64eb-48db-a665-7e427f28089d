#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 22/12/2024
"""
from src.models.mongo import BaseModel


class ReportCicModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "report_cic"

    def aggregate_report_cic_list_by_day(self, filter_query):

        return self._aggregate(
            {
                "_id": {"day": "$action_time_day"},
                "number_new_searches": {"$sum": {"$cond": [{"$eq": ["$action", "search_new"]}, 1, 0]}},
                "number_success_searches": {"$sum": {"$cond": [{"$eq": ["$status", "success"]}, 1, 0]}},
                "number_fail_searches": {"$sum": {"$cond": [{"$eq": ["$status", "fail"]}, 1, 0]}},
            },
            filter_query,
            {"_id.day": 1},
            {
                "date": "$_id.day",
                "number_new_searches": 1,
                "number_success_searches": 1,
                "number_fail_searches": 1,
                "_id": 0,
            },
        )

    def aggregate_report_cic_list_by_user(self, filter_query):
        pipeline_query = [
            {"$match": filter_query},
            {"$sort": {"action_time": -1}},
            {"$group": {"_id": "$account_id", "latest_action": {"$first": "$$ROOT"}}},
            {"$replaceRoot": {"newRoot": "$latest_action"}},
            {
                "$group": {
                    "_id": {"day": "$action_time_day"},
                    "number_new_searches": {"$sum": {"$cond": [{"$eq": ["$action", "search_new"]}, 1, 0]}},
                    "number_success_searches": {"$sum": {"$cond": [{"$eq": ["$status", "success"]}, 1, 0]}},
                    "number_fail_searches": {"$sum": {"$cond": [{"$eq": ["$status", "fail"]}, 1, 0]}},
                }
            },
            {
                "$project": {
                    "date": "$_id.day",
                    "number_new_searches": 1,
                    "number_success_searches": 1,
                    "number_fail_searches": 1,
                    "_id": 0,
                }
            },
            {"$sort": {"date": 1}},
        ]

        return self.process_aggregate_by_pipeline(pipeline_query)

    def aggregate_total_report_cic_day(self, filter_query):

        return self._aggregate(
            {
                "_id": None,
                "total_number_new_searches": {"$sum": {"$cond": [{"$eq": ["$action", "search_new"]}, 1, 0]}},
                "total_number_searches_success": {"$sum": {"$cond": [{"$eq": ["$status", "success"]}, 1, 0]}},
                "total_number_searches_fail": {"$sum": {"$cond": [{"$eq": ["$status", "fail"]}, 1, 0]}},
            },
            filter_query,
            {},
            {
                "total_number_new_searches": 1,
                "total_number_searches_success": 1,
                "total_number_searches_fail": 1,
                "_id": 0,
            },
        )

    def aggregate_total_report_cic_user(self, filter_query):
        pipeline_query = [
            {"$match": filter_query},
            {"$sort": {"action_time": -1}},
            {"$group": {"_id": "$account_id", "latest_action": {"$first": "$$ROOT"}}},
            {"$replaceRoot": {"newRoot": "$latest_action"}},
            {
                "$group": {
                    "_id": None,
                    "total_number_new_searches": {"$sum": {"$cond": [{"$eq": ["$action", "search_new"]}, 1, 0]}},
                    "total_number_searches_success": {"$sum": {"$cond": [{"$eq": ["$status", "success"]}, 1, 0]}},
                    "total_number_searches_fail": {"$sum": {"$cond": [{"$eq": ["$status", "fail"]}, 1, 0]}},
                }
            },
            {
                "$project": {
                    "date": "$_id.day",
                    "total_number_new_searches": 1,
                    "total_number_searches_success": 1,
                    "total_number_searches_fail": 1,
                    "_id": 0,
                }
            },
        ]

        return self.process_aggregate_by_pipeline(pipeline_query)
