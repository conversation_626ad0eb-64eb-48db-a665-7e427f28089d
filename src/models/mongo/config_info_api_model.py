#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 15/04/2024
"""


import os

from mobio.sdks.admin import MobioAdminSDK

from configs import MobileBackendApplicationConfig, RedisConfig
from src.common import ConstantStatusConfig, ThirdPartyType
from src.common.init_lib import lru_redis_cache
from src.models.mongo import BaseModel


class ConfigInfoApiModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "config_info_api"

    @classmethod
    def replace_config_run_local(self, result_config):
        if "uri" in result_config:
            result_config["uri"] = (
                result_config["uri"]
                .replace("http://mobilebackend-api-service.mobio", "http://localhost")
                .replace("http://mobilebackendmockup-api-service.mobio", "http://localhost:7099/")
            )
        if "vnpay_certificates_path" in result_config:
            result_config["vnpay_certificates_path"] = result_config["vnpay_certificates_path"].replace(
                "/home/<USER>/projects/MobileBackend/",
                "/Users/<USER>/workspace/mobio/eibmobilebackend/",
            )
        if "certificates_path" in result_config:
            result_config["certificates_path"] = result_config["certificates_path"].replace(
                "/home/<USER>/projects/MobileBackend/",
                "/Users/<USER>/workspace/mobio/eibmobilebackend/",
            )
        if "path_template_send_email_success" in result_config:
            result_config["path_template_send_email_success"] = result_config["path_template_send_email_success"].replace(
                "/home/<USER>/projects/MobileBackend/",
                "/Users/<USER>/workspace/mobio/eibmobilebackend/",
            )
        if "path_template_send_email_fail" in result_config:
            result_config["path_template_send_email_fail"] = result_config["path_template_send_email_fail"].replace(
                "/home/<USER>/projects/MobileBackend/",
                "/Users/<USER>/workspace/mobio/eibmobilebackend/",
            )
        return result_config

    def get_config_info_api_not_merchant_id(self, third_party_type):
        merchant_id = "mobio"
        filter_option = {
            "status": ConstantStatusConfig.ENABLE,
            "type": third_party_type,
        }
        result = self.find_one(filter_option)
        if result:
            result_config = result.get("config", {})
            if os.getenv("VM") == "local":
                result_config = self.replace_config_run_local(result_config)

            username_encrypt = result_config.get("auth_name")
            password_encrypt = result_config.get("auth_pass")
            if username_encrypt:

                username_decrypt_object = MobioAdminSDK().decrypt_values(
                    merchant_id=merchant_id,
                    module="MOBILE_BACKEND",
                    field="username",
                    values=username_encrypt,
                )

                username = username_decrypt_object.get("data", {}).get(username_encrypt)
                result_config["auth_name"] = username

            if password_encrypt:

                password_decrypt_object = MobioAdminSDK().decrypt_values(
                    merchant_id=merchant_id,
                    module="MOBILE_BACKEND",
                    field="password",
                    values=password_encrypt,
                )
                password = password_decrypt_object.get("data", {}).get(password_encrypt)
                result_config["auth_pass"] = password
            return result_config
        return None

    # @lru_redis_cache.add_for_class(expiration=10800)
    def get_config_info_api(self, merchant_id, third_party_type):
        MobioAdminSDK().config(
            admin_host=MobileBackendApplicationConfig.ADMIN_HOST,  # admin host (VD:https://api-test1.mobio.vn/)
            redis_uri=RedisConfig.REDIS_URI,  # redis uri (VD: redis://redis-server:6378/0)
            module_use="MOBILE_BACKEND",  # liên hệ admin để khai báo tên của module
            module_encrypt="uf0+Du78XLDMn+AinbpFinBONHYe1rfR649d0922d69bf84b0e377e4c1d211785",  # liên hệ admin để lấy mã
            api_admin_version="api/v2.1",  # ["v1.0", "api/v2.0", "api/v2.1"]
        )

        filter_option = {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": third_party_type,
        }
        result = self.find_one(filter_option)
        if result:
            result_config = result.get("config", {})
            if os.getenv("VM") == "local":
                result_config = self.replace_config_run_local(result_config)

            username_encrypt = result_config.get("auth_name")
            password_encrypt = result_config.get("auth_pass")
            if username_encrypt:

                username_decrypt_object = MobioAdminSDK().decrypt_values(
                    merchant_id=merchant_id,
                    module="MOBILE_BACKEND",
                    field="username",
                    values=username_encrypt,
                )

                username = username_decrypt_object.get("data", {}).get(username_encrypt)
                result_config["auth_name"] = username if username else username_encrypt

            if password_encrypt:

                password_decrypt_object = MobioAdminSDK().decrypt_values(
                    merchant_id=merchant_id,
                    module="MOBILE_BACKEND",
                    field="password",
                    values=password_encrypt,
                )
                password = password_decrypt_object.get("data", {}).get(password_encrypt)
                result_config["auth_pass"] = password if password else password_encrypt
            return result_config
        return None

    def get_config_info_api_sent_sms_confirm_consent(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_SMS_CONFIRM_CONSENT)

    def get_config_info_api_save_customer(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_SAVE_CUSTOMER)

    def get_config_info_api_sent_face_check(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_CHECK_FACE)

    def get_config_info_api_check_customer_exist(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_EXIST)

    def get_config_info_api_sent_identification_card(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_READ_CARD)

    def get_config_info_api_sent_check_card(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_CHECK_CARD)

    def get_config_info_api_sent_insert_id_check(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_INSERT_ID_CHECK)

    def get_config_info_api_sent_id_check_insert_data_log(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_ID_CHECK_INSERT_DATA_LOG)

    def get_config_info_api_check_unique_id(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_CHECK_UNIQUE_ID)

    def get_config_info_api_sms_reply_confirm_consent(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.RESPONSE_SMS_REPLY_CONFIRM_CONSENT)

    def get_config_info_api_send_request_los_authenticate(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_LOS_AUTHENTICATE)

    def get_config_info_api_send_request_los_pull_trang_thai(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_LOS_PULL_TRANG_THAI)

    def get_config_info_api_send_request_los_pull_danh_sach(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_LOS_PULL_DANH_SACH)

    def get_config_info_custom_information_send_hpt(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.CONFIG_CUSTOM_INFORMATION_SEND_HPT)

    def get_config_info_api_send_request_inquiry_product_cic(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_INQUIRY_PRODUCT_CIC)

    def get_config_info_api_send_request_check_customer_cic(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_CIC)

    def get_config_info_api_send_request_search_history_cic(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_SEARCH_HISTORY_CIC)

    def get_config_info_api_send_request_verify_image(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_VERIFY_IMAGE)

    def get_config_info_api_send_request_get_link_file_report(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_GET_FILE_REPORT_CIC)

    def get_config_info_custom_send_request_cich2h(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.CONFIG_CUSTOM_INFORMATION_SEND_CICH2H)

    def get_config_info_custom_send_request_ecm(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.CONFIG_CUSTOM_INFORMATION_SEND_ECM)

    def get_config_info_api_send_request_ecm_cic_authenticate(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_ECM_CIC_AUTHENTICATE)

    def get_config_info_api_send_request_ecm_cic_detail_file(self, merchant_id):
        return self.get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_ECM_CIC_DETAIL_FILE)

    def get_list_config_info_api(self, merchant_id):
        filter_option = {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
        }
        result = self.find(filter_option)
        if result:
            return result
        return None


if __name__ == "__main__":
    ConfigInfoApiModel().get_config_info_api_check_customer_exist("57d559c1-39a1-4cee-b024-b953428b5ac8")
