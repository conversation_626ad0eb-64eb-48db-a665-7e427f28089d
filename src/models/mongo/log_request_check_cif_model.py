#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 14/03/2025
"""

from src.models.mongo import BaseModel


class LogRequestCheckCifModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "log_request_check_cif"

    def find_by_log_request_id(self, merchant_id, log_request_id):
        filter_option = {
            "merchant_id": merchant_id,
            "session_id": log_request_id,
        }
        return self.find_one(filter_option)
