#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 22/02/2025
"""


from bson import ObjectId

from src.common import ConstantQuickSales
from src.common.init_lib import lru_redis_cache
from src.common.utils import get_datetime_utc_to_format_datetime
from src.models.mongo import BaseModel


class LogCustomerFullFlowQuickSalesModel(BaseModel):
    # Lưu lại thông tin full luồng quick sales, từ khi submit landing page đến khi tạo được tài khoản

    def __init__(self):
        super().__init__()
        self.collection_name = "log_customer_full_flow_quick_sales"

    def init_log_customer_full_flow_quick_sales(
        self, merchant_id, staff_id, object_id, object_type, form_id, result_submit_form, form_data_submit
    ):
        data_insert = {
            "merchant_id": merchant_id,
            "staff_id": staff_id,
            "object_id": object_id,
            "object_type": object_type,
            "form_id": form_id,
            "status": ConstantQuickSales.ConstantStatusFlowQuickSales.INIT,
            "form_data_submit": form_data_submit,
            ConstantQuickSales.ConstantStepFlow.STEP_SUBMIT_FORM: result_submit_form,
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF: {},
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_ACCOUNT_INDIV: {},
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_REGISTER_EDIGI_ACCOUNT: {},
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_UPDATE_DATA_TO_CRM: {},
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_INSERT_IN4_DSA: {},
            "created_time": get_datetime_utc_to_format_datetime(),
        }
        log_full_flow = self.insert_document(data_insert)
        return log_full_flow.inserted_id

    def update_log_customer_full_flow_quick_sales(self, log_customer_full_flow_id, data_step_update):
        query_update = {"_id": ObjectId(log_customer_full_flow_id)}
        return self.update_one_query(query_update, data_step_update)

    def get_log_customer_full_flow_quick_sales_by_id(self, log_customer_full_flow_id):
        query_find = {"_id": ObjectId(log_customer_full_flow_id)}
        return self.find_one(query_find)

    @lru_redis_cache.add_for_class(expiration=500)
    def get_log_customer_full_flow_quick_sales_by_id_cache(self, log_customer_full_flow_id):
        return self.get_log_customer_full_flow_quick_sales_by_id(log_customer_full_flow_id)

    def get_log_customer_full_flow_quick_sales_by_object_id(self, merchant_id, object_id, object_type, form_id):
        query_find = {
            "merchant_id": merchant_id,
            "object_id": object_id,
            "object_type": object_type,
            "form_id": form_id,
        }
        return self.find_one(query_find)
