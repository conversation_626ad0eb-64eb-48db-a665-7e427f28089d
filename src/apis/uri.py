#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 02/04/2024
"""


class URI:
    class SMS:
        RECEIVER = "/sms/receiver"
        REQUEST_SEND_SMS_CONFIRM_CONSENT = "/sms/request/confirm-consent"

    class DECRYPTION:
        FACE_CHECK = "/face/check"
        IDENTIFICATION_CARD = "/identification-card/decryption"

    class INFORMATION_PROFILE:
        GET_AVATAR_BY_LOG_ID = "/avatars/get-by-log-id"
        GET_EVIDENT_BY_LOG_ID = "/evident-consent-by-log-id"

    class CONFIG:
        INFORMATION_CONFIG_API = "/information/config/third-party/api"

    class LOS:
        GET_LIST_STATUS = "/los/status"
        GET_LIST_LOS = "/los/actions/filter"
        DETAIL_LOS = "/los/detail-by-code"
        MERCHANT_CONFIG_FIELD = "/los/merchant/config/fields"

        UPSERT_CONFIG_STATUS = "/los/status/actions/upsert"

    class CIC:
        GET_REPORT_STATUS = "/cic/report-status"
        ACTION_SEARCH_NEW = "/cic/actions/search-new"
        GET_PRODUCT_CIC = "/cic/products"
        GET_CIC_CODE = "/cic/actions/get-code"
        GET_HISTORY = "/cic/actions/get-history"
        DETAIL_BY_SHEET_NUMBER = "/cic/detail-by-sheet-number/<sheet_number>"
        GET_LINK_FILE_REPORT = "/cic/actions/get-file-report/<report_id>"

        # Report web
        REPORT_HISTORY_LIST = "/cic/reports/history/list"
        TOTAL_REPORT_HISTORY = "/cic/reports/history/total"
        EXPORT_REPORT_HISTORY = "/cic/reports/history/exports"

        # Save view
        UPSERT_SAVE_VIEW = "/cic/actions/upsert/save-view"
        FILTER_SAVE_VIEW = "/cic/actions/filters/save-view"

        # CIC receiver search results
        CIC_RECEIVER_SEARCH_RESULTS = "/cic/receiver/search-results"

    class QUICK_SALES:
        CHECK_CIF = "/quick-sales/check/cif"
        LOCK_UNLOCK_ACCOUNT = "/quick-sales/premium-accounts/lock-unlock"
        PREMIUM_ACCOUNT_TYPES = "/quick-sales/premium-account-types"
        PREMIUM_NUMBER_TYPES_FEE = "/quick-sales/premium-number-types-fee"
        PREMIUM_NUMBER_TYPES = "/quick-sales/premium-number-types"
        FILTER_PREMIUM_ACCOUNT = "/quick-sales/premium-accounts/actions/filter"
        FILTER_ACCOUNT_NUMBER_BY_TYPE = "/quick-sales/accounts/actions/filter-by-type"
        PREMIUM_ACCOUNT_LENGTHS = "/quick-sales/premium-account-lengths"

        # Phần xác thực khuôn mặt
        FACE_RECOGNITION_VERIFY = "/face-recognition/verify"
        CITIZEN_IDS_VALIDATE = "/citizen-ids/validate"
        CITIZEN_IDS_READ_ONLY_INFORMATION = "/citizen-ids/read-only-information"

        # Landing page
        LANDING_PAGE_OTP = "/quick-sales/landing-page/otp"
        LANDING_PAGE_OTP_VERIFY = "/quick-sales/landing-page/otp/verify"
        LANDING_PAGE_SUBMIT = "/quick-sales/landing-page/submit"

        # Retry
        QUICK_SALES_RETRY = "/quick-sales/actions/retry"

    class EXCHANGE_RATE:
        LIST_EXCHANGE_RATE = "/exchange-rate/list"

    class PRE_APPROVAL:
        CHECK_CUSTOMER_EXIST = "/pre-approval/customers/check-exist"


class URI_CUSTOM:
    API_LOG_INFO = "/api/v1/<path:real_api>"
    API_CUSTOM_SAMPLE = "/api/sample/<path:real_api>"
    API_INSERT_RESULT = "/api/insert-result"


class URI_INTERNAL:
    class ADMIN:
        GET_INFORMATION_EXTRA_BY_ACCOUNT_IDS = "{host}/adm/api/v2.1/accounts/extra/by-ids"
        GET_MERCHANT_ID_BY_MERCHANT_CODE = "{host}/adm/api/v2.1/merchants/detail/by-code"
        DETAIL_ACCOUNT = "{host}/adm/api/v2.1/merchants/{merchant_id}/accounts/{account_id}"
        GET_ACCOUNT_LEVEL_LOWER = "{host}/adm/api/v2.1/accounts/eib/level-lower"

    class FORM_BUILDER:
        UPDATE_STATUS_FORM_LANDING_PAGE_EKYC = "{domain}/wfb/internal/api/v1.0/forms/process-status"
        DETAIL_FORM_BUILDER_BY_TOKEN_LANDING_PAGE_EKYC = "{domain}/wfb/internal/api/v1.0/forms/decrypt-code"
        GET_DATA_FORM_SUBMIT_BY_FORM_ID = "{domain}/wfb/internal/api/v1.0/forms/submit/transform-module"

    class PROFILING:
        PROFILING_LIST_PROFILE = "{host}/profiling/internal/v3.0/merchants/{merchant_id}/user/profile-id"
        PROFILING_LIST_PROFILE_BY_ID = "{host}/profiling/internal/v3.0/merchants/profile/profile-without-encrypt"
        PROFILING_UPDATE_CIF_PROFILE_BY_PROFILE_ID = "{host}/profiling/internal/dataflow/v3.0/profile/upsert"


class URI_MOCKUP_DATA:
    EIB_SEND_NOTIFY = "/api/v1/sendNotify"
    EIB_ADD_CIF_RET_CUST_ADD = "/api/v1/RetCustAdd"
    EIB_REGISTER_EDIGI_ACCOUNT = "/api/v1/RegisterEDigiAccount"
    EIB_VTOP_PUBLISH = "/api/v1/vtopPublish"
    EIB_READ_CARD = "/api/v1/ReadCard"
    EIB_CHECK_CARD = "/api/v1/CheckCard"
    EIB_CHECK_FACE = "/api/v1/CheckFace"
    EIB_SAVE_CUSTOMER = "/api/v1/SaveCustomer"
    EIB_CHECK_CUSTOMER_EXISTS = "/api/v1/CheckCustomerExits"
    EIB_CHECK_UNIQUE_ID = "/api/v1/CheckUniqueId"
    EIB_ID_CHECK_INSERT_DATA_LOG = "/api/v1/IdCheckInsertDataLog"
    EIB_INSERT_ID_CHECK = "/api/v1/InsertIDCheck"
    EIB_VERIFY_IMAGE = "/api/v1/VerifyImage"
    EIB_CA_ACCT_ADD_INDIV = "/api/v1/CAAcctAddIndiv"
    EIB_CHECK_CIF_FOR_LIEN = "/api/v1/CheckCIFForLien"
    EIB_CHECK_SO_DEP = "/api/v1/CheckSoDep"
    EIB_GET_LOAI_DETAILS = "/api/v1/GetLoaiDetails"
    EIB_GET_DANH_SACH_SO_DEP = "/api/v1/GetDanhSachSoDep"
    EIB_SEND_OTP = "/api/v1/SendOTP"
    EIB_LAN_LOCK_UNLOCK = "/api/v1/LANLockUnlock"
    EIB_CHECK_AML = "/api/v1/CheckAML"
    EIB_INSERT_IN4_DSA = "/api/v1/InsertIn4DSA"

    # Mockup front-end
    EIB_VIEW_WORKFLOW_TREE = "/api/v1/quick-sales/view-workflow-tree"
    EIB_CUSTOMER_FORM = "/api/v1/customer-form"
    EIB_DELETE_CUSTOMER = "/api/v1/delete-customer"
    EIB_GET_CUSTOMERS_API = "/api/v1/customers"
    EIB_GET_LOG_DETAIL = "/api/v1/log-detail"
