#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" Company: MobioVN
    Date created: 2024/04/02
"""
from mobio.sdks.base.apis.check_service import checking_service_mod

from src.apis import app
from src.apis.mockup_data.mockup_data_service import mockup_data_mod

v1_0_prefix = "/api/v1.0"

app.register_blueprint(checking_service_mod, url_prefix=v1_0_prefix)

app.register_blueprint(
    blueprint=mockup_data_mod,
    url_prefix="/",
    name="mobilebackend_internal_{}".format(mockup_data_mod.name),
)

app.register_blueprint(
    blueprint=mockup_data_mod,
    url_prefix="/mobilebackend/mockup-data",
    name="mobilebackend_mockup_data_internal_{}".format(mockup_data_mod.name),
)
