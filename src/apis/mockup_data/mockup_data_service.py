#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 11/02/2025
"""

from flask import Blueprint, redirect, render_template, request, url_for

from src.apis import HTTP
from src.apis.uri import URI_MOCKUP_DATA
from src.controllers.mockup_data.mockup_data_controller import MockupDataController

mockup_data_mod = Blueprint("mockup_data_mod", __name__)


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_SEND_NOTIFY, methods=[HTTP.METHOD.POST])
def send_notify():
    return MockupDataController().send_notify()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_ADD_CIF_RET_CUST_ADD, methods=[HTTP.METHOD.POST])
def add_cif_ret_cust():
    return MockupDataController().add_cif_ret_cust()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_REGISTER_EDIGI_ACCOUNT, methods=[HTTP.METHOD.POST])
def register_edigi_account():
    return MockupDataController().register_edigi_account()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_VTOP_PUBLISH, methods=[HTTP.METHOD.POST])
def vtop_publish():
    return MockupDataController().vtop_publish()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_READ_CARD, methods=[HTTP.METHOD.POST])
def read_card():
    return MockupDataController().read_card()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_CHECK_CARD, methods=[HTTP.METHOD.POST])
def check_card():
    return MockupDataController().check_card()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_CHECK_FACE, methods=[HTTP.METHOD.POST])
def check_face():
    return MockupDataController().check_face()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_SAVE_CUSTOMER, methods=[HTTP.METHOD.POST])
def save_customer():
    return MockupDataController().save_customer()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_CHECK_CUSTOMER_EXISTS, methods=[HTTP.METHOD.POST])
def check_customer_exists():
    return MockupDataController().check_customer_exists()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_CHECK_UNIQUE_ID, methods=[HTTP.METHOD.POST])
def check_unique_id():
    return MockupDataController().check_unique_id()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_ID_CHECK_INSERT_DATA_LOG, methods=[HTTP.METHOD.POST])
def id_check_insert_data_log():
    return MockupDataController().id_check_insert_data_log()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_INSERT_ID_CHECK, methods=[HTTP.METHOD.POST])
def insert_id_check():
    return MockupDataController().insert_id_check()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_VERIFY_IMAGE, methods=[HTTP.METHOD.POST])
def verify_image():
    return MockupDataController().verify_image()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_CA_ACCT_ADD_INDIV, methods=[HTTP.METHOD.POST])
def ca_acct_add_indiv():
    return MockupDataController().ca_acct_add_indiv()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_CHECK_CIF_FOR_LIEN, methods=[HTTP.METHOD.POST])
def check_cif_for_lien():
    return MockupDataController().check_cif_for_lien()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_CHECK_SO_DEP, methods=[HTTP.METHOD.POST])
def check_so_dep():
    return MockupDataController().check_so_dep()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_GET_LOAI_DETAILS, methods=[HTTP.METHOD.POST])
def get_loai_details():
    return MockupDataController().get_loai_details()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_GET_DANH_SACH_SO_DEP, methods=[HTTP.METHOD.POST])
def get_danh_sach_so_dep():
    return MockupDataController().get_danh_sach_so_dep()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_SEND_OTP, methods=[HTTP.METHOD.POST])
def send_otp():
    return MockupDataController().send_otp()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_LAN_LOCK_UNLOCK, methods=[HTTP.METHOD.POST])
def lan_lock_unlock():
    return MockupDataController().lan_lock_unlock()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_CHECK_AML, methods=[HTTP.METHOD.POST])
def check_aml():
    return MockupDataController().check_aml()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_INSERT_IN4_DSA, methods=[HTTP.METHOD.POST])
def insert_in4_dsa():
    return MockupDataController().insert_in4_dsa()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_VIEW_WORKFLOW_TREE, methods=[HTTP.METHOD.GET])
def view_workflow_tree():
    return MockupDataController().view_workflow_tree()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_CUSTOMER_FORM, methods=[HTTP.METHOD.GET, HTTP.METHOD.POST])
def customer_form():
    # Get data from controller
    template_data = MockupDataController().handle_customer_form()

    # Render the template with data from controller
    return render_template("mockup_data/customer_form.html", **template_data)


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_DELETE_CUSTOMER, methods=[HTTP.METHOD.POST])
def delete_customer():
    # Get customer ID from request
    customer_id = request.form.get("customer_id")

    # Pass the customer ID to the controller
    MockupDataController().handle_delete_customer(customer_id)

    # Redirect back to the customer form using the full blueprint name
    return redirect(url_for("mobilebackend_internal_mockup_data_mod.customer_form"))


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_GET_CUSTOMERS_API, methods=[HTTP.METHOD.GET])
def get_customers_api():
    return MockupDataController().get_customers_api_response()


@mockup_data_mod.route(URI_MOCKUP_DATA.EIB_GET_LOG_DETAIL, methods=[HTTP.METHOD.GET])
def get_log_detail():
    log_id = request.args.get("log_id")
    return MockupDataController().get_log_detail(log_id)
