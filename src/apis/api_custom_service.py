#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/05/2024
"""

from flask import Blueprint

from src.apis import HTTP, build_response_message, try_catch_error
from src.apis.uri import URI_CUSTOM
from src.controllers.api_custom_controller import ApiCustomController

api_custom_mod = Blueprint("api_custom_service", __name__)


@api_custom_mod.route(URI_CUSTOM.API_LOG_INFO, methods=HTTP.METHOD.SUPPORTED)
# @try_catch_error
def get_result_sample(real_api):
    return ApiCustomController().get_result_sample(real_api)


@api_custom_mod.route(URI_CUSTOM.API_CUSTOM_SAMPLE, methods=HTTP.METHOD.SUPPORTED)
# @try_catch_error
def get_custom_sample(real_api):
    return ApiCustomController().get_result_sample(real_api)


@api_custom_mod.route(URI_CUSTOM.API_INSERT_RESULT, methods=[HTTP.METHOD.POST])
@try_catch_error
def insert_result():
    return build_response_message(ApiCustomController().insert_result())
