#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""Company: MobioVN
Date created: 2024/04/02
"""
from mobio.sdks.base.apis.check_service import checking_service_mod

from src.apis import app
from src.apis.v1_0.cic_service import cic_mod
from src.apis.v1_0.config_information_service import config_information_mod
from src.apis.v1_0.decryption_service import decryption_mod
from src.apis.v1_0.exchange_rate_service import exchange_rate_mod
from src.apis.v1_0.get_information_profile_service import get_information_profile_mod
from src.apis.v1_0.los_service import los_mod
from src.apis.v1_0.pre_approval_service import pre_approval_mod
from src.apis.v1_0.quick_sales_service import quick_sales_mod
from src.apis.v1_0.sms_service import sms_mod

v1_0_prefix = "/api/v1.0"

app.register_blueprint(checking_service_mod, url_prefix=v1_0_prefix)

mobilebackend_v1_0_prefix = "/mobilebackend/api/v1.0"

app.register_blueprint(
    checking_service_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(checking_service_mod.name),
)
app.register_blueprint(
    sms_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(sms_mod.name),
)

app.register_blueprint(
    decryption_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(decryption_mod.name),
)

app.register_blueprint(
    get_information_profile_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(get_information_profile_mod.name),
)

app.register_blueprint(
    config_information_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(config_information_mod.name),
)

app.register_blueprint(
    los_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(los_mod.name),
)

app.register_blueprint(
    cic_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(cic_mod.name),
)
app.register_blueprint(
    quick_sales_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(quick_sales_mod.name),
)
app.register_blueprint(
    exchange_rate_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(exchange_rate_mod.name),
)
app.register_blueprint(
    pre_approval_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_{}".format(pre_approval_mod.name),
)
