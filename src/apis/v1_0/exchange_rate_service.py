from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.exchange_rate_controller import ExchangeRateController


exchange_rate_mod = Blueprint("exchange_rate_mod", __name__)


@exchange_rate_mod.route(URI.EXCHANGE_RATE.LIST_EXCHANGE_RATE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_list_exchange_rate():
    return build_response_message(ExchangeRateController().get_list_exchange_rate())