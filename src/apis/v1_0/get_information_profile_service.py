#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/05/2024
"""

from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.get_information_profile_controller import (
    GetInformationProfileController,
)

get_information_profile_mod = Blueprint("get_information_profile_mod", __name__)


@get_information_profile_mod.route(URI.INFORMATION_PROFILE.GET_AVATAR_BY_LOG_ID, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_avatar_by_log_id():
    return build_response_message(GetInformationProfileController().get_avatar_by_log_id())


@get_information_profile_mod.route(URI.INFORMATION_PROFILE.GET_EVIDENT_BY_LOG_ID, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_evident_by_log_id():
    return build_response_message(GetInformationProfileController().get_evident_by_log_id())
