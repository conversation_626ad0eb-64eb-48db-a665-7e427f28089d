#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 11/06/2024
"""

from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.config_information_call_api_controller import (
    ConfigInformationCallAPIController,
)

config_information_mod = Blueprint("config_information_mod", __name__)


@config_information_mod.route(URI.CONFIG.INFORMATION_CONFIG_API, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def information_config_api():
    return build_response_message(ConfigInformationCallAPIController().upsert_config())
