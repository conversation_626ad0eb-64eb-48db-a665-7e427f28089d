#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" Company: MobioVN
    Date created: 2024/04/02
"""
from mobio.sdks.base.apis.check_service import checking_service_mod

from src.apis import app
from src.apis.v1_0.external.quick_sales_landing_page import quick_sales_landing_page_mod

v1_0_prefix = "/api/v1.0"

app.register_blueprint(
    checking_service_mod, url_prefix=v1_0_prefix, name="mobilebackend_ex_{}".format(checking_service_mod.name)
)

mobilebackend_v1_0_prefix = "/mobilebackend/external/api/v1.0"

app.register_blueprint(
    checking_service_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_external_{}".format(checking_service_mod.name),
)
app.register_blueprint(
    quick_sales_landing_page_mod,
    url_prefix=mobilebackend_v1_0_prefix,
    name="mobilebackend_external_{}".format(quick_sales_landing_page_mod.name),
)
