#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 20/02/2025
"""

from flask import Blueprint

from src.apis import HTTP, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.quick_sales.landing_page_controller import LandingPageController

quick_sales_landing_page_mod = Blueprint("quick_sales_landing_page_mod", __name__)


@quick_sales_landing_page_mod.route(URI.QUICK_SALES.LANDING_PAGE_OTP, methods=[HTTP.METHOD.POST])
# @auth.verify_token
@try_catch_error
def landing_page_otp():
    return build_response_message(LandingPageController().landing_page_otp())


@quick_sales_landing_page_mod.route(URI.QUICK_SALES.LANDING_PAGE_OTP_VERIFY, methods=[HTTP.METHOD.POST])
# @auth.verify_token
@try_catch_error
def landing_page_otp_verify():
    return build_response_message(LandingPageController().landing_page_otp_verify())


@quick_sales_landing_page_mod.route(URI.QUICK_SALES.LANDING_PAGE_SUBMIT, methods=[HTTP.METHOD.POST])
# @auth.verify_token
@try_catch_error
def landing_page_submit():
    return build_response_message(LandingPageController().landing_page_submit())
