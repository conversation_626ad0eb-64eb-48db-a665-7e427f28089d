#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 13/02/2025
"""

from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.quick_sales.quick_sales_controller import QuickSalesController

quick_sales_mod = Blueprint("quick_sales_mod", __name__)


@quick_sales_mod.route(URI.QUICK_SALES.CHECK_CIF, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def check_cif():
    return build_response_message(QuickSalesController().check_cif())


@quick_sales_mod.route(URI.QUICK_SALES.LOCK_UNLOCK_ACCOUNT, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def lock_unlock_account():
    return build_response_message(QuickSalesController().lock_unlock_account())


@quick_sales_mod.route(URI.QUICK_SALES.PREMIUM_ACCOUNT_TYPES, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def premium_account_types():
    return build_response_message(QuickSalesController().premium_account_types())


@quick_sales_mod.route(URI.QUICK_SALES.PREMIUM_NUMBER_TYPES_FEE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def premium_number_types_fee():
    return build_response_message(QuickSalesController().premium_number_types_fee())


@quick_sales_mod.route(URI.QUICK_SALES.PREMIUM_NUMBER_TYPES, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def premium_number_types():
    return build_response_message(QuickSalesController().premium_number_types())


@quick_sales_mod.route(URI.QUICK_SALES.FILTER_PREMIUM_ACCOUNT, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def filter_premium_account():
    return build_response_message(QuickSalesController().filter_premium_account())


@quick_sales_mod.route(URI.QUICK_SALES.FILTER_ACCOUNT_NUMBER_BY_TYPE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def filter_account_number_by_type():
    return build_response_message(QuickSalesController().filter_account_number_by_type())


@quick_sales_mod.route(URI.QUICK_SALES.PREMIUM_ACCOUNT_LENGTHS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def premium_account_lengths():
    return build_response_message(QuickSalesController().premium_account_lengths())


@quick_sales_mod.route(URI.QUICK_SALES.FACE_RECOGNITION_VERIFY, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def face_recognition_verify():
    return build_response_message(QuickSalesController().face_recognition_verify())


@quick_sales_mod.route(URI.QUICK_SALES.CITIZEN_IDS_VALIDATE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def citizen_ids_validate():
    return build_response_message(QuickSalesController().citizen_ids_validate())


@quick_sales_mod.route(URI.QUICK_SALES.CITIZEN_IDS_READ_ONLY_INFORMATION, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def citizen_ids_read_only_information():
    return build_response_message(QuickSalesController().citizen_ids_read_only_information())


@quick_sales_mod.route(URI.QUICK_SALES.QUICK_SALES_RETRY, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def quick_sales_retry():
    return build_response_message(QuickSalesController().quick_sales_retry())
