#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 22/11/2024
"""
from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.cic_controller import Cic<PERSON><PERSON>roller

cic_mod = Blueprint("cic", __name__)


@cic_mod.route(URI.CIC.GET_REPORT_STATUS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_report_status():
    return build_response_message(CicController().get_report_status())


@cic_mod.route(URI.CIC.ACTION_SEARCH_NEW, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def action_search_new():
    return build_response_message(CicController().action_search_new())


@cic_mod.route(URI.CIC.GET_PRODUCT_CIC, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_product_cic():
    return build_response_message(CicController().get_product_cic())


@cic_mod.route(URI.CIC.GET_CIC_CODE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_cic_code():
    return build_response_message(CicController().get_cic_code())


@cic_mod.route(URI.CIC.GET_HISTORY, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_history():
    return build_response_message(CicController().get_history())


@cic_mod.route(URI.CIC.DETAIL_BY_SHEET_NUMBER, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def detail_by_sheet_number(sheet_number):
    return build_response_message(CicController().detail_by_sheet_number(sheet_number))


@cic_mod.route(URI.CIC.GET_LINK_FILE_REPORT, methods=[HTTP.METHOD.GET])
@auth.verify_token
# @try_catch_error
def get_link_file_report(report_id):
    return CicController().get_link_file_report(report_id)


@cic_mod.route(URI.CIC.UPSERT_SAVE_VIEW, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def upsert_save_view():
    return build_response_message(CicController().upsert_save_view())


@cic_mod.route(URI.CIC.FILTER_SAVE_VIEW, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def filter_save_view():
    return build_response_message(CicController().filter_save_view())


@cic_mod.route(URI.CIC.REPORT_HISTORY_LIST, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def report_history_list():
    return build_response_message(CicController().report_history_list())


@cic_mod.route(URI.CIC.TOTAL_REPORT_HISTORY, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def total_report_history():
    return build_response_message(CicController().total_report_history())


@cic_mod.route(URI.CIC.EXPORT_REPORT_HISTORY, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def export_report_history():
    return build_response_message(CicController().export_report_history())


@cic_mod.route(URI.CIC.CIC_RECEIVER_SEARCH_RESULTS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def cic_receiver_search_results():
    return build_response_message(CicController().cic_receiver_search_results())
