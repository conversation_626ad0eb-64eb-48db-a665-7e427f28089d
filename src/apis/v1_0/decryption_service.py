#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 12/04/2024
"""

from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.decryption_controller import DecryptionController

decryption_mod = Blueprint("decryption_mod", __name__)


@decryption_mod.route(URI.DECRYPTION.FACE_CHECK, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def face_check():
    return build_response_message(DecryptionController().face_check())

@decryption_mod.route(URI.DECRYPTION.IDENTIFICATION_CARD, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def identification_card():
    return build_response_message(DecryptionController().identification_card())