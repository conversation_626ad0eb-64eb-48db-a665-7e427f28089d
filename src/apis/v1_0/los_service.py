#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/11/2024
"""

from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.los_controller import LosController

los_mod = Blueprint("los_mod", __name__)


@los_mod.route(URI.LOS.GET_LIST_STATUS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_status():
    return build_response_message(LosController().get_list_status())


@los_mod.route(URI.LOS.GET_LIST_LOS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def get_los_by_filter():
    return build_response_message(LosController().get_los_by_filter())


@los_mod.route(URI.LOS.DETAIL_LOS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def detail_los():
    return build_response_message(LosController().detail_los())


@los_mod.route(URI.LOS.MERCHANT_CONFIG_FIELD, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def merchant_config_field():
    return build_response_message(LosController().merchant_config_field())


@los_mod.route(URI.LOS.UPSERT_CONFIG_STATUS, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def upsert_config_status():
    return build_response_message(LosController().upsert_config_status())
