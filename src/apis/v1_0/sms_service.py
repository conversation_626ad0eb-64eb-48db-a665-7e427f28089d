#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/04/2024
"""
from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.sms_controller import SmsController

sms_mod = Blueprint("sms_service", __name__)


@sms_mod.route(URI.SMS.RECEIVER, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def receiver_sms():
    return build_response_message(SmsController().receiver())

@sms_mod.route(URI.SMS.REQUEST_SEND_SMS_CONFIRM_CONSENT, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def request_send_sms_confirm_consent():
    return build_response_message(SmsController().request_send_sms_confirm_consent())
