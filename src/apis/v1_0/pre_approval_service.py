#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 01/07/2025
"""

from flask import Blueprint

from src.apis import HTTP, auth, build_response_message, try_catch_error
from src.apis.uri import URI
from src.controllers.pre_approval_controller import PreApprovalController

pre_approval_mod = Blueprint("pre_approval_mod", __name__)


@pre_approval_mod.route(URI.PRE_APPROVAL.CHECK_CUSTOMER_EXIST, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def check_customer_exist():
    return build_response_message(PreApprovalController().check_customer_exist())
