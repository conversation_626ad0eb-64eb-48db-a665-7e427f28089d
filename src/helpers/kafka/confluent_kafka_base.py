#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 15/04/2024
"""

import time

from mobio.libs.kafka_lib.helpers.kafka_consumer_manager import BaseKafkaConsumer

from src.models.mongo import base_client


class ConfluentKafkaConsumer(BaseKafkaConsumer):

    def __init__(self, topic_name, group_id):
        super().__init__(topic_name=topic_name, group_id=group_id, client_mongo=base_client)

    def process_msg(self, payload):
        pass

    def message_handle(self, data):
        self.process_msg(data)

    def start_consumer(self):
        time.sleep(1000)
