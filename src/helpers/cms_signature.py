# -*- coding: utf-8 -*-
""" Author: mobio
    Company: MobioVN
    Date created: 11/10/2023
"""

import base64
import hashlib
from copy import deepcopy
from datetime import datetime, timezone

from asn1crypto import algos, cms, core, x509
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.serialization import pkcs12
from oscrypto import asymmetric


class CMSSignedData(object):
    @staticmethod
    def sign_bytes(
        data_unsigned,
        key_signer,
        cert_signer,
        digest_alg="sha256",
        sig_alg="rsa",
        attrs=True,
        include_cert_signer=True,
        additional_certs=None,
        signed_value=None,
    ):
        """Takes bytes, creates a ContentInfo structure and returns it as signed bytes

        Notes:
            cert_signer is mandatory (needed to get Issuer and Serial Number ) but can be
                excluded from signed data.

        Args:
            data_unsigned (bytes): data
            key_signer (:obj:`oscrypto.asymmetric.PrivateKey`): Private key used to sign the
                message.
            cert_signer (:obj:`asn1crypto.x509.Certificate`): Certificate/Public Key
                (belonging to Private Key) that will be included in the signed message.
            digest_alg (str): Digest (Hash) Algorithm - e.g. "sha256"
            sig_alg (str): Signature Algorithm
            attrs (bool): Whether to include signed attributes (signing time). Default
                to True
            include_cert_signer (bool): Whether to include the public certificate of the signer
                in the signed data. Default to True
            additional_certs (:obj:`list` of :obj:`asn1crypto.x509.Certificate`): List of
                additional certificates to be included (e.g. Intermediate or Root CA certs).
            signed_value: unknown


        Returns:
             bytes: signed bytes

        """

        if not isinstance(data_unsigned, bytes):
            raise AttributeError("only bytes supported")

        if not isinstance(key_signer, asymmetric.PrivateKey):
            raise AttributeError("only asn1crypto.keys.PrivateKeyInfo supported")

        if not isinstance(cert_signer, x509.Certificate):
            raise AttributeError("only asn1crypto.x509.Certificate supported")

        if include_cert_signer:
            certificates = [cert_signer]
        else:
            certificates = []

        if additional_certs:
            for additional in additional_certs:
                if not isinstance(additional, x509.Certificate):
                    raise AttributeError("only asn1crypto.x509.Certificate supported")
                certificates.append(additional)

        if digest_alg not in ["md5", "sha1", "sha256", "sha512"]:
            raise AttributeError("digest algorithm unsupported: {}".format(digest_alg))

        if signed_value is None:
            signed_value = getattr(hashlib, digest_alg)(data_unsigned).digest()
        signed_time = datetime.now(tz=timezone.utc)

        signer = {
            "version": "v1",
            "sid": cms.SignerIdentifier(
                {
                    "issuer_and_serial_number": cms.IssuerAndSerialNumber(
                        {
                            "issuer": cert_signer.issuer,
                            "serial_number": cert_signer.serial_number,
                        }
                    ),
                }
            ),
            "digest_algorithm": algos.DigestAlgorithm({"algorithm": digest_alg}),
            "signature": signed_value,
        }

        pss_digest_alg = digest_alg  # use same digest algorithm for pss signature as for message

        if sig_alg == "rsa":
            signer["signature_algorithm"] = algos.SignedDigestAlgorithm({"algorithm": "rsassa_pkcs1v15"})

        elif sig_alg == "pss":
            salt_length = getattr(hashlib, pss_digest_alg)().digest_size
            signer["signature_algorithm"] = algos.SignedDigestAlgorithm(
                {
                    "algorithm": "rsassa_pss",
                    "parameters": algos.RSASSAPSSParams(
                        {
                            "hash_algorithm": algos.DigestAlgorithm({"algorithm": pss_digest_alg}),
                            "mask_gen_algorithm": algos.MaskGenAlgorithm(
                                {
                                    "algorithm": algos.MaskGenAlgorithmId("mgf1"),
                                    "parameters": {
                                        "algorithm": algos.DigestAlgorithmId(pss_digest_alg),
                                    },
                                }
                            ),
                            "salt_length": algos.Integer(salt_length),
                            "trailer_field": algos.TrailerField(1),
                        }
                    ),
                }
            )

        else:
            raise AttributeError("signature algorithm unsupported: {}".format(sig_alg))

        if attrs:
            if attrs is True:
                signer["signed_attrs"] = [
                    cms.CMSAttribute(
                        {
                            "type": cms.CMSAttributeType("content_type"),
                            "values": ("data",),
                        }
                    ),
                    cms.CMSAttribute(
                        {
                            "type": cms.CMSAttributeType("message_digest"),
                            "values": (signed_value,),
                        }
                    ),
                    cms.CMSAttribute(
                        {
                            "type": cms.CMSAttributeType("signing_time"),
                            "values": (cms.Time({"utc_time": core.UTCTime(signed_time)}),),
                        }
                    ),
                ]
            else:
                signer["signed_attrs"] = attrs

        config = {
            "version": "v1",
            "digest_algorithms": cms.DigestAlgorithms((algos.DigestAlgorithm({"algorithm": digest_alg}),)),
            "encap_content_info": {
                "content_type": "data",
            },
            "certificates": certificates,
            "signer_infos": [
                signer,
            ],
        }
        data_signed = cms.ContentInfo(
            {
                "content_type": cms.ContentType("signed_data"),
                "content": cms.SignedData(config),
            }
        )
        if attrs:
            to_sign = data_signed["content"]["signer_infos"][0]["signed_attrs"].dump()
            to_sign = b"\x31" + to_sign[1:]
        else:
            to_sign = data_unsigned

        if sig_alg == "rsa":
            signed_value_signature = asymmetric.rsa_pkcs1v15_sign(key_signer, to_sign, digest_alg.lower())

        elif sig_alg == "pss":
            signed_value_signature = asymmetric.rsa_pss_sign(key_signer, to_sign, pss_digest_alg)

        else:
            raise AttributeError("signature algorithm unsupported: {}".format(sig_alg))

        data_signed["content"]["signer_infos"][0]["signature"] = signed_value_signature

        return data_signed.dump()

    @staticmethod
    def read_key_cer_p12(private_key_path, certificate_password):
        """
        * Tạo private key
        - B1 : Tạo Private key RSA 2048 có bảo vệ bằng mật khẩu
        openssl genrsa -des3 -out Privatekey.pem 2048

        cho em xin cái pass
        - B2 : Tạo file .pem
        openssl req -new -x509 -sha256 -key Privatekey.pem -out cert.pem -days 7300
        - B3 : Tạo file .p12
        openssl pkcs12 -export -in cert.pem -inkey Privatekey.pem -out Privatekey.p12
        *, Tạo public key
        openssl pkcs12 -in Privatekey.p12 -clcerts -nokeys -out PubliKey.cer
        """

        # Đường dẫn tới tệp tin Privatekey.p12 và mật khẩu của nó
        password = certificate_password

        # Đọc nội dung của tệp tin P12
        with open(private_key_path, "rb") as p12_file:
            p12_data = p12_file.read()

        # Trích xuất khóa riêng tư từ tệp tin P12
        private_key, certificates, _ = pkcs12.load_key_and_certificates(p12_data, password.encode(), default_backend())

        private_key = private_key.private_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption(),
        )

        certificate = certificates.public_bytes(encoding=serialization.Encoding.DER)

        return private_key, certificate

    @staticmethod
    def sign_message(
        msg,
        k_signer,
        c_signer,
        digest_alg="sha256",
        sig_alg="rsa",
        attrs=True,
        include_cert_signer=True,
        additional_certs=None,
    ):

        # private key
        if not isinstance(k_signer, asymmetric.PrivateKey):
            k_signer = asymmetric.load_private_key(k_signer)

        # cert
        if not isinstance(c_signer, x509.Certificate):
            cert_signer_oscrypto = asymmetric.load_certificate(c_signer)
            c_signer = cert_signer_oscrypto.asn1

        additional_x509 = []
        if additional_certs:
            for additional in additional_certs:
                if not isinstance(additional, x509.Certificate):
                    additional_oscrypto = asymmetric.load_certificate(additional)
                    additional = additional_oscrypto.asn1

                additional_x509.append(additional)

        # make a deep copy of original message to avoid any side effects (original will not be touched)
        copied_msg = deepcopy(msg)

        data_unsigned = copied_msg.encode("utf-8")
        data_signed = CMSSignedData.sign_bytes(
            data_unsigned,
            k_signer,
            c_signer,
            digest_alg,
            sig_alg,
            attrs=attrs,
            include_cert_signer=include_cert_signer,
            additional_certs=additional_x509,
        )
        data_signed = base64.encodebytes(data_signed)

        return data_signed

    @staticmethod
    def verify_signature(public_key, data, signature):
        try:
            public_key.verify(base64.b64decode(signature), data.encode("utf-8"), padding.PKCS1v15(), hashes.SHA256())
            return True
        except Exception as e:
            return False

    @staticmethod
    def load_rsa_private_key(filepath):
        with open(filepath, "r") as key_file:
            key_data = key_file.read()
            data_load = (
                f"-----BEGIN PRIVATE KEY-----\n{key_data}\n-----END PRIVATE KEY-----"
                if "BEGIN PRIVATE KEY" not in key_data
                else key_data
            )
            try:
                # Decode Base64 content and load as PEM format
                private_key = serialization.load_pem_private_key(
                    data_load.encode(),
                    password=None,
                    backend=default_backend(),
                )
            except ValueError:
                raise ValueError("Failed to load private key. The key data may be in an incorrect format.")
        return private_key

    @staticmethod
    def load_rsa_public_key(filepath):
        with open(filepath, "r") as key_file:
            key_data = key_file.read()
            data_load = (
                f"-----BEGIN PUBLIC KEY-----\n{key_data}\n-----END PUBLIC KEY-----"
                if "BEGIN PUBLIC KEY" not in key_data
                else key_data
            )
            try:
                # Decode Base64 content and load as PEM format
                public_key = serialization.load_pem_public_key(
                    data_load.encode(),
                    backend=default_backend(),
                )
            except ValueError:
                raise ValueError("Failed to load public key. The key data may be in an incorrect format.")
        return public_key

    @staticmethod
    def gen_signature_by_file_rsa(private_key, data):
        signature = private_key.sign(data.encode("utf-8"), padding.PKCS1v15(), hashes.SHA256())
        return base64.b64encode(signature).decode("utf-8")


if __name__ == "__main__":
    # Example usage
    data_to_sign = "requestId" + "timestamp" + "idCard" + "name" + "dateOfBirth" + "gender" + "dateOfIssuance"

    # Load private key from .rsa file (PEM format)
    private_key = CMSSignedData.load_rsa_private_key("prv.rsa")

    # Generate signature
    signature = CMSSignedData.gen_signature_by_file_rsa(private_key, data_to_sign)
    print("Signature: " + signature)

    # Load public key from .rsa file (PEM format)
    public_key = CMSSignedData.load_rsa_public_key("pub.rsa")

    # Verify signature
    is_valid = CMSSignedData.verify_signature(public_key, data_to_sign, signature)
    print("Verified signature: " + str(is_valid))
