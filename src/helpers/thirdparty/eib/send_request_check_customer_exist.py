#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 05/05/2024
"""

import base64
import os
import uuid

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from configs import MobileBackendApplicationConfig, RedisConfig
from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    SHARE_FOLDER_IMAGE_VERIFY,
    ConstantMessageSendRequestToThirdParty,
    ConstantNameFlow,
    KeyConfigNotifySDK,
    StatusCode,
    StatusCodePushSocket,
    ThirdPartyType,
)
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_card_model import LogRequestCardModel

MobioMediaSDK().config(admin_host=MobileBackendApplicationConfig.ADMIN_HOST, cache_prefix=RedisConfig.CACHE_PREFIX)


class SendRequestCheckCustomerExist:
    @staticmethod
    def _send(log_id, merchant_id, request_body, account_id, action_time, log_id_start, flow_name):
        func_visit = "SendRequestCheckCustomerExist"
        log_id_push_socket = log_id_start if log_id_start else log_id
        request_body_check_customer_exist = request_body.get("body_customer_exist")
        request_body_check_check_card = request_body.get("body_check_card")

        data, config = SendRequestCheckCustomerExist._build_config_send_request_check_customer_exist(
            merchant_id, request_body_check_customer_exist
        )
        response, status_code, reasons = ThirdPartyEIB.check_customer_exists(
            merchant_id, log_id, config, data, log_id_start=log_id_start
        )
        MobioLogging().info(
            "_send_request_check_customer_exist :: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            data_send_socket = {
                "data": {
                    "status": StatusCodePushSocket.FAIL,
                    "reason": reasons,
                    "log_id": log_id_push_socket,
                    "func_visit": func_visit,
                }
            }
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )
            return

        data_response = response.get("data", {})
        userExists = data_response.get("userExists")
        data_in_data_response = data_response.get("data")
        log_request_card = LogRequestCardModel().find_by_log_request_id(merchant_id, log_id_start)
        if not log_request_card:
            MobioLogging().info("_send_request_check_customer_exist :: not log_request_card")
            return

        MobioLogging().info("data_response :: {}".format(data_response))

        faceLiveness = data_in_data_response.get("base64FaceImage")
        if faceLiveness:
            imgdata = base64.b64decode(s=faceLiveness)  # I assume you have a way of picking unique filenames
            filename = "{}.jpeg".format(str(uuid.uuid4()))
            path_face_liveness = os.path.join(SHARE_FOLDER_IMAGE_VERIFY, filename)
            with open(path_face_liveness, "wb") as f:
                f.write(imgdata)
            info_upload = MobioMediaSDK().upload_without_kafka(
                merchant_id=merchant_id,
                file_path=path_face_liveness,
                filename=filename,
                do_not_delete=True,
            )
            MobioLogging().debug("upload_file :: faceLiveness :: info_upload %s " % info_upload)
            url_file = info_upload.get("url")
            faceLiveness = url_file
        

        data_update_read_card = {
            "isAuthInformation": False,
            "isAuthTemporary": False,
            "dataResponseCheckCustomerExist": data_response,
            "isSameCardNoRequest": True,
        }
        MobioLogging().info("data_update_read_card :: {}".format(data_update_read_card))
        if flow_name in [ConstantNameFlow.TELLER_APP, ConstantNameFlow.QUICK_SALES]:
            data_update_read_card.update({"isNextAuthFace": True})
        log_request_card_id = log_request_card.get("_id")
        cardInformation = log_request_card.get("cardInformation")
        if cardInformation:
            cardInformation["faceLiveness"] = faceLiveness
        lst_func_handle = log_request_card.get("lst_func_handle", [])
        lst_func_handle.append(func_visit)
        MobioLogging().info("lst_func_handle :: {}".format(lst_func_handle))
        if userExists and userExists.lower() == "true":
            data_update_read_card.update(
                {"isAuthInformation": True, "isAuthTemporary": True, "lst_func_handle": lst_func_handle}
            )
            if flow_name in [ConstantNameFlow.TELLER_APP, ConstantNameFlow.QUICK_SALES]:
                data_update_read_card.update({"isNextAuthFace": False})

            LogRequestCardModel().update_by_set({"_id": log_request_card_id}, data_update_read_card)
            body_send_socket = {
                "status": StatusCodePushSocket.SUCCESS,
                "isAuthInformation": data_update_read_card.get("isAuthInformation"),
                "isAuthTemporary": data_update_read_card.get("isAuthTemporary"),
                "cardInformation": cardInformation,
                "func_visit": func_visit,
                "existEdigi": True,
                "isSameCardNoRequest": data_update_read_card.get("isSameCardNoRequest"),
                "log_id": log_id_push_socket,
            }
            if flow_name in [ConstantNameFlow.TELLER_APP, ConstantNameFlow.QUICK_SALES]:
                body_send_socket.update({"isNextAuthFace": data_update_read_card.get("isNextAuthFace")})
            if flow_name == ConstantNameFlow.CBBH_ADD_PROFILE and "SendRequestCheckUniqueId" in lst_func_handle:
                body_send_socket.update(
                    {
                        "cif": log_request_card.get("cif"),
                        "status_C06": data_in_data_response.get("status_C06"),
                        "result_C06": data_in_data_response.get("resultC06"),
                    }
                )
            if (
                flow_name == ConstantNameFlow.CBBH_ADD_PROFILE and "SendRequestCheckUniqueId" in lst_func_handle
            ) or flow_name == ConstantNameFlow.TELLER_APP:
                data_send_socket = {"data": body_send_socket}
                Producer().push_message_push_socket_notify_mobile(
                    merchant_id=merchant_id,
                    account_id=account_id,
                    message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                    data_send=data_send_socket,
                )
            if flow_name == ConstantNameFlow.QUICK_SALES:
                body_send_socket.update({"status": StatusCodePushSocket.FAIL, "isAuthExistCifByCustomerExist": True})
                data_send_socket = {"data": body_send_socket}
                Producer().push_message_push_socket_notify_mobile(
                    merchant_id=merchant_id,
                    account_id=account_id,
                    message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                    data_send=data_send_socket,
                )
        else:
            log_id_new = str(uuid.uuid1())
            if flow_name in [ConstantNameFlow.TELLER_APP, ConstantNameFlow.QUICK_SALES]:
                MobioLogging().info("_send_request_check_customer_exist :: log_id_new :: {}".format(log_id_new))
                data_send = {
                    ConstantMessageSendRequestToThirdParty.LOG_ID: log_id_new,
                    ConstantMessageSendRequestToThirdParty.LOG_ID_START: log_id_start,
                    ConstantMessageSendRequestToThirdParty.REQUEST_BODY: request_body_check_check_card,
                    ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_CHECK_CARD,
                }
                Producer().push_message_to_request_third_party(
                    merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
                )
            MobioLogging().info("data_update_read_card :: {}".format(data_update_read_card))
            body_send_socket = {
                "status": StatusCodePushSocket.SUCCESS,
                "isAuthInformation": data_update_read_card.get("isAuthInformation"),
                "isAuthTemporary": data_update_read_card.get("isAuthTemporary"),
                "isSameCardNoRequest": data_update_read_card.get("isSameCardNoRequest"),
                "cardInformation": cardInformation,
                "existEdigi": False,
                "func_visit": func_visit,
                "log_id": log_id_push_socket,
            }
            if flow_name in [ConstantNameFlow.TELLER_APP, ConstantNameFlow.QUICK_SALES]:
                body_send_socket.update({"isNextAuthFace": data_update_read_card.get("isNextAuthFace")})
            if flow_name == ConstantNameFlow.CBBH_ADD_PROFILE and "SendRequestCheckUniqueId" in lst_func_handle:
                body_send_socket.update(
                    {
                        "cif": log_request_card.get("cif"),
                        "status_C06": data_in_data_response.get("status_C06"),
                        "result_C06": data_in_data_response.get("resultC06"),
                    }
                )
            data_send_socket = {"data": body_send_socket}
            MobioLogging().info("data_send_socket :: {}".format(data_send_socket))
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )
            MobioLogging().info("data_send_socket :: {}".format(data_send_socket))
            LogRequestCardModel().update_by_set(
                {"_id": log_request_card_id},
                {
                    "lst_func_handle": lst_func_handle,
                    "dataResponseCheckCustomerExist": data_response,
                },
            )

    @staticmethod
    def _build_config_send_request_check_customer_exist(merchant_id, request_body):
        config = ConfigInfoApiModel().get_config_info_api_check_customer_exist(merchant_id)
        if not config:
            raise CustomError("Not config send check customer exist")

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: request_body}
            }
        }

        return data, config
