#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 05/05/2024
"""

import json
import uuid

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    ConstantMessageSendRequestToThirdParty,
    ConstantNameFlow,
    KeyConfigNotifySDK,
    StatusCode,
    StatusCodePushSocket,
    ThirdPartyType,
)
from src.controllers.decryption_controller import ConstantBodyIdentificationCard
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_card_model import LogRequestCardModel


class SendRequestReadCard:
    @staticmethod
    def _send(log_id, merchant_id, request_body, account_id, action_time, flow_name, log_id_start=None):
        func_visit = "SendRequestReadCard"
        log_id_push_socket = log_id_start if log_id_start else log_id
        data, config, sdk_request_id = SendRequestReadCard._build_config_send_identification_card(
            merchant_id, request_body
        )
        data_response, status_code, reasons = ThirdPartyEIB.read_card(
            log_id=log_id,
            config=config,
            data=data,
            merchant_id=merchant_id,
            account_id=account_id,
            log_id_start=log_id_start,
            sdk_request_id=sdk_request_id,
        )
        MobioLogging().info(
            "Sending identification_card:: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            data_send_socket = {
                "data": {
                    "status": StatusCodePushSocket.FAIL,
                    "reason": reasons,
                    "log_id": log_id_push_socket,
                    "func_visit": func_visit,
                }
            }
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )
            return

        data_insert_log_card = {
            "merchant_id": merchant_id,
            "log_id_request": log_id_push_socket,
            "account_id": account_id,
            "request_action_time": action_time,
            "flow_name": flow_name,
            "lst_func_handle": [func_visit],
        }

        if flow_name == ConstantNameFlow.TELLER_APP:
            data_insert_log_card.update(
                {
                    "isAuthInformation": False,
                    "isNextAuthFace": True,
                    "isAuthTemporary": False,
                    "isAuthC06": False,
                    "isSaveCustomer": True,
                }
            )
        else:
            data_insert_log_card.update(
                {
                    "isAuthInformation": False,
                    "isAuthTemporary": False,
                }
            )

        try:
            data_response_json = json.loads(data_response.get("dataResponse"))
        except Exception as ex:
            data_response_json = None
            data_insert_log_card.update({"error_json_load": str(ex)})
        data_insert_log_card.update({"cardInformation": data_response_json})
        """
        Data sample::
        {
            'deviceType': 'TEST_DEVICE',
            'dateOfExpiry': '10/09/2035',
            'fatherName': '', 
            'ethnic': 'Kinh', 
            'address': 'HANOI', 
            'gender': 'Nam', 
            'idCard': '056095011725', 
            'placeOfResidence': 'Lô 15, Ô 5, Thôn Đất Lành, Vĩnh Thái, Nha Trang, Khánh Hòa', 
            'motherName': '', 
            'dateOfBirth': '10/09/1995', 
            'personalSpecificIdentification': 'Sẹo chấm C:1cm5 sau cánh mũi trái', 
            'dateOfIssuance': '12/09/2022', 
            'religion': 'Phật giáo', 
            'nationality': 'Việt Nam', 
            'oldIdCardNo': '225576873', 
            'placeOfOrigin': 'Nha Trang, Khánh Hòa', 
            'name': 'Nguyễn Hồ Phi Long', 
            'spouseName': '',
            'cardImage': ''
        }
        """

        MobioLogging().info(
            "_send_identification_card_third_party :: data_response_json :: {}".format(data_response_json)
        )

        insert_log_read_card_id = LogRequestCardModel().insert_document(data_insert_log_card)
        MobioLogging().info(
            "_send_identification_card_third_party :: insert_log_read_card_id :: {}".format(
                str(insert_log_read_card_id)
            )
        )

        if data_response_json:

            cardImage = data_response_json.get("cardImage")
            if cardImage:
                data_send_gen_avatar = {
                    "log_id": log_id_push_socket,
                    "message_type": "build_avatar_profile",
                    "data_build_avatar": cardImage,
                }
                MobioLogging().info("_send_identification_card_third_party :: push message to gen avatar")
                Producer().push_message_to_build_information_related_profile(
                    merchant_id, account_id, action_time, data_send_gen_avatar
                )

            body_push_socket = {
                "body_customer_exist": {
                    "idCard": data_response_json.get("idCard"),
                    "name": data_response_json.get("name"),
                    "dateOfBirth": data_response_json.get("dateOfBirth"),
                    "gender": data_response_json.get("gender"),
                    "dateOfIssuance": data_response_json.get("dateOfIssuance"),
                },
                "body_check_unique_id": {
                    "uniqueidType": "CCUOC",
                    "uniqueId": data_response_json.get("idCard"),
                    "name": "",
                    "dob": "",
                    "taxId": "",
                },
                "body_check_card": request_body,
            }
            log_id_new = str(uuid.uuid1())
            MobioLogging().info("_send_identification_card_third_party :: log_id_new :: {}".format(log_id_new))
            data_send = {
                ConstantMessageSendRequestToThirdParty.LOG_ID: log_id_new,
                ConstantMessageSendRequestToThirdParty.LOG_ID_START: log_id_push_socket,
                ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body_push_socket,
                ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_EXIST,
                ConstantMessageSendRequestToThirdParty.FLOW_NAME: flow_name,
            }
            Producer().push_message_to_request_third_party(
                merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
            )
            if flow_name == ConstantNameFlow.CBBH_ADD_PROFILE:
                log_id_new_check_unique_id = str(uuid.uuid1())
                MobioLogging().info(
                    "_send_identification_card_third_party :: log_id_new_check_unique_id :: {}".format(
                        log_id_new_check_unique_id
                    )
                )
                data_send = {
                    ConstantMessageSendRequestToThirdParty.LOG_ID: log_id_new_check_unique_id,
                    ConstantMessageSendRequestToThirdParty.LOG_ID_START: log_id_push_socket,
                    ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body_push_socket,
                    ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_CHECK_UNIQUE_ID,
                    ConstantMessageSendRequestToThirdParty.FLOW_NAME: flow_name,
                }
                Producer().push_message_to_request_third_party(
                    merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
                )

    @staticmethod
    def _build_config_send_identification_card(merchant_id, request_body):
        config_send_identification_card = ConfigInfoApiModel().get_config_info_api_sent_identification_card(merchant_id)
        if not config_send_identification_card:
            raise CustomError("Not config send identification card")

        data_decryption = request_body.get(ConstantBodyIdentificationCard.DATA_DECRYPTION)
        sdk_request_round = request_body.get(ConstantBodyIdentificationCard.SDK_REQUEST_ROUND)
        sdk_request_session = request_body.get(ConstantBodyIdentificationCard.SDK_REQUEST_SESSION)
        request_timestamp = request_body.get(ConstantBodyIdentificationCard.REQUEST_TIMESTAMP)
        sdk_request_id = request_body.get(ConstantBodyIdentificationCard.SDK_REQUEST_ID)

        data_request = {
            "encryptedData": data_decryption,
            "requestTimeStamp": int(request_timestamp),
            "requestSession": sdk_request_session,
            "requestRound": sdk_request_round,
        }
        str_data_request = json.dumps(data_request)

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.CONTENT: str_data_request,
                },
            }
        }

        return data, config_send_identification_card, sdk_request_id
