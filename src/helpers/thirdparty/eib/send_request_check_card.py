#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 05/05/2024
"""

import json

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    ConstantNameFlow,
    KeyConfigNotifySDK,
    StatusCode,
    StatusCodePushSocket,
)
from src.controllers.decryption_controller import ConstantBodyIdentificationCard
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_card_model import LogRequestCardModel


class SendRequestCheckCard:
    @staticmethod
    def _send(log_id, merchant_id, request_body, account_id, action_time, log_id_start, flow_name):
        if flow_name == ConstantNameFlow.CBBH_ADD_PROFILE:
            MobioLogging().info("Flow name {} not handle".format(flow_name))
            return
        log_id_push_socket = log_id_start if log_id_start else log_id
        func_visit = "SendRequestCheckCard"
        data, config, sdk_request_id = SendRequestCheckCard._build_config_send_check_card(merchant_id, request_body)
        data_response, status_code, reasons = ThirdPartyEIB.check_card(
            log_id=log_id,
            config=config,
            data=data,
            merchant_id=merchant_id,
            account_id=account_id,
            log_id_start=log_id_start,
            sdk_request_id=sdk_request_id,
        )
        MobioLogging().info(
            "Sending identification_card:: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        data_send_socket_fail = {
            "data": {
                "status": StatusCodePushSocket.FAIL,
                "reason": "Xác thực C06 thất bại",
                "isAuthInformation": False,
                "isNextAuthFace": False,
                "isAuthC06": False,
                "log_id": log_id_push_socket,
                "func_visit": func_visit,
            }
        }
        data_update_read_card = {"isAuthInformation": False, "isNextAuthFace": True}
        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket_fail,
            )
            return
        try:
            data_response_json = json.loads(data_response.get("dataResponse"))
        except Exception as ex:
            data_response_json = None
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket_fail,
            )
            return
        resultCheckCard = data_response_json.get("result")
        if not resultCheckCard:
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket_fail,
            )
            return

        """
        Data sample::
        {
            'result': True,
            'deviceType': 'TEST_DEVICE',
            'dateOfExpiry': '10/09/2035',
            'fatherName': '', 
            'ethnic': 'Kinh', 
            'address': 'HANOI', 
            'gender': 'Nam', 
            'idCard': '************', 
            'placeOfResidence': 'Lô 15, Ô 5, Thôn Đất Lành, Vĩnh Thái, Nha Trang, Khánh Hòa', 
            'motherName': '', 
            'dateOfBirth': '10/09/1995', 
            'personalSpecificIdentification': 'Sẹo chấm C:1cm5 sau cánh mũi trái', 
            'dateOfIssuance': '12/09/2022', 
            'religion': 'Phật giáo', 
            'nationality': 'Việt Nam', 
            'oldIdCardNo': '*********', 
            'placeOfOrigin': 'Nha Trang, Khánh Hòa', 
            'name': 'Nguyễn Hồ Phi Long', 
            'spouseName': '',
            'cardImage': ''
        }
        """

        MobioLogging().info(
            "_send_identification_card_third_party :: data_response_json :: {}".format(data_response_json)
        )
        data_update_read_card.update({"isAuthInformation": True, "isNextAuthFace": True, "isAuthC06": True})
        log_request_card = LogRequestCardModel().find_by_log_request_id(merchant_id, log_id_start)
        if log_request_card:
            log_request_card_id = log_request_card.get("_id")
            LogRequestCardModel().update_by_set({"_id": log_request_card_id}, data_update_read_card)
            cardInformation = log_request_card.get("cardInformation")
            data_send_socket = {
                "data": {
                    "status": StatusCodePushSocket.SUCCESS,
                    "isAuthInformation": data_update_read_card.get("isAuthInformation"),
                    "isNextAuthFace": data_update_read_card.get("isNextAuthFace"),
                    "isAuthC06": data_update_read_card.get("isAuthC06"),
                    "cardInformation": cardInformation,
                    "func_visit": func_visit,
                    "log_id": log_id_push_socket,
                }
            }
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )
            return

        # insert_log_read_card_id = LogRequestCardModel().insert_document(data_insert_log_card)
        # MobioLogging().info(
        #     "_send_identification_card_third_party :: insert_log_read_card_id :: {}".format(
        #         str(insert_log_read_card_id)
        #     )
        # )

    @staticmethod
    def _build_config_send_check_card(merchant_id, request_body):
        config_send_check_card = ConfigInfoApiModel().get_config_info_api_sent_check_card(merchant_id)
        if not config_send_check_card:
            raise CustomError("Not config send check card")

        data_decryption = request_body.get(ConstantBodyIdentificationCard.DATA_DECRYPTION)
        sdk_request_round = request_body.get(ConstantBodyIdentificationCard.SDK_REQUEST_ROUND)
        sdk_request_session = request_body.get(ConstantBodyIdentificationCard.SDK_REQUEST_SESSION)
        request_timestamp = request_body.get(ConstantBodyIdentificationCard.REQUEST_TIMESTAMP)
        sdk_request_id = request_body.get(ConstantBodyIdentificationCard.SDK_REQUEST_ID)

        data_request = {
            "encryptedData": data_decryption,
            "requestTimeStamp": int(request_timestamp),
            "requestSession": sdk_request_session,
            "requestRound": sdk_request_round,
        }
        str_data_request = json.dumps(data_request)

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.CONTENT: str_data_request,
                },
            }
        }

        return data, config_send_check_card, sdk_request_id
