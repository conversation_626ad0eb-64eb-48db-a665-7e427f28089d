
import time
import requests
import json
import uuid
import base64
from mobio.libs.logging import Mo<PERSON>Logging
from datetime import datetime, timezone


from src.common import StatusCode, ThirdPartyType
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.models.mongo.log_request_send_third_party_model import LogRequestSendThirdPartyModel


class ExchangeRateHelper:
    def create_basic_auth(self, username, password):
        token = f"{username}:{password}"
        token_bytes = token.encode("ascii")
        base64_bytes = base64.b64encode(token_bytes)
        return base64_bytes.decode("ascii")
    
    @staticmethod
    def get_current_time_formatted():
        now = datetime.now(timezone.utc)
        return now.strftime("%Y-%m-%dT%H:%M:%S.") + f"{int(now.microsecond / 1000):03d}"
    
    @staticmethod
    def convert_datetime_format(iso_str):
        """
        Chuyển đổi chuỗi thời gian ISO 8601 thành định dạng DD-MM-YYYY HH:MM:SS

        Args:
            iso_str (str): chuỗi thời gian, ví dụ: '2025-06-20T16:43:46'

        Returns:
            str: chuỗi đã định dạng lại, ví dụ: '20-06-2025 16:43:46'
        """
        try:
            dt = datetime.strptime(iso_str, "%Y-%m-%dT%H:%M:%S")
            return dt.strftime("%d-%m-%Y %H:%M:%S")
        except ValueError as e:
            return f"Invalid datetime format: {e}"

    def _request_get_quotes_count_list(self, config, body):
        domain = config.get("domain")
        url = config.get("uri").format(domain) # url = f"http://{domain}/FinCore/GetExchangeRateRService/GetExchangeRate"
        
        payload = {
            "RequestUUID" : str(uuid.uuid1()),
            "ChannelId" : body.get("channel_id"),
            "MessageDateTime" : ExchangeRateHelper.get_current_time_formatted(),
            "CcyCd" : body.get("currency"),
            "SolId" : body.get("sol_id")
        }
        headers = ThirdPartyEIB.build_headers(config, payload)        
        info_request = {
            "url": url,
            "headers": headers,
            "payload": payload,
            "config": config,
        }

        result = None
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = requests.post(url=url, headers=headers, json=payload, timeout=3)
            if response.status_code == 200:
                try:
                    result = response.json()
                except Exception as ex:
                    MobioLogging().error(
                        "ExchangeRateHelper_1::_requet_get_quotes_count_list: {} ".format(ex)
                    )
            else:
                MobioLogging().error(
                    "ExchangeRateHelper_2::_requet_get_quotes_count_list: {}".format(response.content)
                )
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                ThirdPartyType.SEND_REQUEST_EXCHANGE_RATE,
                config,
                payload,
                info_request,
                response,
                str(uuid.uuid4()),
                None,
                time_request,
            )
            return data_response_json, status_code
        # save log
        time_request = time.perf_counter() - start_time
        LogRequestSendThirdPartyModel().save_log_send_eib(
            ThirdPartyType.SEND_REQUEST_EXCHANGE_RATE,
            config,
            payload,
            info_request,
            response.text,
            str(uuid.uuid4()),
            None,
            time_request,
        )
        return result, ''
            
    def _request_get_sample_exchange_rate(self, config, body):
        
        domain = config.get("domain")
        url = config.get("uri").format(domain) # url = f"http://{domain}/FinCore/GetExchangeRateRService/GetExchangeRate"
        auth_name = config.get("auth_name")       
        auth_pass = config.get("auth_pass")
        client_id = config.get("client_id")
        
        headers= {
            "Content-Type": "application/json",
            "Authorization": f"Basic {self.create_basic_auth(auth_name, auth_pass)}"
        }

        payload = {
            "RequestUUID" : str(uuid.uuid1()),
            "ChannelId" : body.get("channel_id"),
            "MessageDateTime" : ExchangeRateHelper.get_current_time_formatted(),
            "CcyCd" : body.get("currency"),
            "SolId" : body.get("sol_id")
        }
        exchange_date = body.get("exchange_date").replace("-", "")
        sol_id = body.get("sol_id")
        quotes = self._request_get_lastest_quotes(exchange_date, sol_id)
        if quotes:
            if quotes == -1:
                return {
                            "EXCHANGERATE": [],
                            "STATUS": "FAILURE",
                            "ErrorCode": "99",
                            "ErrorDesc": "Loi he thong"
                        }
            quote_cnt = quotes[0].get("QUOTECNT")
            lst_exchange_rate = self._request_get_web_exchange_rate(exchange_date, quote_cnt, sol_id)
            currency = body.get("currency") if body.get("currency") else "USD"
            if lst_exchange_rate:
                if quotes == -1:
                    return {
                                "EXCHANGERATE": [],
                                "STATUS": "FAILURE",
                                "ErrorCode": "99",
                                "ErrorDesc": "Loi he thong"
                            },StatusCode.THIRD_PARTY_FAILURE
                    
                for exchange_rate in lst_exchange_rate:
                    if exchange_rate.get("CCYCD") == currency:
                        return {
                            "EXCHANGERATE": [
                                {
                                    "QUOTECNT": exchange_rate.get("QUOTECNT"),
                                    "EXCHANGEDATE": ExchangeRateHelper.convert_datetime_format(exchange_rate.get("QUOTETM").split('+')[0]) ,
                                    "CCYCD": exchange_rate.get("CCYCD"),
                                    "BUYTRANSFER": exchange_rate.get("TTBUYRT"),
                                    "SELLTRANSFER": exchange_rate.get("TTSELLRT"),
                                    "BUYTRANSFER_INTBK": exchange_rate.get("TTBUYRT"),
                                    "SELLTRANSFER_INTBK": exchange_rate.get("TTSELLRT"),
                                    "BUYCASH": exchange_rate.get("CSHBUYRT"),
                                    "SELLCASH": exchange_rate.get("CSHSELLRT"),
                                    "BUYCASH_INTBK": exchange_rate.get("CSHBUYRT"),
                                    "SELLCASH_INTBK": exchange_rate.get("CSHSELLRT")
                                }
                            ],
                            "STATUS": "SUCCESS",
                            "ErrorCode": "0",
                            "ErrorDesc": "SUCCESS"
                        }, ""
            return {
                    "EXCHANGERATE": [],
                    "STATUS": "FAILURE",
                    "ErrorCode": "-1",
                    "ErrorDesc": "Chưa có bảng tỷ giá ngày hiện tại"
                },""  
        return {
            "EXCHANGERATE": [],
            "STATUS": "FAILURE",
            "ErrorCode": "-1",
            "ErrorDesc": "Chưa có bảng tỷ giá ngày hiện tại"
        },""
            
            
        
        
    def _request_get_lastest_quotes(self, exchange_date, sol_id):
        url = "https://eximbank.com.vn/api/front/v1/quote-count-list?strNoticeday={}&strBRCD={}".format(exchange_date, sol_id)
        headers = {
        }

        response = requests.get(url, headers=headers)
        result = None
        if response.status_code == 200:
            try:
                result = response.json()
            except Exception as ex:
                MobioLogging().error(
                    "ExchangeRateHelper_1::_request_get_lastest_quotes: {} ".format(ex)
                )
                return -1
        else:
            MobioLogging().error(
                "ExchangeRateHelper_2::_request_get_lastest_quotes: {}".format(response.content)
            )
            return -1
        return result
    
    def _request_get_web_exchange_rate(self, strNoticeday, strQuoteCNT, strBRCD):
        url = "https://eximbank.com.vn/api/front/v1/exchange-rate"
        headers = {}
        params = {
            "strNoticeday": strNoticeday,
            "strBRCD": strBRCD,
            "strQuoteCNT": strQuoteCNT
        }
        response = requests.get(url, headers=headers, params=params)

        # In kết quả ra màn hình (nếu là JSON thì in đẹp hơn)
        result = None
        if response.status_code == 200:
            try:
                result = response.json()
            except Exception as ex:
                MobioLogging().error(
                    "ExchangeRateHelper_1::_request_get_web_exchange_rate: {} ".format(ex)
                )
                return -1
        else:
            MobioLogging().error(
                "ExchangeRateHelper_2::_request_get_web_exchange_rate: {}".format(response.content)
            )
            return -1
        return result
            
if __name__ == "__main__":
    print(ExchangeRateHelper()._request_get_lastest_quotes("********", "1000"))