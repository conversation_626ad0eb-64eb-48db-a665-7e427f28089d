#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 08/05/2024
"""
import json
import os

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
)
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


class SendRequestIdCheckInsertDataLog:
    @staticmethod
    def _send(log_id, merchant_id, request_body, account_id, log_id_start):
        func_visit = "SendRequestIdCheckInsertDataLog"
        data, config = SendRequestIdCheckInsertDataLog._build(merchant_id, request_body, account_id)
        data_response, status_code, reasons = ThirdPartyEIB.id_check_insert_data_log(
            log_id, config, data, account_id, merchant_id, log_id_start
        )
        MobioLogging().info(
            "{}:: log_id: {}, status_code: {}, reasons: {}".format(
                func_visit, log_id, status_code, reasons, data_response
            )
        )

    @staticmethod
    def _build(merchant_id, request_body, account_id):
        config_send = ConfigInfoApiModel().get_config_info_api_sent_id_check_insert_data_log(merchant_id)
        if not config_send:
            raise CustomError("Not config send id check insert data log")
        solId = InternalAdminHelper().get_sol_id_by_account_id(merchant_id, account_id)
        data_request_body = {}
        if os.path.exists(request_body):
            with open(request_body, "r") as f:
                data = json.load(f)
                data_request_body = data["request_body"]
            os.remove(request_body)
        data_request_body["solId"] = solId

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: data_request_body,
                },
            }
        }

        return data, config_send
