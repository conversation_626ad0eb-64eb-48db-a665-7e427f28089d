#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 16/04/2024
"""


import base64
import copy
import datetime
import hashlib
import json
import os
import re
import sys
import time
import uuid

import jwt
import jwt.algorithms
from Crypto import Random
from Crypto.Cipher import AES
from Crypto.Util import Counter
from mobio.libs.logging import MobioLogging

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    ConstantKeyConfigSendThirdParty,
    StatusCode,
    ThirdPartyType,
)
from src.common.requests_retry import RequestRetryAdapter
from src.common.utils import replace_newlines, utf8_to_ascii
from src.helpers.cms_signature import CMSSignedData
from src.helpers.thirdparty.eib.base_send_request import BaseSendRequestThirdParty
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_check_cif_model import LogRequestCheckCifModel
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)


class ThirdPartyEIB(object):

    @staticmethod
    def sha512_hash(text):
        return hashlib.sha512(text.encode("utf-8")).hexdigest()

    @staticmethod
    def create_jwt(jwt_key, jwt_secret, jwt_audience, expires_in_seconds=86400):
        """
        Creates a JWT token in Python.

        Args:
            jwt_key (str): Key ID for the token.
            jwt_secret (str): Secret key used for signing the token.
            jwt_audience (str): Intended audience for the token.
            expires_in_seconds (int, optional): Expiration time in seconds. Defaults to 30.

        Returns:
            str: The encoded JWT token.
        """

        # Ensure secret is bytes in UTF-8 encoding
        secret_bytes = jwt_secret.encode("utf-8")

        # Create the JWT payload
        payload = {
            "iat": datetime.datetime.now(datetime.UTC),  # Issued at
            "exp": datetime.datetime.now(datetime.UTC) + datetime.timedelta(seconds=expires_in_seconds),  # Expiration
            "aud": jwt_audience,
        }

        # Create the JWT token with PyJWT library
        algorithm = "HS256"

        token = jwt.encode(payload, secret_bytes, algorithm=algorithm, headers={"kid": jwt_key})
        return token

    @staticmethod
    def get_id_card_in_test_uat(merchant_id, log_id_start):
        if os.environ.get("VM_TYPE") in ["TEST", "UAT"]:
            request_check_cif = LogRequestCheckCifModel().find_by_log_request_id(merchant_id, log_id_start)
            if request_check_cif:
                body_request = request_check_cif.get("body_request")
                identify = body_request.get("identify")
                id_card_request = identify.get("identify_value")
                return id_card_request
        return None

    @staticmethod
    def build_headers(config, payload):
        """
        Builds the headers for the request.

        Args:
            config (dict): Configuration for the request.

        Returns:
            dict: The headers for the request.
        """

        jwt_key = config.get(ConstantKeyConfigSendThirdParty.JWT_KEY)
        jwt_secret = config.get(ConstantKeyConfigSendThirdParty.JWT_SECRET)
        jwt_audience = config.get(ConstantKeyConfigSendThirdParty.JWT_AUDIENCE)
        jwt_token = ThirdPartyEIB.create_jwt(jwt_key, jwt_secret, jwt_audience)
        x_client_id = config.get(ConstantKeyConfigSendThirdParty.X_CLIENT_ID)
        # string_body = "".join(list(payload.values()))
        plaintext = json.dumps(payload, separators=(",", ":"), ensure_ascii=False)
        hash_plaintext = ThirdPartyEIB.sha512_hash(plaintext)
        MobioLogging().info("build_headers :: hash_plaintext :: {}".format(hash_plaintext))
        signature_body = ThirdPartyEIB.build_signature_by_plaintext(config, plaintext=hash_plaintext)
        basic_token = ThirdPartyEIB.create_basic_auth(
            config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME), config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        )

        headers = {
            "Content-Type": "application/json",
            "X-Authorization": "Bearer " + jwt_token,
            "X-ClientId": x_client_id,
            "X-Signature": signature_body,
            "Authorization": "Basic " + basic_token,
        }
        return headers

    @staticmethod
    def fix_header_value(header_value):
        # Remove leading whitespace
        header_value = header_value.strip()

        # Convert to bytes
        header_value_bytes = header_value.encode("utf-8")

        # Base64 encode and decode to remove invalid characters
        encoded_value = base64.b64encode(header_value_bytes)
        decoded_value = base64.b64decode(encoded_value)

        # Convert back to string
        fixed_value = decoded_value.decode("utf-8")

        return fixed_value

    @staticmethod
    def send_sms(log_id, config, data):
        request_type = ThirdPartyType.SEND_SMS_CONFIRM_CONSENT
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        access_token = config.get(ConstantKeyConfigSendThirdParty.SMS_ACCESS_TOKEN) or ""

        phone_number = to_data.get(NOTIFICATION_TO_STRUCTURE.PHONE_NUMBER)
        body_data_content = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        message = utf8_to_ascii(body_data_content)
        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")

        data_encrypted = config.get(ConstantKeyConfigSendThirdParty.DATA_ENCRYPTED).format(
            log_id=log_id, phone_number=phone_number, message=message
        )
        # MobioLogging().info("EibSender::data:: %s" % plaintext)
        ciphertext = ThirdPartyEIB.encrypt(access_token, data_encrypted)
        ciphertext = ciphertext.decode("utf-8")

        # signature
        msg_signature = str(log_id) + str(ClientId) + sent_time + data_encrypted
        signed_cert_b64 = ThirdPartyEIB.build_signature_by_plaintext(config, msg_signature)

        payload = {
            "RequestId": log_id,
            "ClientId": ClientId,
            "Timestamp": sent_time,
            "DataEncrypted": ciphertext,
            "Signature": signed_cert_b64,
            "ReferenceNo": str(uuid.uuid1()),
            "PhoneNumber": phone_number,
            "Message": message,
        }

        headers = ThirdPartyEIB.build_headers(config, payload)
        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        info_request = {"headers": headers, "payload": payload, "config": config}
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, None, time_request
            )
            return data_response_json, status_code, str(e)

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, None, time_request
        )

        return ThirdPartyEIB.handle_response(
            "ResponseCode",
            "ResponseDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def handle_response(
        key_get_response_code,
        key_get_response_desc,
        response,
        mapping_code_message_error,
        mapping_code_message_success,
    ):
        data_response = None
        reasons = ""
        status_code = None
        try:
            data_response = response.json()
            ResponseCode = data_response.get(key_get_response_code)
            ResponseDesc_response = data_response.get(key_get_response_desc)

            status_code = StatusCode.THIRD_PARTY_FAILURE
            if not ResponseCode:
                reasons = mapping_code_message_error.get(ResponseCode)
            if mapping_code_message_success.get(ResponseCode):
                status_code = StatusCode.SUCCESS
            else:
                if mapping_code_message_error.get(ResponseCode):
                    reasons = mapping_code_message_error.get(ResponseCode)
                else:
                    reasons = "Mã code lỗi {} chưa được khai báo".format(ResponseCode)

        except Exception as e:  # noqa
            MobioLogging().error("ThirdPartyEIB :: send_sms :: {}".format(e))
            result = response.text
            if not re.search("Successful|0</ResponseCode", result):
                reasons = result
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        return data_response, status_code, reasons

    @staticmethod
    def read_card(log_id, config, data, merchant_id, account_id, log_id_start, sdk_request_id):
        request_type = ThirdPartyType.SEND_REQUEST_READ_CARD
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)

        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        sent_time = datetime.datetime.now(datetime.UTC)
        sent_timestamp = sent_time.strftime("%Y-%m-%d %H:%M:%S")

        payload = {
            "requestId": log_id,
            "clientId": ClientId,
            "timestamp": sdk_request_id,
            "dataRequest": dataRequest,
            "signature": "null",
        }

        """
            Hàm này custom cho việc gọi API EIB trong môi trường TEST và UAT
            Khi đó sẽ lấy thông tin khách hàng từ bảng log_request_check_cif
        """
        id_card_request = ThirdPartyEIB.get_id_card_in_test_uat(merchant_id, log_id_start)
        if id_card_request:
            payload["idCardRequest"] = id_card_request

        headers = ThirdPartyEIB.build_headers(config, payload)

        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: read_card :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )

        return ThirdPartyEIB.handle_response(
            "responseCode",
            "responseMessage",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def check_card(log_id, config, data, merchant_id, account_id, log_id_start, sdk_request_id):
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_CARD
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)

        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        method_request_log = config.get(ConstantKeyConfigSendThirdParty.METHOD_REQUEST_LOG)
        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        sent_time = datetime.datetime.now(datetime.UTC)
        sent_timestamp = sent_time.strftime("%Y-%m-%d %H:%M:%S")

        payload = {
            "requestId": log_id,
            "clientId": ClientId,
            "timestamp": sdk_request_id,
            "dataRequest": dataRequest,
            "signature": "null",
        }

        """
            Hàm này custom cho việc gọi API EIB trong môi trường TEST và UAT
            Khi đó sẽ lấy thông tin khách hàng từ bảng log_request_check_cif
        """
        id_card_request = ThirdPartyEIB.get_id_card_in_test_uat(merchant_id, log_id_start)
        if id_card_request:
            payload["idCardRequest"] = id_card_request

        headers = ThirdPartyEIB.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:

            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            BaseSendRequestThirdParty().push_socket_send_request_insert_data_log(
                merchant_id=merchant_id,
                request_payload=payload,
                response_text=response,
                method=method_request_log,
                error_message=reasons,
                account_id=account_id,
                log_id_start=log_id_start,
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: read_card :: response :: {}".format(response.text))
        # save log
        response_text = response.text
        BaseSendRequestThirdParty().push_socket_send_request_insert_data_log(
            merchant_id=merchant_id,
            request_payload=payload,
            response_text=response_text,
            method=method_request_log,
            error_message=reasons,
            account_id=account_id,
            log_id_start=log_id_start,
        )
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )

        return ThirdPartyEIB.handle_response(
            "responseCode",
            "responseMessage",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def check_face(log_id, config, data, merchant_id, account_id, log_id_start, sdk_request_id, id_card_no=None):
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_FACE
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)

        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        method_request_log = config.get(ConstantKeyConfigSendThirdParty.METHOD_REQUEST_LOG)
        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        sent_time = datetime.datetime.now(datetime.UTC)
        sent_timestamp = sent_time.strftime("%Y-%m-%d %H:%M:%S")

        payload = {
            "requestId": log_id,
            "clientId": ClientId,
            "timestamp": sdk_request_id,
            "dataRequest": dataRequest,
            "signature": "null",
        }

        """
            Hàm này custom cho việc gọi API EIB trong môi trường TEST và UAT
            Khi đó sẽ lấy thông tin khách hàng từ bảng log_request_check_cif
        """
        id_card_request = ThirdPartyEIB.get_id_card_in_test_uat(merchant_id, log_id_start)
        if id_card_request:
            payload["idCardRequest"] = id_card_request

        payload_send_log = copy.deepcopy(payload)
        payload_send_log["idCardNo"] = id_card_no

        headers = ThirdPartyEIB.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            response_text = response.text if not isinstance(response, str) else response

            BaseSendRequestThirdParty().push_socket_send_request_insert_data_log(
                merchant_id=merchant_id,
                request_payload=payload_send_log,
                response_text=response_text,
                method=method_request_log,
                error_message=reasons,
                account_id=account_id,
                log_id_start=log_id,
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: check_face :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )
        response_text = response.text
        BaseSendRequestThirdParty().push_socket_send_request_insert_data_log(
            merchant_id=merchant_id,
            request_payload=payload_send_log,
            response_text=response_text,
            method=method_request_log,
            error_message=reasons,
            account_id=account_id,
            log_id_start=log_id,
        )

        return ThirdPartyEIB.handle_response(
            "responseCode",
            "responseDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def verify_image(log_id, config, data, merchant_id, account_id, log_id_start):
        request_type = ThirdPartyType.SEND_REQUEST_VERIFY_IMAGE
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)
        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        sent_time = datetime.datetime.now(datetime.UTC)
        sent_timestamp = sent_time.strftime("%Y-%m-%d %H:%M:%S")

        image_data = dataRequest.get("image")
        image_data = replace_newlines(image_data)
        dataRequest["image"] = image_data
        payload = {
            "requestId": log_id,
            "channel": channel,
            "timestamp": sent_timestamp,
            "data": dataRequest,
        }

        """
            Hàm này custom cho việc gọi API EIB trong môi trường TEST và UAT
            Khi đó sẽ lấy thông tin khách hàng từ bảng log_request_check_cif
        """
        id_card_request = ThirdPartyEIB.get_id_card_in_test_uat(merchant_id, log_id_start)
        if id_card_request:
            payload["idCardRequest"] = id_card_request

        image_liveness = image_data.replace("data:image/png;base64,", "")
        image_liveness = base64.b64decode(image_liveness)

        hash_object = hashlib.sha256(image_liveness)

        sum_sha_256_image = hash_object.hexdigest()

        plan_text = payload.get("requestId") + payload.get("timestamp") + sum_sha_256_image
        signed_cert_b64 = ThirdPartyEIB.gen_signature_vnpay(config, plan_text)
        payload["signature"] = signed_cert_b64

        headers = ThirdPartyEIB.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: verify_image :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )
        return ThirdPartyEIB.handle_response(
            "code",
            "desc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def check_customer_exists(merchant_id, log_id, config, data, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: check_customer_exists :: log_id :: {}, config :: {}, data :: {}".format(
                log_id, config, data
            )
        )
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_EXIST
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST)
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        msg_signature = "".join(
            [
                log_id,
                sent_time,
                dataRequest.get("idCard"),
                dataRequest.get("name"),
                dataRequest.get("dateOfBirth"),
                dataRequest.get("gender"),
                dataRequest.get("dateOfIssuance"),
            ]
        )
        signed_cert_b64 = ThirdPartyEIB.gen_signature_vnpay(config, msg_signature)

        payload = {
            "requestId": log_id,
            "timestamp": sent_time,
            "channel": channel,
            "data": dataRequest,
            "signature": signed_cert_b64,
        }

        """
            Hàm này custom cho việc gọi API EIB trong môi trường TEST và UAT
            Khi đó sẽ lấy thông tin khách hàng từ bảng log_request_check_cif
        """
        # id_card_request = ThirdPartyEIB.get_id_card_in_test_uat(merchant_id, log_id_start)
        # if id_card_request:
        #     payload["idCardRequest"] = id_card_request

        headers = ThirdPartyEIB.build_headers(config, payload)

        info_request = {"headers": headers, "payload": payload, "config": config}

        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: check_customer_exists :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )

        return ThirdPartyEIB.handle_response(
            "code",
            "desc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def save_customer(log_id, config, data, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: save_customer :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )
        request_type = ThirdPartyType.SEND_REQUEST_SAVE_CUSTOMER
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST)
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        msg_signature = "".join(
            [
                log_id,
                sent_time,
                dataRequest.get("idCard"),
                dataRequest.get("name"),
                dataRequest.get("dateOfBirth"),
                dataRequest.get("gender"),
                dataRequest.get("dateOfIssuance"),
            ]
        )
        signed_cert_b64 = ThirdPartyEIB.gen_signature_vnpay(config, msg_signature)
        payload = {
            "requestId": log_id,
            "timestamp": sent_time,
            "channel": channel,
            "data": dataRequest,
            "signature": signed_cert_b64,
        }

        headers = ThirdPartyEIB.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:

            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: save_customer :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )
        return ThirdPartyEIB.handle_response(
            "code",
            "desc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def insert_id_check(log_id, config, data, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: insert_id_check :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )
        request_type = ThirdPartyType.SEND_REQUEST_INSERT_ID_CHECK
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST)

        payload = {
            "channel": channel,
        }
        payload.update(dataRequest)

        headers = ThirdPartyEIB.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: insert_id_check :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )
        try:
            data_response_json = response.json()
            status = data_response_json.get("status")
            throwException = data_response_json.get("throwException")
            if status != "SUCCESS" or not status:
                reasons = throwException
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        except Exception as e:  # noqa
            MobioLogging().error("ThirdPartyEIB :: insert_id_check :: log_id :: error :: {}".format(log_id, str(e)))
            result = response.text
            if not re.search("SUCCESS", result):
                reasons = result
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        return data_response_json, status_code, reasons

    @staticmethod
    def id_check_insert_data_log(log_id, config, data, account_id, merchant_id, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: id_check_insert_data_log :: log_id :: {}, config :: {}, data :: {}".format(
                log_id, config, data
            )
        )
        request_type = ThirdPartyType.SEND_REQUEST_ID_CHECK_INSERT_DATA_LOG
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        # channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST)

        headers = ThirdPartyEIB.build_headers(config, dataRequest)
        info_request = {"headers": headers, "payload": dataRequest, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=dataRequest,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: id_check_insert_data_log :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )
        try:
            data_response_json = response.json()
            status = data_response_json.get("status")
            throwException = data_response_json.get("throwException")
            if status != "SUCCESS" or not status:
                reasons = throwException
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        except Exception as e:  # noqa
            MobioLogging().error("ThirdPartyEIB :: insert_id_check :: log_id :: error :: {}".format(log_id, str(e)))
            result = response.text
            if not re.search("SUCCESS", result):
                reasons = result
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        return data_response_json, status_code, reasons

    @staticmethod
    def check_unique_id(log_id, config, data, account_id, merchant_id, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: check_unique_id :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_UNIQUE_ID
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)

        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST)

        """
            Hàm này custom cho việc gọi API EIB trong môi trường TEST và UAT
            Khi đó sẽ lấy thông tin khách hàng từ bảng log_request_check_cif
        """
        id_card_request = ThirdPartyEIB.get_id_card_in_test_uat(merchant_id, log_id_start)
        if id_card_request:
            dataRequest["idCardRequest"] = id_card_request

        headers = ThirdPartyEIB.build_headers(config, dataRequest)
        info_request = {"headers": headers, "payload": dataRequest, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=dataRequest,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: check_unique_id :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )
        return ThirdPartyEIB.handle_response(
            "errorCode",
            "errorDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def encrypt(key, plaintext):
        iv = Random.new().read(AES.block_size)

        ctr = Counter.new(128, initial_value=int.from_bytes(iv, byteorder="big"))

        cipher = AES.new(bytes(key, "utf-8"), AES.MODE_CTR, counter=ctr)
        return base64.b64encode(iv + cipher.encrypt(bytes(plaintext, "utf-8")))

    @staticmethod
    def decrypt(key, ciphertext):
        enc = base64.urlsafe_b64decode(ciphertext)
        iv = enc[: AES.block_size]
        ctr = Counter.new(128, initial_value=int.from_bytes(iv, byteorder="big"))

        cipher = AES.new(bytes(key, "utf-8"), AES.MODE_CTR, counter=ctr)

        return cipher.decrypt(enc[AES.block_size :]).decode("utf-8")

    @staticmethod
    def build_signature_by_plaintext(config, plaintext):
        certificates_path = config.get(ConstantKeyConfigSendThirdParty.CERTIFICATES_PATH)
        pem_pass_phrase = config.get(ConstantKeyConfigSendThirdParty.PEM_PASS_PHRASE)
        # signature
        key_signer, cert_signer = CMSSignedData.read_key_cer_p12(
            private_key_path=certificates_path, certificate_password=pem_pass_phrase
        )
        signature = CMSSignedData.sign_message(
            plaintext, key_signer, cert_signer, digest_alg=config.get(ConstantKeyConfigSendThirdParty.DIGEST_ALG)
        )
        signed_cert_b64 = signature.decode("utf-8").replace("\n", "")
        return signed_cert_b64

    @staticmethod
    def gen_signature_vnpay(config, plaintext):
        certificates_path = config.get(ConstantKeyConfigSendThirdParty.VNPAY_CERTIFICATES_PATH)
        # pem_pass_phrase = config.get(ConstantKeyConfigSendThirdParty.PEM_PASS_PHRASE)
        # signature
        private_key = CMSSignedData.load_rsa_private_key(certificates_path)

        # Generate signature
        signature = CMSSignedData.gen_signature_by_file_rsa(private_key, plaintext)
        return signature

    @staticmethod
    def create_basic_auth(user_name: str, password: str) -> str:
        auth_str = f"{user_name}:{password}"
        auth_bytes = auth_str.encode("ascii")
        base64_bytes = base64.b64encode(auth_bytes)
        base64_str = base64_bytes.decode("ascii")
        return base64_str


if __name__ == "__main__":
    merchant_id = sys.argv[1] if len(sys.argv) > 1 else None
    print(
        # run(merchant_id)
        ThirdPartyEIB.build_headers(
            ConfigInfoApiModel().get_config_info_api_sent_sms_confirm_consent(merchant_id),
            {
                "RequestId": "f4d12bee-18e3-11ef-9c11-8125e4b97fdd",
                "ClientId": "CRM_EKYC",
                "Timestamp": "2024-05-23 09:08:01",
                "DataEncrypted": "Ixc4aumOGY3J6/dR0OCAhQMWoQVLWrXPYH6kljEmQsoNvqsPl1uP4qyxB3iHbDIZ9oGGTRmWOo8dUZTrgJ0Jfjft5ma+5RDwH2d+tTFBvocHtl2Ca4UUKbz7CmC2rpDc3loHVDiWV9mSD8Gq8gmGAeBZNYrZicKbg/zFpGkeJMA8rMzNR4cErLu2DfT4oJddJ0sa+o6sgqyjp70Ir+VcRvlSk3eMwKBKOn9zoEZICqgjxApo7hKTiFZ3KKRP0Rb6Dw317SV4xbELF5AagOU95xNbpJCnHffL/FWPEGNfWckyo3hgSTdYG2MUzsOrPjrX652uOXUz2uT5pcqHTSc+yB0uPhwI443D6+lwwBILOsdJWO0Grq4P5ZNTIHmmKwaNFDatSSgO45bL/2WUGksHEqTFFMj9h3p3Sz3JbqmTIgySk/wo6Lb1CLf0zCjzPUeSntBUcUKygRdX9ZOG60yo8/nh17+V9cQqgtFkm39XWj3q7rCfcx4wk6UrGWFZPQ==",
                "Signature": "MIIFlQYJKoZIhvcNAQcCoIIFhjCCBYICAQExDzANBglghkgBZQMEAgEFADALBgkqhkiG9w0BBwGg\nggNpMIIDZTCCAk2gAwIBAgIUTaTEY7TmENZlOR5pTrm3QdVSEaMwDQYJKoZIhvcNAQELBQAwQjEL\nMAkGA1UEBhMCVk4xFTATBgNVBAcMDERlZmF1bHQgQ2l0eTEcMBoGA1UECgwTRGVmYXVsdCBDb21w\nYW55IEx0ZDAeFw0yNDA1MjAwODE0MjJaFw00NDA1MTUwODE0MjJaMEIxCzAJBgNVBAYTAlZOMRUw\nEwYDVQQHDAxEZWZhdWx0IENpdHkxHDAaBgNVBAoME0RlZmF1bHQgQ29tcGFueSBMdGQwggEiMA0G\nCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDDL1WNNXITYjbn4ufGrL75Q6xE26em/wCUjkYqIhGl\n9eFotnJp61u6bU2o71zXkfsu92RDYbJLSCDM8VhiJs1vQI5TlZrGTg2El7ELzZ3UcxhFk40adwYZ\ntVsK1b5FKNNJ1cJbv7hHiGrJQVHsA4VgOZ+0P5Si23sx6w/bb6JMeD5tI5SbvStANUOd9QjAnJEn\nTztLCccN3oEq7PRUwZWRdph3fiSHXekl4unb3pjeL1wrA2H/aoq6PwqQ4GgbN7AhSgbZQCy3KGLV\n+iWuv4buhAsn0t6xM0BqkZ4hn7QctEqENovo2tq7HbnOv9o9RXXi4elGaq5IVik85B2JyiqBAgMB\nAAGjUzBRMB0GA1UdDgQWBBQ+tFsYffHUEfgIQwty5VGeY89pzDAfBgNVHSMEGDAWgBQ+tFsYffHU\nEfgIQwty5VGeY89pzDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQC7Ngh5jKsi\nmZ2a6TE1lcHdezDPUh005vvQ8qtXog1BY2G3raDURPiYCQF6SOL1G80XHXieVPLWjPRiOW+BmGuq\ncnzMDqCoobgvNeGJ4I8oERAbHC17SKA6zCqYS0KIbDZ24IzwBllbgczK6h2MfurGsNPIhcLEU5Ry\npdVwryyCAYu04WCCEglYQ3HS8K/Xn22nEEXvHIwwbR/yd5tWPVyQGJKODCVY4DDhc1PVvudoXUmN\nLBSvAsG/iNXML2om/q2MdouqxrKELByJSZJM3X1wriIXWXyghjO5b3P2xAwnIkNjEM8JPjj84UNw\n8XLdfBt2v6jhcd5oXNXckNm2UWPYMYIB8DCCAewCAQEwWjBCMQswCQYDVQQGEwJWTjEVMBMGA1UE\nBwwMRGVmYXVsdCBDaXR5MRwwGgYDVQQKDBNEZWZhdWx0IENvbXBhbnkgTHRkAhRNpMRjtOYQ1mU5\nHmlOubdB1VIRozANBglghkgBZQMEAgEFAKBpMBgGCSqGSIb3DQEJAzELBgkqhkiG9w0BBwEwHAYJ\nKoZIhvcNAQkFMQ8XDTI0MDUyMzA5MDgwMVowLwYJKoZIhvcNAQkEMSIEIEbAgV8pTpwRzsmg1K1H\nq9q6ToM1e9IehA+6uIv5OwYJMA0GCSqGSIb3DQEBAQUABIIBAJsXihFrNjnMQFKsKnuxeCQ7FxS5\nNJGizrtuHrK8Bc132VWv5jJw1pKti/Xptbj0EIZ/flLMNOV1kd5mUIJvIdFibuCsarNKnx30IuRt\n+Orf8taLd6+cKRtP+wgZ14ciGXa/HiFC+2o0YSnJl62JNDSXLWvj0zVdTGrYp8yhb4LNVsp6NF8Z\nwPjJUWyxYs2av6P2FJAalz+Q/74cDTP8GA5ugsrY8q1roTRvC7s7Dr60lg/33P6bITOv0IBS1dw5\ncpiO5O1FuHIF0iQaYI4Lqi56aumjQsd9JMEG92Gkuuae9HtKOPK5C+n6WcN/xogtw43vgTwnVNul\nckpaY9vLDVc=\n",
                "ReferenceNo": "f4dbe6b7-18e3-11ef-bf6f-8125e4b97fdd",
                "PhoneNumber": "***********",
                "Message": "Quy khach Dao Duc Tung 1 dang cung cap thong tin CCCD de dang ky SPDV tai Eximbank. Soan tin YES gui 8149 de dong y hoac goi ******** de duoc ho tro.",
            },
        )
    )
