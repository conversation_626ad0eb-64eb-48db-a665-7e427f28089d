#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 19/05/2024
"""
import datetime
import uuid

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    KeyConfigNotifySDK,
    StatusCodePushSocket,
)
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_card_model import LogRequestCardModel


class SendRequestCheckUniqueId(object):
    @staticmethod
    def _send(log_id, merchant_id, request_body, action_time, account_id, log_id_start, flow_name):
        log_id_push_socket = log_id_start if log_id_start else log_id
        func_visit = "SendRequestCheckUniqueId"
        data, config = SendRequestCheckUniqueId._build(merchant_id, request_body, account_id)

        MobioLogging().info("send_request_check_unique_id :: data: %s, config :: %s" % (data, config))

        data_response, status_code, reasons = ThirdPartyEIB.check_unique_id(
            log_id, config, data, account_id, merchant_id, log_id_start
        )
        MobioLogging().info(
            "{}:: log_id: {}, status_code: {}, reasons: {}".format(
                func_visit, log_id, status_code, reasons, data_response
            )
        )
        cifInformation = None
        if data_response and data_response.get("errorCode") in ["99", "93"]:
            cifInformation = data_response.get("errorDesc")

        log_request_card = LogRequestCardModel().find_by_log_request_id(merchant_id, log_id_start)
        if not log_request_card:
            MobioLogging().info("SendRequestCheckUniqueId :: not log_request_card")
            return

        lst_func_handle = log_request_card.get("lst_func_handle")
        lst_func_handle.append(func_visit)
        is_push_socket = False
        if "SendRequestCheckCustomerExist" in lst_func_handle:
            is_push_socket = True
        log_request_card_id = log_request_card.get("_id")
        cardInformation = log_request_card.get("cardInformation")
        LogRequestCardModel().update_by_set(
            {"_id": log_request_card_id}, {"cif": cifInformation, "lst_func_handle": lst_func_handle}
        )

        if is_push_socket:
            body_send_socket = {
                "flow_name": flow_name,
                "status": StatusCodePushSocket.SUCCESS,
                "cif": cifInformation,
                "isAuthInformation": log_request_card.get("isAuthInformation"),
                "isAuthTemporary": log_request_card.get("isAuthTemporary"),
                "cardInformation": cardInformation,
                "func_visit": func_visit,
                "log_id": log_id_push_socket,
            }
            data_send_socket = {"data": body_send_socket}
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )

    @staticmethod
    def _build(merchant_id, request_body, account_id):
        config_send = ConfigInfoApiModel().get_config_info_api_check_unique_id(merchant_id)
        if not config_send:
            raise CustomError("Not config send id check unique id")

        data_request = request_body.get("body_check_unique_id")
        messageDateTime = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000")
        data_request.update({"requestUUID": str(uuid.uuid1()), "messageDateTime": messageDateTime})
        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: data_request,
                },
            }
        }

        return data, config_send
