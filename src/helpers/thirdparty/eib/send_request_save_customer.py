#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 05/05/2024
"""


import json
import os

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    KeyConfigNotifySDK,
    StatusCode,
    StatusCodePushSocket,
)
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


class SendRequestSaveCustomer(object):

    @staticmethod
    def _send(log_id, merchant_id, request_body, account_id, action_time, log_id_start, flow_name):
        func_visit = "SendRequestSaveCustomer"
        data, config = SendRequestSaveCustomer._build(merchant_id, request_body)
        log_id_push_socket = log_id_start if log_id_start else log_id
        data_response, status_code, reasons = ThirdPartyEIB.save_customer(
            log_id, config, data, log_id_start=log_id_start
        )
        MobioLogging().info(
            "SendRequestSaveCustomer:: log_id: {}, status_code: {}, reasons: {}".format(log_id, status_code, reasons)
        )
        is_save_customer = True
        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            is_save_customer = False
        data_of_data_send_socket = {
            "status": StatusCodePushSocket.SUCCESS,
            "log_id": log_id_push_socket,
            "func_visit": func_visit,
        }

        data_send_socket_of_check_face = request_body.get("body_send_socket_after_save_customer", {})
        data_send_socket_of_check_face["isSaveCustomer"] = is_save_customer
        data_of_data_send_socket.update(data_send_socket_of_check_face)
        data_send_socket = {"data": data_of_data_send_socket}
        Producer().push_message_push_socket_notify_mobile(
            merchant_id=merchant_id,
            account_id=account_id,
            message_type=KeyConfigNotifySDK.MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION,
            data_send=data_send_socket,
        )

    @staticmethod
    def _build(merchant_id, request_body):
        config_send = ConfigInfoApiModel().get_config_info_api_save_customer(merchant_id)
        if not config_send:
            raise CustomError("Not config send save customer")

        data_request = request_body.get("body_save_customer")
        if data_request:
            path_face_image = data_request.get("faceImage")
            if path_face_image:
                if os.path.exists(path_face_image):
                    with open(path_face_image, "rb") as f:
                        json_data = json.load(f)
                        face_image = json_data.get("face_image")
                    data_request["faceImage"] = face_image
                    os.remove(path_face_image)

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: data_request,
                },
            }
        }

        return data, config_send
