#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 09/05/2024
"""


import datetime
import json
import os
from uuid import uuid1

from src.common import (
    SHARE_FOLDER_IMAGE_VERIFY,
    ConstantMessageSendRequestToThirdParty,
    ThirdPartyType,
)
from src.message_queue.kafka.producer.push_message_to_topic import Producer


class BaseSendRequestThirdParty(object):

    def _build_data_send_id_check_insert_data_log(self, request, response_text, method, error_message, account_id):
        return {
            "request": request,
            "response": response_text,
            "method": method,
            "error": error_message,
            "createdBy": account_id,
        }

    def push_socket_send_request_insert_data_log(
        self, merchant_id, request_payload, response_text, method, error_message, account_id, log_id_start
    ):
        str_request = json.dumps(request_payload)
        request_body = self._build_data_send_id_check_insert_data_log(
            str_request, response_text, method, error_message, account_id
        )
        action_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")

        path_file = os.path.join(SHARE_FOLDER_IMAGE_VERIFY, "request_body_{}.json".format(str(uuid1())))
        with open(path_file, "w") as f:
            json.dump({"request_body": request_body}, f)

        data_send = {
            ConstantMessageSendRequestToThirdParty.LOG_ID: str(uuid1()),
            ConstantMessageSendRequestToThirdParty.LOG_ID_START: log_id_start,
            ConstantMessageSendRequestToThirdParty.REQUEST_BODY: path_file,
            ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_ID_CHECK_INSERT_DATA_LOG,
        }
        Producer().push_message_to_request_third_party(
            merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
        )
