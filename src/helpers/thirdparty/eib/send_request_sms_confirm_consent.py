#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 05/05/2024
"""

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    ConstantKeyConfigSendThirdParty,
)
from src.common.utils import utf8_to_ascii
from src.controllers.sms_controller import ConstantBodyRequestConfirmConsent
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_sms_consent_model import LogRequestSmsConsentModel


class SendSmsConfirmConsent(object):

    @staticmethod
    def _send(log_id, merchant_id, request_body, account_id, action_time, flow_name):
        config, data = SendSmsConfirmConsent._build_config_send_sms_confirm_consent(merchant_id, log_id, request_body)
        data_response, status_code, reasons = ThirdPartyEIB.send_sms(log_id, config, data=data)
        MobioLogging().info(
            "Sending confirm consent :: log_id: {}, status_code: {}, reasons: {}".format(log_id, status_code, reasons)
        )

    @staticmethod
    def _build_config_send_sms_confirm_consent(merchant_id, log_id, request_body):
        config_send_sms = ConfigInfoApiModel().get_config_info_api_sent_sms_confirm_consent(merchant_id)
        if not config_send_sms:
            raise CustomError("Not config send sms")
        format_sms_send = config_send_sms.get(ConstantKeyConfigSendThirdParty.FORMAT_SMS)
        request_body["profile_name"] = utf8_to_ascii(request_body["profile_name"])
        content = format_sms_send.format(**request_body)

        MobioLogging().info("send_request_sms_confirm_consent :: update content: %s", content)
        LogRequestSmsConsentModel().update_message_content_request(merchant_id, log_id, content)
        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.CONTENT: content,
                },
                NOTIFICATION_TO_STRUCTURE.PHONE_NUMBER: request_body.get(
                    ConstantBodyRequestConfirmConsent.PHONE_NUMBER
                ),
            }
        }
        return config_send_sms, data
