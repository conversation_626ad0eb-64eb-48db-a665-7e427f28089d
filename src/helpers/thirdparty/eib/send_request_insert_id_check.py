#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 08/05/2024
"""


import json
import os

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
)
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


class SendRequestInsertIdCheck:
    @staticmethod
    def _send(log_id, merchant_id, request_body, account_id, action_time, log_id_start, flow_name):
        func_visit = "SendRequestInsertIdCheck"
        data, config = SendRequestInsertIdCheck._build(merchant_id, request_body, log_id_start)
        data_response, status_code, reasons = ThirdPartyEIB.insert_id_check(
            log_id, config, data, log_id_start=log_id_start
        )
        MobioLogging().info(
            "SendRequestInsertIdCheck:: log_id: {}, status_code: {}, reasons: {}".format(log_id, status_code, reasons)
        )

    @staticmethod
    def _build(merchant_id, request_body, log_id_start):
        config = ConfigInfoApiModel().get_config_info_api_sent_insert_id_check(merchant_id)
        if not config:
            raise CustomError("Not config send insert id check")

        data_request = request_body.get("body_insert_id_check")
        if data_request:
            path_face_image = data_request.get("faceImage")
            if path_face_image:
                if os.path.exists(path_face_image):
                    with open(path_face_image, "rb") as f:
                        json_data = json.load(f)
                        face_image = json_data.get("face_image")
                    data_request["faceImage"] = face_image
                    os.remove(path_face_image)
        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: data_request,
                },
            }
        }

        return data, config
