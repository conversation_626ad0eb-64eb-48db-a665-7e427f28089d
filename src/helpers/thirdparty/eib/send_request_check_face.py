#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 05/05/2024
"""

import base64
import json
import os
import uuid
from datetime import datetime

from mobio.libs.kafka_lib.helpers.batch_consumer_manager import uuid4
from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK

from configs import MobileBackendApplicationConfig, RedisConfig
from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    SHARE_FOLDER_IMAGE_VERIFY,
    ConstantKeyConfigSendThirdParty,
    ConstantMessageSendRequestToThirdParty,
    ConstantNameFlow,
    KeyConfigNotifySDK,
    StateNFC,
    StatusCode,
    StatusCodePushSocket,
    ThirdPartyType,
)
from src.common.init_lib import lru_redis_cache
from src.common.utils import replace_newlines
from src.controllers.decryption_controller import ConstantBodyCheckFace
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_card_model import LogRequestCardModel

MobioMediaSDK().config(admin_host=MobileBackendApplicationConfig.ADMIN_HOST, cache_prefix=RedisConfig.CACHE_PREFIX)


class SendRequestCheckFace(object):

    @staticmethod
    def _send(log_id, merchant_id, request_body, account_id, action_time, log_id_start, flow_name):
        if flow_name == ConstantNameFlow.CBBH_ADD_PROFILE:
            MobioLogging().info("Flow name {} not handle".format(flow_name))
            return
        log_id_push_socket = log_id_start if log_id_start else log_id
        status_check_face = SendRequestCheckFace.handle_logic_check_face(
            log_id=log_id,
            merchant_id=merchant_id,
            request_body=request_body,
            account_id=account_id,
            action_time=action_time,
            log_id_start=log_id_start,
            log_id_push_socket=log_id_push_socket,
            flow_name=flow_name,
        )

        MobioLogging().info("SendRequestCheckFace :: status_check_face :: {}".format(status_check_face))
        if not status_check_face:
            return

    @staticmethod
    def handle_logic_check_face(
        log_id, merchant_id, request_body, account_id, action_time, log_id_start, log_id_push_socket, flow_name
    ):

        func_visit = "SendRequestCheckFace"
        """
            - Phằn logic xử lý việc retry khi call api bị lỗi
            - Lấy số lượt request check face của sesion trong redis
            - Kiểm tra xem số lượt request check face đã đạt giới hạn chưa
            - Trong trường hợp quá thì sẽ báo fail
        """

        data_send_socket = {"data": {"flow_name": flow_name}}
        # Kiểm tra xem có cần verify image không
        path_file_image_verify = os.path.join(SHARE_FOLDER_IMAGE_VERIFY, f"image_verify_{log_id}.json")
        with open(path_file_image_verify, "r") as f:
            json_data = json.load(f)

            image_verify = json_data.get("image_verify")
            request_body["raw_img_1"] = json_data.get("raw_img_1")
            request_body["raw_img_2"] = json_data.get("raw_img_2")

        status_verify_image, reasons = SendRequestCheckFace._send_request_verify_image(
            merchant_id=merchant_id,
            image_verify=image_verify,
            log_id_start=log_id_start,
            log_id=str(uuid4()),
            account_id=account_id,
        )
        os.remove(path_file_image_verify)
        MobioLogging().info("Sending check face verification :: status_verify_image :: {}".format(status_verify_image))

        key_cache = "#".join([RedisConfig.CACHE_PREFIX, func_visit, log_id_push_socket, "check_face"])

        data, config, rawImg2, sdk_request_id = SendRequestCheckFace._build_config_send_face_check(
            merchant_id, request_body
        )

        try:
            value_number_retry_processed = lru_redis_cache.cache[key_cache]
        except KeyError:
            value_number_retry_processed = 0

        value_number_retry_processed = int(value_number_retry_processed)

        config_number_retry = config.get("number_retry", 10) or 10

        MobioLogging().info("Session id: %s, number retry :: %s" % (log_id_push_socket, value_number_retry_processed))

        if value_number_retry_processed >= config_number_retry:
            data_send_socket = {
                "data": {
                    "status": StatusCodePushSocket.FAIL,
                    "reason": "Số lần gửi request check face đã đạt giới hạn.",
                    "remaining_attempts_verify": 0,
                    "log_id": log_id_push_socket,
                    "func_visit": func_visit,
                    "state": StateNFC.CHECK_FACE,
                }
            }
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION,
                data_send=data_send_socket,
            )
            return
        value_number_retry_processed += 1
        remaining_attempts_verify = config_number_retry - value_number_retry_processed

        if not status_verify_image:
            lru_redis_cache.cache.set_item(key_cache, value_number_retry_processed, expiration=-1)
            data_send_socket["data"].update(
                {
                    "status": StatusCodePushSocket.FAIL,
                    "reason": reasons,
                    "state": StateNFC.VERIFY_IMAGE,
                    "remaining_attempts_verify": remaining_attempts_verify,
                    "log_id": log_id_push_socket,
                }
            )
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION,
                data_send=data_send_socket,
            )
            return False

        # Get infomation request read card
        log_request_read_card = LogRequestCardModel().find_by_log_request_id(
            merchant_id=merchant_id, log_request_id=log_id_push_socket
        )
        if not log_request_read_card:
            log_request_read_card = {}
        MobioLogging().info(
            "_build_body_data_send_next_step :: log_id_start :: {}, data :: {}".format(
                log_id_push_socket, log_request_read_card
            )
        )

        id_card_no = None
        cardInformation = log_request_read_card.get("cardInformation")
        if cardInformation:
            id_card_no = cardInformation.get("idCard")

        data_response, status_code, reasons = ThirdPartyEIB.check_face(
            log_id=log_id,
            config=config,
            data=data,
            log_id_start=log_id_start,
            merchant_id=merchant_id,
            account_id=account_id,
            sdk_request_id=sdk_request_id,
            id_card_no=id_card_no,
        )
        MobioLogging().info(
            "Sending check face :: log_id: {}, status_code: {}, reasons: {}".format(log_id, status_code, reasons)
        )

        lru_redis_cache.cache.set_item(key_cache, value_number_retry_processed, expiration=-1)

        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            data_send_socket = {
                "data": {
                    "status": StatusCodePushSocket.FAIL,
                    "reason": reasons,
                    "remaining_attempts_verify": remaining_attempts_verify,
                    "log_id": log_id_push_socket,
                    "func_visit": func_visit,
                    "state": StateNFC.CHECK_FACE,
                }
            }
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION,
                data_send=data_send_socket,
            )
            return False
        lru_redis_cache.cache.delete_item(key_cache)
        data_result = data_response.get("dataResponse")
        MobioLogging().info("Sending check face :: data_result: {}".format(data_result))
        if not data_result:
            data_send_socket = {
                "data": {
                    "status": StatusCodePushSocket.FAIL,
                    "reason": "Không tìm thấy dữ liệu trả về.",
                    "remaining_attempts_verify": remaining_attempts_verify,
                    "log_id": log_id_push_socket,
                    "func_visit": func_visit,
                    "state": StateNFC.CHECK_FACE,
                }
            }
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION,
                data_send=data_send_socket,
            )
            return False
        try:
            data_result = json.loads(data_result)
        except Exception as ex:
            MobioLogging().error("Json load data_result :: {} fail".format(data_result))
            data_send_socket = {
                "data": {
                    "status": StatusCodePushSocket.FAIL,
                    "reason": str(ex),
                    "remaining_attempts_verify": remaining_attempts_verify,
                    "log_id": log_id_push_socket,
                    "func_visit": func_visit,
                    "state": StateNFC.CHECK_FACE,
                }
            }
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION,
                data_send=data_send_socket,
            )
            return False
        matching = data_result.get("matching")

        data_send_socket = {
            "data": {
                "log_id": log_id_push_socket,
                "data_face": data_result,
                "func_visit": func_visit,
                "state": StateNFC.CHECK_FACE,
                "remaining_attempts_verify": remaining_attempts_verify,
                "flow_name": flow_name,
            }
        }

        if matching < config.get(ConstantKeyConfigSendThirdParty.VALUE_MATCHING_FACE):
            data_send_socket["data"]["status"] = StatusCodePushSocket.FAIL
            data_send_socket["data"]["reason"] = "Tỷ lệ khuôn mặt không phù hợp."
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION,
                data_send=data_send_socket,
            )
            return False

        if flow_name == ConstantNameFlow.PRE_APPROVED:
            data_send_socket["data"]["status"] = StatusCodePushSocket.SUCCESS
            data_send_socket["data"]["reason"] = ""
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION,
                data_send=data_send_socket,
            )
            return True

        index_slice = config.get("index_slice_face_image", -1)

        MobioLogging().info("Sending check face :: Start build body next step")
        body_save_customer, body_insert_id_check, existsEdigi = SendRequestCheckFace._build_body_data_send_next_step(
            merchant_id=merchant_id,
            log_id_push_socket=log_id_push_socket,
            data_face=data_result,
            account_id=account_id,
            rawImg2=rawImg2,
            remaining_attempts_verify=remaining_attempts_verify,
            log_request_read_card=log_request_read_card,
            index_slice=index_slice,
            image_verify=image_verify,
        )
        MobioLogging().info("Sending check face :: Done build body next step")
        if body_save_customer:

            data_send_save_customer = {
                ConstantMessageSendRequestToThirdParty.LOG_ID: str(uuid.uuid1()),
                ConstantMessageSendRequestToThirdParty.LOG_ID_START: log_id_start,
                ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body_save_customer,
                ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_SAVE_CUSTOMER,
            }
            Producer().push_message_to_request_third_party(
                merchant_id, account_id=account_id, action_time=action_time, data_send=data_send_save_customer
            )
            MobioLogging().info("Sending check face :: Done send body save customer")

        if body_insert_id_check:
            data_send_insert_id_check = {
                ConstantMessageSendRequestToThirdParty.LOG_ID: str(uuid.uuid1()),
                ConstantMessageSendRequestToThirdParty.LOG_ID_START: log_id_start,
                ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body_insert_id_check,
                ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_INSERT_ID_CHECK,
            }
            Producer().push_message_to_request_third_party(
                merchant_id, account_id=account_id, action_time=action_time, data_send=data_send_insert_id_check
            )
            MobioLogging().info("Sending check face :: Done send insert id check")

        # Nếu không có dữ liệu của body_save_customer thì khách hàng không được save vào database thì push socket luôn.

        if not body_save_customer:
            MobioLogging().info("Sending check face :: body_save_customer: {}".format(body_save_customer))
            data_send_socket["data"]["status"] = StatusCodePushSocket.SUCCESS
            data_send_socket["data"]["isSaveCustomer"] = True
            data_send_socket["data"]["existEdigi"] = existsEdigi
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION,
                data_send=data_send_socket,
            )
        return True

    @staticmethod
    def _build_config_send_face_check(merchant_id, request_body):
        config_send_face_check = ConfigInfoApiModel().get_config_info_api_sent_face_check(merchant_id)
        if not config_send_face_check:
            raise CustomError("Not config send face check")

        data_decryption = request_body.get(ConstantBodyCheckFace.DATA_DECRYPTION)
        sdk_request_round = request_body.get(ConstantBodyCheckFace.SDK_REQUEST_ROUND)
        sdk_request_session = request_body.get(ConstantBodyCheckFace.SDK_REQUEST_SESSION)
        sdk_request_id = request_body.get(ConstantBodyCheckFace.SDK_REQUEST_ID)
        raw_1 = request_body.get(ConstantBodyCheckFace.RAW_IMG_1)
        raw_2 = request_body.get(ConstantBodyCheckFace.RAW_IMG_2)
        request_timestamp = request_body.get(ConstantBodyCheckFace.REQUEST_TIMESTAMP)

        index_split_raw_img1 = config_send_face_check.get("index_split_raw_img1", 100)
        index_split_raw_img2 = config_send_face_check.get("index_split_raw_img2", 100)

        rawImg2 = raw_2[index_split_raw_img2:]

        data_request = {
            "encryptedData": data_decryption,
            "requestTimeStamp": int(request_timestamp),
            "requestSession": sdk_request_session,
            "requestRound": sdk_request_round,
            "rawImg1": raw_1[index_split_raw_img1:],
            "rawImg2": rawImg2,
        }
        str_data_request = json.dumps(data_request, separators=(",", ":"), ensure_ascii=False).replace("\\\\", "\\")

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.CONTENT: str_data_request,
                },
            }
        }

        return data, config_send_face_check, rawImg2, sdk_request_id

    @staticmethod
    def _convert_face_image(rawImg2, img2, index_slice=-1):
        raw_img2 = replace_newlines(rawImg2)
        img2 = replace_newlines(img2)

        img2 = img2[:index_slice]
        img2 += raw_img2
        return img2

    @staticmethod
    def _build_body_data_send_next_step(
        merchant_id,
        log_id_push_socket,
        data_face,
        account_id,
        rawImg2,
        remaining_attempts_verify,
        log_request_read_card,
        index_slice=-1,
        image_verify=None,
    ):
        # MobioLogging().info("Sending check face :: _build_body_data_send_next_step :: data_face: {}".format(data_face))
        # face_image = SendRequestCheckFace._convert_face_image(rawImg2, data_face.get("img2"), index_slice)
        face_image = replace_newlines(image_verify)

        path_face_image = os.path.join(SHARE_FOLDER_IMAGE_VERIFY, "face_image_{}.json".format(log_id_push_socket))
        with open(path_face_image, "w") as f:
            json.dump({"face_image": face_image}, f)

        path_face_image_save_customer = os.path.join(
            SHARE_FOLDER_IMAGE_VERIFY, "face_image_{}.json".format(str(uuid.uuid4()))
        )
        with open(path_face_image_save_customer, "w") as f:
            json.dump({"face_image": face_image}, f)

        path_face_image_save_customer_quick_sales = os.path.join(
            SHARE_FOLDER_IMAGE_VERIFY, "face_image_quick_sales_{}.json".format(str(uuid.uuid4()))
        )
        with open(path_face_image_save_customer_quick_sales, "w") as f:
            json.dump({"face_image": face_image}, f)

        card_information = log_request_read_card.get("cardInformation", {})

        flow_name = log_request_read_card.get("flow_name", "")
        MobioLogging().info("Sending check face :: _build_body_data_send_next_step :: flow_name: {}".format(flow_name))

        # Lấy dữ liệu trong data của read card và check customer exits
        # Tài liệu: https://mobiojsc.sg.larksuite.com/docx/UloYdSpGooN1oFxrdlvl6Sm0ggd

        dateOfIssuance = card_information.get("dateOfIssuance", "")
        typeCard = "CCCD21"

        timeConditionGetTypeCard = datetime.strptime("01/07/2024", "%d/%m/%Y")

        # Chuyển đổi chuỗi ngày tháng thành đối tượng datetime
        try:
            dateTimeOfIssuance = datetime.strptime(dateOfIssuance, "%d/%m/%Y")
        except Exception as ex:
            MobioLogging().error(
                "_build_body_data_send_next_step :: dateOfIssuance :: {}, error :: {}".format(dateOfIssuance, str(ex))
            )
            dateTimeOfIssuance = None

        if dateTimeOfIssuance and dateTimeOfIssuance >= timeConditionGetTypeCard:
            typeCard = "CCCD24"

        data_response_of_check_customer_exist = log_request_read_card.get("dataResponseCheckCustomerExist", {})
        userExists = data_response_of_check_customer_exist.get("userExists")
        data_in_data_response = data_response_of_check_customer_exist.get("data", {})

        cif = data_in_data_response.get("cif")
        issuePlace = data_in_data_response.get("issuePlace")
        statusC06 = data_in_data_response.get("statusC06")
        resultC06 = data_in_data_response.get("resultC06")
        body_save_customer = {
            "cif": cif,
            "faceImage": path_face_image_save_customer,
            # "path_face_image": path_face_image,
            "issuePlace": issuePlace if issuePlace else card_information.get("placeOfIssuance", ""),
            "statusC06": statusC06,
            "resultC06": resultC06,
            "typeCard": typeCard,
        }

        solId = InternalAdminHelper().get_sol_id_by_account_id(merchant_id, account_id)

        body_insert_id_check = {"userId": account_id, "solId": solId, "faceImage": path_face_image}
        for k, v in card_information.items():  # Sửa từ card_information thành card_information.items()
            body_save_customer[k if k != "cardImage" else "nfcImage"] = v
            if k == "personalSpecificIdentification":
                k = "personalSpecid"
            body_insert_id_check[k] = v
        body_insert_id_check["placeOfResidence"] = body_insert_id_check.get("address")

        try:
            # Đoạn này cần update lại body_save_customer nếu flow_name là quick_sales
            MobioLogging().info(
                "Sending check face :: _build_body_data_send_next_step :: flow_name: {}".format(flow_name)
            )
            if flow_name == "quick_sales":
                log_request_read_card_id = log_request_read_card.get("_id")
                MobioLogging().info(
                    "Sending check face :: _build_body_data_send_next_step :: log_request_read_card_id: {}".format(
                        log_request_read_card_id
                    )
                )
                body_save_customer["faceImage"] = path_face_image_save_customer_quick_sales

                MobioLogging().info(
                    "Sending check face :: _build_body_data_send_next_step :: [{}] :: body_save_customer: {}".format(
                        flow_name, body_save_customer
                    )
                )

                LogRequestCardModel().update_one_query(
                    {"_id": log_request_read_card_id},
                    {"body_save_customer": body_save_customer},
                )
        except Exception as ex:
            MobioLogging().error("Sending check face :: _build_body_data_send_next_step :: error :: {}".format(str(ex)))
            log_request_read_card_id = None

        if data_face:
            try:
                imgdata = base64.b64decode(s=face_image)  # I assume you have a way of picking unique filenames
                filename = "{}.jpeg".format(str(uuid.uuid4()))
                path_face_liveness = os.path.join(SHARE_FOLDER_IMAGE_VERIFY, filename)
                with open(path_face_liveness, "wb") as f:
                    f.write(imgdata)
                info_upload = MobioMediaSDK().upload_without_kafka(
                    merchant_id=merchant_id,
                    file_path=path_face_liveness,
                    filename=filename,
                    do_not_delete=True,
                )
                MobioLogging().debug("upload_file :: faceLiveness :: info_upload %s " % info_upload)
                url_file = info_upload.get("url")
            except Exception as ex:
                MobioLogging().error("Sending check face :: faceLiveness::gen error :: {}".format(str(ex)))
                url_file = None

            data_face["faceLiveness"] = url_file  # Thêm faceImage vào data_face
        MobioLogging().info("Sending check face :: data_face: {}".format(data_face))

        existEdigi = True if userExists and userExists.lower() == "true" else False
        body_send_next_step_save_customer = {
            "body_save_customer": body_save_customer,
            "body_send_socket_after_save_customer": {
                "log_id": log_id_push_socket,
                "data_face": data_face,
                "isSaveCustomer": True,
                "remaining_attempts_verify": remaining_attempts_verify,
                "cif": cif,
                "existEdigi": existEdigi,
            },
        }

        body_send_next_step_insert_id_check = {
            "body_insert_id_check": body_insert_id_check,
        }

        if userExists.lower() == "false" or (userExists.lower() == "true" and not cif):
            body_send_next_step_save_customer = {}
            if os.path.exists(path_face_image_save_customer):
                os.remove(path_face_image_save_customer)

        return body_send_next_step_save_customer, body_send_next_step_insert_id_check, existEdigi

    @staticmethod
    def _build_body_verify_image(merchant_id, image):
        config_send_face_check = ConfigInfoApiModel().get_config_info_api_sent_face_check(merchant_id)
        if not config_send_face_check:
            raise CustomError("Not config send verify image")

        data_request = {"image": image}
        str_data_request = json.dumps(data_request, separators=(",", ":"), ensure_ascii=False).replace("\\\\", "\\")

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.CONTENT: str_data_request,
                },
            }
        }

        return data, config_send_face_check

    @staticmethod
    def _send_request_verify_image(log_id, merchant_id, log_id_start, image_verify, account_id):
        config_send_verify_image = ConfigInfoApiModel().get_config_info_api_send_request_verify_image(merchant_id)
        if not config_send_verify_image:
            return False, "Không có cấu hình verify image"

        str_data_request = image_verify.replace("\\\\", "\\")

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.CONTENT: {"image": str_data_request},
                },
            }
        }

        data_response, status_code, reasons = ThirdPartyEIB.verify_image(
            log_id=log_id,
            config=config_send_verify_image,
            data=data,
            log_id_start=log_id_start,
            merchant_id=merchant_id,
            account_id=account_id,
        )
        MobioLogging().info(
            "Sending check face :: _send_request_verify_image :: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            return False, reasons
        MobioLogging().info(
            "Sending check face :: _send_request_verify_image :: data_response: {}".format(data_response)
        )
        lstErrorDetail = data_response.get("data", {}).get("lstErrorDetail")

        if not lstErrorDetail or (lstErrorDetail and not any(lstErrorDetail)):
            return True, ""

        # Filter out None values before joining
        filtered_errors = [str(error) for error in lstErrorDetail if error is not None]
        return False, "".join(filtered_errors) if filtered_errors else ""


if __name__ == "__main__":
    x = {
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "account_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "action_time": "2024-06-21 02:08:19",
        "log_id": "********-2f73-11ef-b83c-85ba68999d4d",
        "log_id_start": "f1b99a95-65d3-11ef-b411-7f7cfd744c19",
        "flow_name": "teller_app",
        "request_body": {
            "data_decryption": "FYQk+yoMzyY8DYRv8cNE51fYM5jwM7Uh8qb/PeShlZF5DifKcccvC8EfcDJ28o9rRoO93JQ13Rca2RdkfjMVjDU48SE0taKYqwp35bpongzrNEDvv47leDPHt4pCfEVDSoYSIKtxM7iS3ptd6Tjb1++DnvPm+FB+xj5a4pixP6Pf2DwU31+mTc1ljeCyXleSHJUkWamUQo811+x7KeeakX7Dxq33qg9AqtfUEUqfQNUQ1KlXboCiK6nKGz6CDFuHJZCM0HN2ssR5RbZUUoI0H8/TmbniIWV45+KEAL/nDXKJSbrQJcZ26YwhT4crSOhbIhOCv3SgjBSzvuREERGf/taNCvGrMSjAv8uas4Eqj5P2+feOEQbM/49gRCgYulpLDEQXP/+DlAbifMUiRNB1Of8h6yy6H4oHYrroph6KtKNXX25ibc7XaHZXv+t4X4vsS3CQw5m7akBwSODk4g==",
            "sdk_request_round": "aYM=",
            "sdk_request_session": "21A6BD2E-5D7B-4D49-B47E-50FEE766C153",
            "request_timestamp": "*************",
            "raw_img_1": "AUgEoAAMAAAABAAIAAIdpAAQAAAAB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\\r\\ntyDk2024-06-21T02:08:19.723382993Z AEVGVK9RXUjmYwU4CnBeKMVQhuKXpRiimMOlLzRRjmkIOpp+BSYozQB//9X3\\r\\n3AxQPbmrC27HrxVtIFQdKzckKxTWBmwWPFWREFGQOanxikIwM1ndjsQ9KiPzH2qR\\r\\nuSAKfjC4qRlfaAaawwDT/wCKhwdtADFHzdKRxzVgDGMUjCoAgjbBwayNasmkxeR/\\r\\nwjDfT/JrXI2nNWIyGUg9DUtX0LhPldzz78KOnvWzqFgbdzJH/q2P5Vjla45xszvj\\r\\nNSV0A54pjCnAfNTiOKAIfwpfqKeFqTb60rFIjxkcUoWpAoHalAB7fjU2AFHHP4VM\\r\\nopqipMcU0iQPFRluacxqu8mOM0gRI8mFxTbGxfUbkR/8swcsfb0qGOOa4kWKIEsx\\r\\nAFehabZLY24TA3Y5PvW9Kk5O7Iq1FFWRciiWCJYoxhVAAFIakY1GOa7TgFAyaDGG\\r\\n6injg0vQ1omIrNb/AN01XeNl7Vp4oxVisZAFBFabQoe1V2tz/Cc0XCxUpOlPaKRe\\r\\noIpmKYgzRmmn0opgf//W+mMYopSKTFc4CGoXNSN0xUYGTQxjVGadUgXFGATipAiE\\r\\nW7JpWjwDnpVlRxSEZp2EVlGV/SkK1IF2uw7HmlIqRlZ14qNODirDCq+PmqQLJVZU\\r\\nMbgMD1BrktR09rRyy8oeh9DXWK1SFVlUxsMqRgipnBSNKdTlZ52OvIqTGa1r7S2t\\r\\n28yLlf1rPCYB+lczg1odqkpalfbRjHBqxjjkUhjNRYZFt7ZpwXHrUgT2p6pjnFFg\\r\\nuAUZ4oY4HFSAcVE/FO1hblORjmoOXbA5JOBUj5bpXVaNpITFzOPm7ewqqdPmYqkl\\r\\nFFrSNLFqvmScyH9K36XpSE13pW2PPbu7jCKQDmnUUCDFPxzRjIFLitEgEopcEUgq\\r\\ngCiijtUgHHeoGhR+wH0qam8Z60AU3tv7pz9ahMRU4wa0uKTaKdwsf//X+nSOKTbR\\r\\nupu41zgLgd6hOFNONQt1oAcWJpqrlxyQc9qSpUHJPYUhljgcCm0wHmn0xEbfypOo\\r\\n4pzUxeOKgBCvFV9vzGreKjK80rDIO/FSKaNtIOKQFjIYYPNY97py4MkQ+o/wrVBp\\r\\n4OaGkyoPlONZMEgjGPWm+WPrXUXNkk43DhvWsOSFon2OMVzTp2OqFVNFQR4pSgz1\\r\\nqTHNBpWNCIjHQVXlHYdaubSxwASSeAK2LDTSrh5B83b2q4U+Yh1FEr6ZpWCJZRk+\\r\\nnaupChRgChVCjAoNdcIKJyTm5PUQ0lBoxmmQFHelC0uKYCg4NPFJjigdatAKRmkw\\r\\nKfTSKoCM4FRlqk2E96d5a+lQBXyadipgg7U3FFgExRS0YqgP/9k=",
            "raw_img_2": "AUgEoAAMAAAABAAIAAIdpAAQAAAAB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\\r\\nauk8Q2wuNLljYZ6EfgaxtFtfs0TKv3XAOPQ966KfwnFi2024-06-21T02:08:19.723382993Z NzVMR7zSfmab5XrJIf8A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\\r\\ncfaLT/vuT/43Sf8ACpfEf/PxZ/8Afcn/AMbr6GNApoEj/9k=",
        },
        "request_type": "send_request_check_face",
    }

    SendRequestCheckFace._send(
        log_id=x.get("log_id"),
        merchant_id=x.get("merchant_id"),
        request_body=x.get("request_body"),
        account_id=x.get("account_id"),
        action_time=x.get("action_time"),
        log_id_start=x.get("log_id_start"),
        flow_name=x.get("flow_name"),
    )
