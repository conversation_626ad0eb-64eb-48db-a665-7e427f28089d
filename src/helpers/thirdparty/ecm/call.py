#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 26/11/2024
"""
import time

from flask import Response, stream_with_context
from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import ConstantKeyConfigSendThirdParty, StatusCode, ThirdPartyType
from src.common.init_lib import lru_redis_cache
from src.common.requests_retry import RequestRetryAdapter
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)


class ThirdPartyECMHelper:

    @staticmethod
    def gen_token_request_ecm(log_id, merchant_id):
        config_api = ConfigInfoApiModel().get_config_info_api_send_request_ecm_cic_authenticate(merchant_id)
        if not config_api:
            raise CustomError("Not config send request ecm authenticate")
        key_cache = "ThirdPartyECMHelper#gen_token_request_ecm#{}".format(merchant_id)

        cache_config = config_api.get("cache_config", {})
        ttl_caching = None
        is_use_caching = False
        token = None
        status_code = StatusCode.SUCCESS
        reasons = ""
        if cache_config:
            ttl_caching = cache_config.get("ttl_caching")
            is_use_caching = cache_config.get("is_use_caching")
        if is_use_caching:
            try:
                token = lru_redis_cache.cache[key_cache]
                return token, StatusCode.SUCCESS, ""
            except KeyError:
                token = None

        if not token:
            MobioLogging().info("Start send request get token ecm")
            status_code, reasons, token = ThirdPartyECMHelper.send_request_authenticate(log_id, merchant_id, config_api)
            MobioLogging().info("info token: %s, status_code: %s, reasons: %s" % (token, status_code, reasons))
            if ttl_caching and status_code == StatusCode.SUCCESS:
                lru_redis_cache.cache.set(key_cache, token, ttl_caching)

        return status_code, reasons, token

    @staticmethod
    def send_request_authenticate(log_id, merchant_id, config):
        request_type = ThirdPartyType.SEND_REQUEST_ECM_CIC_AUTHENTICATE
        payload = {
            "username": config.get("auth_name"),
            "password": config.get("auth_pass"),
        }

        headers = {
            "Content-Type": "application/json",
        }
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        info_request = {
            "headers": headers,
            "payload": payload,
            "config": config,
        }
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type,
                config,
                payload,
                info_request,
                response,
                log_id,
                None,
                time_request,
            )
            return data_response_json, status_code, str(e)

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type,
            config,
            payload,
            info_request,
            response.text,
            log_id,
            None,
            time_request,
        )
        if response.status_code != 200:
            return
        try:
            data_response_json = response.json()
        except Exception as e:
            raise CustomError("Not convert response to json")
        status_code = data_response_json.get("code")
        description = data_response_json.get("description")
        data = data_response_json.get("data", "")
        return StatusCode.SUCCESS, description, data

    @staticmethod
    def build_headers_send_request(log_id, merchant_id):
        headers = {}
        return headers

    @staticmethod
    def send_request_download_file(log_id, merchant_id, username, doc_id):
        config_send_request = ConfigInfoApiModel().get_config_info_api_send_request_ecm_cic_detail_file(merchant_id)
        if not config_send_request:
            raise CustomError("Not config send request")
        request_type = ThirdPartyType.SEND_REQUEST_ECM_CIC_DETAIL_FILE
        data_response_json = None
        headers = ThirdPartyECMHelper.build_headers_send_request(log_id, merchant_id)

        base_url = config_send_request.get(ConstantKeyConfigSendThirdParty.URI)
        repository_id = config_send_request.get("repository_id") or "OBJ"

        content_type_get = config_send_request.get("content_type_get", "ContentStream")

        auth_name = config_send_request.get("auth_name")
        auth_pass = config_send_request.get("auth_pass")

        url = f"{base_url}/{repository_id}/{content_type_get}/{doc_id}"
        config_send_request[ConstantKeyConfigSendThirdParty.URI] = url
        MobioLogging().info("Start send request download file ecm")
        # Xác thực cơ bản (Basic Authentication)
        auth = (auth_name, auth_pass)

        info_request = {
            "headers": headers,
            "config": config_send_request,
        }
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_get_request(
                url,
                headers=headers,
                timeout=config_send_request.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10),
                auth=auth,
                stream=True,
                params=None,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type,
                config_send_request,
                {},
                info_request,
                response,
                log_id,
                None,
                time_request,
            )
            return data_response_json, 500, str(e)

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type,
            config_send_request,
            {},
            info_request,
            None,
            log_id,
            None,
            time_request,
        )
        if response.status_code != 200:
            return (
                response.status_code,
                {"error": "Failed to retrieve file data from API A", "details": response.json()},
                "",
            )

        # Trả về phản hồi dạng stream với Content-Type là PDF
        return (
            200,
            "",
            Response(
                stream_with_context(response.iter_content(chunk_size=4096)),
                content_type=response.headers.get("Content-Type"),
                headers={"Content-Disposition": response.headers.get("Content-Disposition")},
            ),
        )


if __name__ == "__main__":
    print(
        ThirdPartyECMHelper.send_request_download_file(
            str(time.time()),
            "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "ecmadmin",
            "idd_003C8794-0000-C517-B264-CDF1A70BB514",
        )
    )
