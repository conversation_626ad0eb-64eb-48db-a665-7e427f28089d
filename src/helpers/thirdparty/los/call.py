#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 26/11/2024
"""
import time
import uuid

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import ConstantKeyConfigSendThirdParty, StatusCode, ThirdPartyType
from src.common.init_lib import lru_redis_cache
from src.common.requests_retry import RequestRetryAdapter
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)


class ThirdPartyLosHelper:

    @staticmethod
    def build_headers_send_request(request_id, merchant_id):

        token_request, _, _ = ThirdPartyLosHelper.get_token_request_los(request_id, merchant_id)
        headers = {
            "Content-Type": "application/json",
            "AuthToken": "Bearer " + token_request,
            "RequestID": request_id,
        }
        return headers

    @staticmethod
    def send_request_authenticate(log_id, merchant_id, config):
        request_type = ThirdPartyType.SEND_REQUEST_LOS_AUTHENTICATE
        payload = {
            "username": config.get("auth_name"),
            "password": config.get("auth_pass"),
        }
        # MobioLogging().info("ThirdPartyLosHelper :: send_request_authenticate :: payload :: %s" % payload)

        headers = {
            "Content-Type": "application/json",
        }
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        info_request = {
            "headers": headers,
            "payload": payload,
            "config": config,
        }
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type,
                config,
                payload,
                info_request,
                response,
                log_id,
                None,
                time_request,
            )
            return data_response_json, status_code, str(e)

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type,
            config,
            payload,
            info_request,
            response.text,
            log_id,
            None,
            time_request,
        )

        return ThirdPartyLosHelper.handle_response(
            "status_return",
            "description_message",
            "token",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def handle_response(
        key_get_response_code,
        key_get_response_desc,
        key_get_data_response,
        response,
        mapping_code_message_error,
        mapping_code_message_success,
    ):
        reasons = ""
        try:
            data_response = response.json()
            response_code = data_response.get(key_get_response_code)
            response_desc = data_response.get(key_get_response_desc)
            result_data = data_response.get(key_get_data_response)
            if isinstance(response_code, int):
                response_code = str(response_code)

            status_code = StatusCode.THIRD_PARTY_FAILURE
            if not response_code:
                reasons = mapping_code_message_error.get(response_code)
            if mapping_code_message_success.get(response_code):
                status_code = StatusCode.SUCCESS
            else:
                if mapping_code_message_error.get(response_code):
                    reasons = mapping_code_message_error.get(response_code)
                else:
                    reasons = "Mã code lỗi {} chưa được khai báo. Chi tiết của mã lỗi: {}".format(
                        response_code, response_desc
                    )

        except Exception as e:  # noqa
            raise ValueError(str(e))
        return result_data, status_code, reasons

    @staticmethod
    def get_token_request_los(request_id, merchant_id):
        key_cache = "ThirdPartyLosHelper#get_token_request_los#{}".format(merchant_id)
        config_send_request = ConfigInfoApiModel().get_config_info_api_send_request_los_authenticate(merchant_id)
        if not config_send_request:
            raise CustomError("Not config send request los authenticate")

        cache_config = config_send_request.get("cache_config", {})
        ttl_caching = None
        is_use_caching = False
        token = None
        status_code = StatusCode.SUCCESS
        reasons = ""
        if cache_config:
            ttl_caching = cache_config.get("ttl_caching")
            is_use_caching = cache_config.get("is_use_caching")
        if is_use_caching:
            try:
                token = lru_redis_cache.cache[key_cache]
                return token, StatusCode.SUCCESS, ""
            except KeyError:
                token = None

        if not token:
            MobioLogging().info("Start send request get token los")
            token, status_code, reasons = ThirdPartyLosHelper.send_request_authenticate(
                request_id, merchant_id, config_send_request
            )
            MobioLogging().info("info token: %s, status_code: %s, reasons: %s" % (token, status_code, reasons))
            if ttl_caching and status_code == StatusCode.SUCCESS:
                lru_redis_cache.cache.set(key_cache, token, ttl_caching)

        return token, status_code, reasons

    @staticmethod
    def send_request_pull_danh_sach(log_id, merchant_id, condition_request, account_id, action_time):
        config_send_request = ConfigInfoApiModel().get_config_info_api_send_request_los_pull_danh_sach(merchant_id)
        if not config_send_request:
            raise CustomError("Not config send request")
        request_type = ThirdPartyType.SEND_REQUEST_LOS_PULL_DANH_SACH
        request_id = str(uuid.uuid4())
        MobioLogging().info("ThirdPartyLosHelper :: send_request_pull_danh_sach :: request_id :: %s" % request_id)

        headers = ThirdPartyLosHelper.build_headers_send_request(request_id, merchant_id)
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        info_request = {
            "headers": headers,
            "payload": condition_request,
            "config": config_send_request,
        }
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config_send_request.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=condition_request,
                timeout=config_send_request.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type,
                config_send_request,
                condition_request,
                info_request,
                response,
                request_id,
                log_id,
                time_request,
            )
            return data_response_json, status_code, str(e)

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type,
            config_send_request,
            condition_request,
            info_request,
            response.text,
            request_id,
            log_id,
            time_request,
        )

        return ThirdPartyLosHelper.handle_response(
            "status_return",
            "description_message",
            "data",
            response,
            config_send_request.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config_send_request.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def send_request_pull_trang_thai(log_id, merchant_id, condition_request, account_id, action_time):
        config_send_request = ConfigInfoApiModel().get_config_info_api_send_request_los_pull_trang_thai(merchant_id)
        if not config_send_request:
            raise CustomError("Not config send request")
        request_type = ThirdPartyType.SEND_REQUEST_LOS_PULL_TRANG_THAI
        request_id = str(uuid.uuid4())
        MobioLogging().info("ThirdPartyLosHelper :: send_request_pull_trang_thai :: request_id :: %s" % request_id)

        headers = ThirdPartyLosHelper.build_headers_send_request(request_id, merchant_id)
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        info_request = {
            "headers": headers,
            "payload": condition_request,
            "config": config_send_request,
        }
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config_send_request.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=condition_request,
                timeout=config_send_request.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type,
                config_send_request,
                condition_request,
                info_request,
                response,
                request_id,
                log_id,
                time_request,
            )
            return data_response_json, status_code, str(e)

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type,
            config_send_request,
            condition_request,
            info_request,
            response.text,
            request_id,
            log_id,
            time_request,
        )

        return ThirdPartyLosHelper.handle_response(
            "status_return",
            "description_message",
            "data",
            response,
            config_send_request.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config_send_request.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )
