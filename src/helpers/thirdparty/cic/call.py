#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 26/11/2024
"""
import time
from uuid import uuid4

from flask import Response, stream_with_context
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import ConstantKeyConfigSendThirdParty, ThirdPartyType
from src.common.requests_retry import RequestRetryAdapter
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)


class ThirdPartyCICHelper:

    @staticmethod
    def build_headers_send_request(request_id, merchant_id, api_key):
        headers = {
            "Content-Type": "application/json",
            "Api-Key": api_key,
            "RequestID": request_id,
        }
        return headers

    @staticmethod
    def send_request_check_customer(log_id, merchant_id, payload):
        config_send_request = ConfigInfoApiModel().get_config_info_api_send_request_check_customer_cic(merchant_id)
        if not config_send_request:
            raise CustomError("Not config send request")
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_CIC
        data_response_json = None
        headers = ThirdPartyCICHelper.build_headers_send_request(
            log_id, merchant_id, config_send_request.get("api_key")
        )
        info_request = {
            "headers": headers,
            "payload": payload,
            "config": config_send_request,
        }
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config_send_request.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config_send_request.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10),
                raise_for_status=False,
                verify=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type,
                config_send_request,
                payload,
                info_request,
                response,
                log_id,
                None,
                time_request,
            )
            return data_response_json, 500, str(e)

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type,
            config_send_request,
            payload,
            info_request,
            response.text,
            log_id,
            None,
            time_request,
        )
        try:
            data_response_json = response.json()

        except Exception as e:
            raise CustomError("Not convert response to json")
        status_code = data_response_json.get("statusCode")
        description = data_response_json.get("description")
        data = data_response_json.get("data")
        return status_code, description, data

    @staticmethod
    def send_request_inquiry_product(log_id, merchant_id, payload):
        config_send_request = ConfigInfoApiModel().get_config_info_api_send_request_inquiry_product_cic(merchant_id)
        if not config_send_request:
            raise CustomError("Not config send request")
        request_type = ThirdPartyType.SEND_REQUEST_INQUIRY_PRODUCT_CIC
        data_response_json = None
        headers = ThirdPartyCICHelper.build_headers_send_request(
            log_id, merchant_id, config_send_request.get("api_key")
        )
        info_request = {
            "headers": headers,
            "payload": payload,
            "config": config_send_request,
        }
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config_send_request.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config_send_request.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10),
                raise_for_status=False,
                verify=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type,
                config_send_request,
                payload,
                info_request,
                response,
                log_id,
                None,
                time_request,
            )
            return 500, str(e), data_response_json, "", ""

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type,
            config_send_request,
            payload,
            info_request,
            response.text,
            log_id,
            None,
            time_request,
        )
        try:
            data_response_json = response.json()

        except Exception as e:
            raise CustomError("Not convert response to json")
        status_code = data_response_json.get("code")
        description = data_response_json.get("description")
        data = data_response_json.get("data")
        number_inquiry_remain = data_response_json.get("numberInquiryRemain")
        role_permission = data_response_json.get("rolePermission")
        return status_code, description, data, number_inquiry_remain, role_permission

    @staticmethod
    def send_request_search_history(log_id, merchant_id, payload):
        config_send_request = ConfigInfoApiModel().get_config_info_api_send_request_search_history_cic(merchant_id)
        if not config_send_request:
            raise CustomError("Not config send request")
        request_type = ThirdPartyType.SEND_REQUEST_SEARCH_HISTORY_CIC
        data_response_json = None
        headers = ThirdPartyCICHelper.build_headers_send_request(
            log_id, merchant_id, config_send_request.get("api_key")
        )
        info_request = {
            "headers": headers,
            "payload": payload,
            "config": config_send_request,
        }
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config_send_request.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config_send_request.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10),
                raise_for_status=False,
                verify=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type,
                config_send_request,
                payload,
                info_request,
                response,
                log_id,
                None,
                time_request,
            )
            return data_response_json, 500, str(e), 0, ""

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type,
            config_send_request,
            payload,
            info_request,
            response.text,
            log_id,
            None,
            time_request,
        )
        try:
            data_response_json = response.json()

        except Exception as e:
            raise CustomError("Not convert response to json")
        # status_code = data_response_json.get("code")
        # description = data_response_json.get("description")
        status_code = data_response_json.get("statusCode")
        description = data_response_json.get("description")
        data = data_response_json.get("data", [])
        if not data:
            data = []
        number_inquiry_remain = data_response_json.get("numberInquiryRemain")
        role_permission = data_response_json.get("rolePermission")
        return status_code, description, data, number_inquiry_remain, role_permission

    @staticmethod
    def send_request_get_link_file_report(log_id, merchant_id, payload):
        config_send_request = ConfigInfoApiModel().get_config_info_api_send_request_get_link_file_report(merchant_id)
        if not config_send_request:
            raise CustomError("Not config send request")
        request_type = ThirdPartyType.SEND_REQUEST_GET_FILE_REPORT_CIC
        data_response_json = None
        headers = ThirdPartyCICHelper.build_headers_send_request(
            log_id, merchant_id, config_send_request.get("api_key")
        )
        info_request = {
            "headers": headers,
            "payload": payload,
            "config": config_send_request,
        }
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config_send_request.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config_send_request.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10),
                raise_for_status=False,
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type,
                config_send_request,
                payload,
                info_request,
                response,
                log_id,
                None,
                time_request,
            )
            return 500, data_response_json, str(e)

        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type,
            config_send_request,
            payload,
            info_request,
            response.text,
            log_id,
            None,
            time_request,
        )

        if response.status_code != 200:
            return (
                response.status_code,
                {"error": "Failed to retrieve file data from API A", "details": response.json()},
                "",
            )

        # Tạo generator để truyền dữ liệu theo từng chunk
        def generate():
            for chunk in response.iter_content(chunk_size=4096):
                if chunk:
                    yield chunk

        # Trả về phản hồi dạng stream với Content-Type là PDF
        return (
            200,
            "",
            Response(
                stream_with_context(generate()),
                content_type="application/pdf",
                headers={"Content-Disposition": f'attachment; filename="{".pdf".format(str(uuid4()))}"'},
            ),
        )
