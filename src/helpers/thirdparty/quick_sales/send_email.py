#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 20/06/2025
"""

import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from mobio.libs.logging import Mo<PERSON>Logging

from src.common import ThirdPartyType
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


class SendEmailHelper:
    def __init__(self, merchant_id):
        self.merchant_id = merchant_id

    def send_email_smtp(self, log_id, recipients, subject, body, mobio_extend):
        """
        gui email qua smtp
        :param config:
        :param log_id:
        :param attachments:
        :param sender:
        :param sender_name:
        :param mobio_extend:
        :param recipients:
        :param subject:
        :param body:
        :return: {
                'code': 500,
                'message': str
            }
        """
        try:
            # TODO: send email
            config_send_email = ConfigInfoApiModel().get_config_info_api(
                self.merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_SEND_EMAIL
            )

            host = config_send_email.get("host")
            cert_file = config_send_email.get("cert_file")
            key_file = config_send_email.get("key_file")
            not_verify_smtp = config_send_email.get("not_verify_smtp")
            port = config_send_email.get("port")
            ssl_type = config_send_email.get("ssl_type")
            config_set = config_send_email.get("config_set")

            username_smtp = config_send_email.get("auth_name")
            password_smtp = config_send_email.get("auth_pass")

            if config_send_email.get("user_is_sender"):
                sender = username_smtp

            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = config_send_email.get("from")
            msg["To"] = recipients.get("to")

            to = recipients.get("to").split(", ")
            cc = recipients.get("cc")
            bcc = recipients.get("bcc")
            reply_to = recipients.get("reply_to")
            if cc:
                msg["Cc"] = cc
                to += cc.split(", ")
            if bcc:
                msg["BCC"] = bcc
                to += bcc.split(", ")
            if reply_to:
                msg["Reply-To"] = reply_to

            if mobio_extend:
                msg["Mobio"] = mobio_extend

            body_html = MIMEText(body, "html")
            msg.attach(body_html)

            # aws
            # config_set ses
            if config_set:
                msg.add_header("config_set", config_set)
                msg.add_header("message_id", log_id)

            # ssl connect
            if not ssl_type:
                if cert_file and key_file:
                    server = smtplib.SMTP_SSL(host, port, certfile=cert_file, keyfile=key_file)
                else:
                    server = smtplib.SMTP(host, port)
            else:
                if ssl_type == 0:
                    server = smtplib.SMTP(host, port, timeout=15)
                elif ssl_type == 1:
                    server = smtplib.SMTP(host, port, timeout=15)
                else:
                    server = smtplib.SMTP_SSL(host, port, timeout=15)

            if port != 25 or port != "25":
                server.ehlo()
                server.starttls()

            server.ehlo()
            if not not_verify_smtp:
                server.login(username_smtp, password_smtp)

            server.sendmail(sender, to, msg.as_string())
            server.close()

            return {"code": 200, "message": "success"}

        except Exception as e:
            MobioLogging().error("SendEmailHelper :: send_email_smtp :: error %s" % e)
            return {"code": 500, "message": str(e)}


if __name__ == "__main__":
    print(
        SendEmailHelper("57d559c1-39a1-4cee-b024-b953428b5ac8").send_email_smtp(
            "123",
            {"to": "<EMAIL>"},
            "Test",
            "Test",
            {},
        )
    )
