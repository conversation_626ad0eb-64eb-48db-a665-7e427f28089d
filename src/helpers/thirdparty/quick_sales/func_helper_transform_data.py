#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 28/02/2025
"""


from datetime import datetime, timedelta, timezone
import time

from mobio.libs.logging import MobioLogging

from src.common import (
    ConstantKeyConfigMappingFieldCrmEib,
    ConstantQuickSales,
    StatusCode,
)
from src.common.utils import utf8_to_ascii
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.models.mongo.config_mapping_field_crm_to_eib import (
    ConfigMappingFieldCrmToEibModel,
)


class FuncHelperTransformData:

    def __init__(self, merchant_id):
        self.logger = MobioLogging()
        self.merchant_id = merchant_id

    def get_config_mapping_data_from_database(self, type_get):
        filter_option = {
            "merchant_id": self.merchant_id,
            "type": "quick_sales",
        }

        detail_config = ConfigMappingFieldCrmToEibModel().find_one(filter_option)
        self.logger.info("get_config_set_default_from_db :: detail_config: {}".format(detail_config))
        if not detail_config:
            return {}
        return detail_config.get(type_get, {})

    def get_dsa_id_from_staff_info(self, staff_info, number_retry_request=3):
        staff_code = staff_info.get("staff_code")
        account_id = staff_info.get("account_id")
        action_time = datetime.now(timezone.utc)
        response_get_dsa_id, status_code, message, log_request_id = QuickSalesHelper().send_request_get_in4_dsa(
            self.merchant_id, account_id, action_time, {"staff_code": staff_code}
        )

        MobioLogging().info("FuncHelperTransformData :: response_get_dsa_id %s" % response_get_dsa_id)
        MobioLogging().info("FuncHelperTransformData :: status_code %s" % status_code)
        MobioLogging().info("FuncHelperTransformData :: message %s" % message)
        MobioLogging().info("FuncHelperTransformData :: log_request_id %s" % log_request_id)

        # TODO: retry send request create cif
        while True and status_code != StatusCode.SUCCESS and (number_retry_request and number_retry_request > 0):
            MobioLogging().info("FuncHelperTransformData :: retry send request get dsa id :: %s" % number_retry_request)
            response_get_dsa_id, status_code, message, log_request_id = QuickSalesHelper().send_request_get_in4_dsa(
                self.merchant_id, account_id, action_time, {"staff_code": staff_code}
            )
            MobioLogging().info("FuncHelperTransformData :: response_get_dsa_id %s" % response_get_dsa_id)
            number_retry_request -= 1
            time.sleep(1)
            
        if status_code == StatusCode.SUCCESS:
            return response_get_dsa_id.get("dsa_id", "")
        return None

    def transform_data_to_update_profile_in_crm(self, profile_id, form_id, form_data):
        pass

    def transform_data_to_upsert_deal_in_crm(self, profile_id, form_id, form_data):
        pass

    def get_first_middle_last_name_by_fullname(self, fullname):
        parts = fullname.strip().split()

        # Xử lý các trường hợp đặc biệt (tên quá ngắn)
        if not parts:
            return {"first_name": "", "middle_name": "", "last_name": ""}

        last_name = parts[0]  # Họ luôn là phần đầu tiên
        first_name = parts[-1]  # Tên chính là phần cuối cùng

        # Tên đệm là các phần ở giữa (nếu có)
        middle_name = " ".join(parts[1:-1]) if len(parts) > 2 else ""

        return {
            "first_name": utf8_to_ascii(first_name).upper(),
            "middle_name": utf8_to_ascii(middle_name).upper(),
            "last_name": utf8_to_ascii(last_name).upper(),
        }

    def get_day_month_year_by_date(self, date_birth):
        if not date_birth:
            return {"day": "", "month": "", "year": ""}
        try:
            value_convert_date = datetime.strptime(date_birth, "%Y-%m-%dT%H:%M:%S.000Z") + timedelta(hours=7)
        except Exception as ex:
            MobioLogging().error("get_day_month_year_by_date :: date_birth: {} :: ex: {}".format(date_birth, ex))
            return {"day": "", "month": "", "year": ""}
        # date = value_convert_date.strftime("%d-%m-%Y")
        date_of_birth = value_convert_date.strftime("%Y-%m-%dT00:00:00.000")
        day = "0{}".format(value_convert_date.day) if value_convert_date.day < 10 else value_convert_date.day
        month = "0{}".format(value_convert_date.month) if value_convert_date.month < 10 else value_convert_date.month
        year = value_convert_date.year
        return {"day": day, "month": month, "year": year, "date_of_birth": date_of_birth}

    def get_gender_by_value(self, gender_value):
        return "M" if gender_value == 2 else "F"

    def get_salutation_by_gender(self, gender_value):
        return "MR." if gender_value == 2 else "MRS."

    def convert_data_personal_address_from_web_form(self, personal_address, code_decode):
        type_address = personal_address.get("type_address")
        if type_address == "fixed":
            # Sample: Ấp 3B, Hựu Thạnh, Đức Hòa, Long An

            value_cccd = personal_address.get("value_cccd")
            items_value_cccd = value_cccd.split(",")

            city = items_value_cccd[-1].strip()
            ascii_city = utf8_to_ascii(city)
            ascii_city_upper = ascii_city.upper()
            if "TP." in ascii_city_upper:
                city = ascii_city_upper.replace("TP.", "THANH PHO")
            else:
                city = utf8_to_ascii(city).upper()
            if "TINH" not in city and "THANH PHO" not in city:
                city = "TINH {}".format(city)
            city = code_decode.get("city", {}).get(city, "00")
            personal_address["city"] = city
            if not personal_address.get("detail"):
                personal_address["detail"] = value_cccd

        return personal_address

    def convert_data_phone_number_from_profile_data_to_cif(self, phone_number):
        PhoneEmailDtls = []
        phone_num = phone_number
        if phone_number.startswith("+"):
            phone_number = phone_number[1:]
        if phone_number.startswith("0"):
            phone_number = "84" + phone_number[1:]
        if phone_number.startswith("84"):
            phone_num = phone_number[2:]

        phone_num_city_code = phone_num[:3]
        phone_num_local_code = phone_num[3:]

        for phone_email_type in ["CELLPH", "HOMEPH1"]:
            PhoneEmailDtls.append(
                {
                    "PhoneEmailType": phone_email_type,
                    "PhoneNum": phone_num,
                    "PhoneNumCityCode": phone_num_city_code,
                    "PhoneNumCountryCode": "84",
                    "PhoneNumLocalCode": phone_num_local_code,
                    "PhoneOrEmail": "PHONE",
                    "PrefFlag": "Y" if phone_email_type == "CELLPH" else "N",
                }
            )
        return PhoneEmailDtls

    def convert_data_from_mapping_field_create_cif(self, form_data_submit):

        config_mapping_field = self.get_config_mapping_data_from_database(
            ConstantKeyConfigMappingFieldCrmEib.API_SEND_REQUEST_CREATE_CIF
        )

        if not config_mapping_field:
            return {}

        """
        - Format:
            "CIFId": {
                "field_source": "customer_id",
                "object_source": "profile",
                "type_target_convert": "string",
                "action_convert": "",
                "use_default": True,
                "value_default": "CIF",
            }
        """
        return self.transform_data_process(form_data_submit, config_mapping_field)

    def convert_data_from_mapping_field_create_account_edigi(self, form_data_submit):
        config_mapping_field = self.get_config_mapping_data_from_database(
            ConstantKeyConfigMappingFieldCrmEib.API_SEND_REQUEST_CREATE_ACCOUNT_EDIGI
        )

        if not config_mapping_field:
            return {}

        """
        - Format:
            "CIFId": {
                "field_source": "customer_id",
                "object_source": "profile",
                "type_target_convert": "string",
                "action_convert": "",
                "use_default": True,
                "value_default": "CIF",
            }
        """
        return self.transform_data_process(form_data_submit, config_mapping_field)

    def convert_data_from_mapping_field_create_account_indiv(self, form_data_submit):
        config_mapping_field = self.get_config_mapping_data_from_database(
            ConstantKeyConfigMappingFieldCrmEib.API_SEND_REQUEST_CREATE_CA_ACCT_ADD_INDIV
        )

        if not config_mapping_field:
            return {}

        """
        - Format:
            "CIFId": {
                "field_source": "customer_id",
                "object_source": "profile",
                "type_target_convert": "string",
                "action_convert": "",
                "use_default": True,
                "value_default": "CIF",
            }
        """
        return self.transform_data_process(form_data_submit, config_mapping_field)

    def convert_data_from_mapping_field_save_customer_to_edigi(self, form_data_submit):
        config_mapping_field = self.get_config_mapping_data_from_database(
            ConstantKeyConfigMappingFieldCrmEib.API_SEND_REQUEST_SAVE_CUSTOMER_TO_EDIGI
        )

        if not config_mapping_field:
            return {}

        return self.transform_data_process(form_data_submit, config_mapping_field)

    def transform_data_process(self, data_transform, config_mapping_field):
        data_convert = {}
        result_process_transform = {}
        for key, value in config_mapping_field.items():
            if value.get("use_default"):
                data_convert[key] = value.get("value_default")
                if value.get("set_to_result"):
                    result_process_transform[key] = data_convert.get(key)
                    continue
            field_source = value.get("field_source")
            object_source = value.get("object_source")
            func_transform_string = value.get("func_transform_string")  # Optional
            key_get_data_input_func_process = value.get("key_get_data_input_func_process")  # Optional

            object_source_data = data_transform.get(object_source, {})
            if object_source_data:
                data_convert[key] = object_source_data.get(field_source)
            if func_transform_string:
                execution_namespace = {}
                try:
                    exec(func_transform_string, execution_namespace)

                    # Lấy hàm 'process' từ namespace sau khi exec() chạy
                    if "process" in execution_namespace and callable(execution_namespace["process"]):
                        process_function = execution_namespace["process"]
                        if key_get_data_input_func_process:
                            data_convert[key] = process_function(data_convert.get(key_get_data_input_func_process))
                        else:
                            data_convert[key] = process_function()

                    else:
                        self.logger.error("Không tìm thấy hàm 'process' trong namespace sau khi thực thi.")
                        data_convert[key] = ""
                except Exception as e:
                    self.logger.error(f"Đã xảy ra lỗi trong quá trình thực thi chuỗi KEY: {key} code: {e}")
                    data_convert[key] = ""
            if value.get("set_to_result"):
                result_process_transform[key] = data_convert.get(key)
        return result_process_transform

    def transform_data_from_submit_data_to_create_cif(self, form_data):
        """
        Data Sample:
        {
            "profile": {
                "_dyn_chuc_vu_1638341818684": "dffgt",
                "_dyn_ngay_cap_cccd_1714017181392": "2022-09-11T17:00:00.000Z",
                "_dyn_ngay_het_han_cccd_1714017181394": "2035-09-09T17:00:00.000Z",
                "_dyn_nghe_nghiep_cua_khach_hang_1696581120694": ["Sinh viên"],
                "birthday": "1988-03-21T17:00:00.000Z",
                "gender": 3,
                "name": "Vũ Thị Ngoan",
                "nationality": "Việt Nam",
                "primary_email": "<EMAIL>",
                "primary_phone": "+84986490460",
                "profile_identify": [
                    {
                        "identify_type": "citizen_identity",
                        "identify_value": "034188003235"
                    }
                ]
            },
            "web_form": {
                "133b7018-2bd5-4b20-b945-9705acdd5b6f": ["Thanh toán hàng hóa, dịch vụ", "Nhận lương"],
                "address_contact": [
                    {
                        "city": "Quận Liên Chiểu",
                        "city_code": 490,
                        "country": "Việt Nam",
                        "country_code": "VNM",
                        "county": "Thành phố Đà Nẵng",
                        "county_code": 48,
                        "detail": "Ttyy",
                        "district": "Phường Hòa Minh",
                        "district_code": 20200,
                        "state": None,
                        "state_code": None,
                        "subdistrict": None,
                        "subdistrict_code": None,
                        "type": "DISTRICT",
                        "type_address": "custom",
                        "unique_value": "VNM#48#490#20200#",
                        "value_cccd": "số 10 Kim Mã Ba Đình Hà Nội"
                    }
                ],
                "ekyc_select_product_bank": [
                    {
                        "field_key": "product_line",
                        "key": "65a0bc1abd24020205a491dc",
                        "name": "Eximbank EDigi"
                    },
                    {
                        "field_key": "product",
                        "key": "67ff14c86295342bb248cd56",
                        "name": "E-Plus Online Banking (IB+MB) – eKYC Plus"
                    }
                ]
            }
        }
        """

        profile_data = form_data.get(ConstantQuickSales.ObjectType.PROFILE, {})
        web_form_data = form_data.get(ConstantQuickSales.ObjectType.WEB_FORM, {})
        card_information = form_data.get("card_information", {})
        field_config_web_form = web_form_data.get("field_config_web_form", {})
        address_contact_in_web_form = []
        for key, value in field_config_web_form.items():
            if value.get("field_key") == "address_contact":
                address_contact_in_web_form = web_form_data.get(key, [])
                break

        # Lấy config mapping field create cif
        config_mapping_by_database = ConfigMappingFieldCrmToEibModel().get_config_mapping_field_crm_to_eib_quick_sales(
            self.merchant_id
        )
        if not config_mapping_by_database:
            raise Exception("Config mapping not found")

        field_configs = config_mapping_by_database.get(
            ConstantKeyConfigMappingFieldCrmEib.API_SEND_REQUEST_CREATE_CIF, {}
        )
        if not field_configs:
            raise Exception("API send request create cif not found")

        code_decode = config_mapping_by_database.get("code_decode", {})

        try:
            item_address_personal = address_contact_in_web_form[0] if address_contact_in_web_form else {}
            if item_address_personal:
                item_address_personal = self.convert_data_personal_address_from_web_form(
                    item_address_personal, code_decode
                )
        except Exception as e:
            self.logger.error("Error convert data personal address from web form: {}".format(e))
            item_address_personal = {}

        self.logger.info("item_address_personal: {}".format(item_address_personal))

        AddrDtls = []
        for address_category in ["Mailing", "Home", "Work"]:
            AddrDtls.append(
                {
                    "AddrLine1": utf8_to_ascii(card_information.get("address")),
                    "AddrLine2": ".",
                    "AddrLine3": ".",
                    "AddrCategory": address_category,
                    "City": item_address_personal.get("city", "00"),
                    "Country": "VN",
                    "HoldMailFlag": "N",
                    "HouseNum": ".",
                    "PrefAddr": "Y" if address_category == "Mailing" else "N",
                    "PrefFormat": "FREE_TEXT_FORMAT",
                    "StartDt": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.000"),
                    "State": "MIGR",
                    "StreetName": ".",
                    "StreetNum": ".",
                    "PostalCode": "XXXXX",
                    "FreeTextLabel": utf8_to_ascii(card_information.get("address")),
                }
            )

        birthday = profile_data.get("birthday", "")
        birthday_data = self.get_day_month_year_by_date(birthday)

        profile_name = profile_data.get("name", "")
        profile_name_data = self.get_first_middle_last_name_by_fullname(profile_name)

        try:
            PhoneEmailDtls = self.convert_data_phone_number_from_profile_data_to_cif(profile_data.get("primary_phone"))
        except Exception as e:
            self.logger.error("Error convert data phone number from profile data to cif: {}".format(e))
            PhoneEmailDtls = []

        self.logger.info("PhoneEmailDtls: {}".format(PhoneEmailDtls))

        data_transform_by_config = self.convert_data_from_mapping_field_create_cif(form_data)

        data_result = {
            "AddrDtls": AddrDtls,
            "BirthDt": str(birthday_data.get("day")),
            "BirthMonth": str(birthday_data.get("month")),
            "BirthYear": str(birthday_data.get("year")),
            "DateOfBirth": birthday_data.get("date_of_birth"),
            "FirstName": profile_name_data.get("first_name"),
            "MiddleName": profile_name_data.get("middle_name"),
            "LastName": profile_name_data.get("last_name"),
            "Name": utf8_to_ascii(profile_name).upper(),
            "Gender": self.get_gender_by_value(profile_data.get("gender")),
            "PrefName": utf8_to_ascii(profile_name).upper(),
            "Salutation": self.get_salutation_by_gender(profile_data.get("gender")),
            "ShortName": profile_name_data.get("last_name"),
            "PhoneEmailDtls": PhoneEmailDtls,
        }
        data_result.update(data_transform_by_config)

        return data_result

    def transform_data_from_create_cif_to_create_account_indiv(self, form_data):
        profile_data = form_data.get(ConstantQuickSales.ObjectType.PROFILE, {})
        account_number, free_account_number = FuncHelperTransformData.get_account_number_from_form_data_submit(
            form_data.get(ConstantQuickSales.ObjectType.WEB_FORM, {})
        )

        SchmCode = self.get_schm_code_from_form_data(form_data.get(ConstantQuickSales.ObjectType.WEB_FORM, {}))

        data_result = {
            "AcctName": profile_data.get("name", ""),
            "AcctShortName": profile_data.get("name", ""),
            "LuckyAccNum": account_number,
            "LienFeeAmount": free_account_number,
            "SchmCode": SchmCode,
        }

        data_transform_by_config = self.convert_data_from_mapping_field_create_account_indiv(form_data)

        data_result.update(data_transform_by_config)
        self.logger.info("transform_data_from_create_cif_to_create_account: data_result :: {}".format(data_result))

        return data_result

    def transform_data_save_customer_to_edigi(self, form_data):
        data_result = {}
        data_transform_by_config = self.convert_data_from_mapping_field_save_customer_to_edigi(form_data)
        data_result.update(data_transform_by_config)
        return data_result

    @staticmethod
    def get_account_number_from_form_data_submit(web_form):
        field_config_web_form = web_form.get("field_config_web_form", {})
        account_number = ""
        free_account_number = ""
        for key, value in field_config_web_form.items():
            field_key_in_config = value.get("field_key")
            if field_key_in_config == "ekyc_select_product_bank":
                data_in_web_form = web_form.get(key, [])
                for item in data_in_web_form:
                    field_key = item.get("field_key")
                    if field_key == "account_number":
                        account_number = item.get("premium_account_number")
                        free_account_number = item.get("fee")
                    elif field_key == "free_account_number":
                        free_account_number = ""
                if account_number or free_account_number:
                    return account_number, free_account_number
        return account_number, free_account_number

    def get_schm_code_from_form_data(self, web_form):
        field_config_web_form = web_form.get("field_config_web_form", {})
        schm_code = ""
        for key, value in field_config_web_form.items():
            field_key_in_config = value.get("field_key")
            if field_key_in_config == "ekyc_select_product_bank":
                data_in_web_form = web_form.get(key, [])
                for item in data_in_web_form:
                    field_key = item.get("field_key")
                    if field_key == "product" and item.get("name") in ["19100", "19009"]:
                        return item.get("name")
        return schm_code

    @staticmethod
    def get_identify_value_from_form_data(form_data):
        profile_data = form_data.get(ConstantQuickSales.ObjectType.PROFILE, {})
        profile_identify = profile_data.get("profile_identify", [])
        for item in profile_identify:
            if item.get("identify_type") == "citizen_identity":
                return item.get("identify_value")
        return ""

    def transform_data_from_create_account_to_res_account_edigi(self, form_data):
        profile_data = form_data.get(ConstantQuickSales.ObjectType.PROFILE, {})
        identify_value = FuncHelperTransformData.get_identify_value_from_form_data(form_data)

        account_number, free_account_number = FuncHelperTransformData.get_account_number_from_form_data_submit(
            form_data.get(ConstantQuickSales.ObjectType.WEB_FORM, {})
        )

        address_detail = (
            profile_data.get("address_personal", [])[0].get("detail", "")
            if profile_data.get("address_personal", [])
            else ""
        )

        data_result = {
            "idNumber": identify_value,
            "address": address_detail,
            "accountNo": account_number,
        }

        data_transform_by_config = self.convert_data_from_mapping_field_create_account_edigi(form_data)
        data_result.update(data_transform_by_config)
        return data_result

    def transform_data_send_email_request_edigi(
        self,
        form_data,
        data_request_res_edigi,
        data_response_res_edigi,
    ):
        data_result = self.convert_data_from_mapping_field_send_email_request_edigi(
            form_data, data_request_res_edigi, data_response_res_edigi
        )
        return data_result

    def convert_data_from_mapping_field_send_email_request_edigi(
        self, form_data, data_request_res_edigi, data_response_res_edigi
    ):

        data_transform = {}
        if form_data:
            data_transform.update(form_data)
        if data_request_res_edigi:
            data_transform.update({"data_request_res_edigi": data_request_res_edigi})
        if data_response_res_edigi:
            data_transform.update({"data_response_res_edigi": data_response_res_edigi})

        config_mapping_field = self.get_config_mapping_data_from_database(
            ConstantKeyConfigMappingFieldCrmEib.SEND_EMAIL_RES_ACCOUNT_EDIGI
        )

        if not config_mapping_field:
            return {}

        """
        - Format:
            "CIFId": {
                "field_source": "customer_id",
                "object_source": "profile",
                "type_target_convert": "string",
                "action_convert": "",
                "use_default": True,
                "value_default": "CIF",
            }
        """
        return self.transform_data_process(data_transform, config_mapping_field)

    def transform_data_send_dynamic_event_to_profile(
        self, form_data, data_request_res_edigi, data_response_res_edigi, data_send_email
    ):
        data_result = self.convert_data_from_mapping_field_send_dynamic_event_to_profile(
            form_data, data_request_res_edigi, data_response_res_edigi, data_send_email
        )
        return data_result

    def transform_data_insert_in4_dsa(self, form_data):
        data_result = self.convert_data_from_mapping_field_insert_in4_dsa(form_data)
        return data_result

    def convert_data_from_mapping_field_insert_in4_dsa(self, form_data):
        data_transform = {}
        if form_data:
            data_transform.update(form_data)

        config_mapping_field = self.get_config_mapping_data_from_database(
            ConstantKeyConfigMappingFieldCrmEib.INSERT_IN4_DSA
        )

        if not config_mapping_field:
            return {}

        return self.transform_data_process(data_transform, config_mapping_field)

    def convert_data_from_mapping_field_send_dynamic_event_to_profile(
        self, form_data, data_request_res_edigi, data_response_res_edigi, data_send_email
    ):
        data_transform = {}
        if form_data:
            data_transform.update(form_data)
        if data_request_res_edigi:
            data_transform.update({"data_request_res_edigi": data_request_res_edigi})
        if data_response_res_edigi:
            data_transform.update({"data_response_res_edigi": data_response_res_edigi})
        if data_send_email:
            data_transform.update({"data_send_email": data_send_email})

        config_mapping_field = self.get_config_mapping_data_from_database(
            ConstantKeyConfigMappingFieldCrmEib.SEND_DYNAMIC_EVENT_TO_PROFILE
        )

        if not config_mapping_field:
            return {}

        return self.transform_data_process(data_transform, config_mapping_field)


if __name__ == "__main__":
    x = FuncHelperTransformData("57d559c1-39a1-4cee-b024-b953428b5ac8").transform_data_from_submit_data_to_create_cif(
        {
            "profile": {
                "_dyn_chuc_vu_1638341818684": "a fgg",
                "_dyn_ngay_cap_cccd_1714017181392": "2024-12-07T17:00:00.000Z",
                "_dyn_ngay_het_han_cccd_1714017181394": "2035-09-09T17:00:00.000Z",
                "_dyn_nghe_nghiep_cua_khach_hang_1696581120694": "Công chức nhà nước",
                "_dyn_noi_cap_cccd_1747291155358": "Bo Cong An",
                "_dyn_noi_lam_viec_1747734087125": "hqf noi",
                "birthday": "2000-06-23T00:00:00.000Z",
                "gender": 2,
                "marital_status": 1,
                "name": "Nguyễn Anh Quân",
                "nationality": "Việt Nam",
                "primary_email": "<EMAIL>",
                "primary_phone": "+84986491014",
                "profile_identify": [{"identify_type": "citizen_identity", "identify_value": "************"}],
            },
            "web_form": {
                "133b7018-2bd5-4b20-b945-9705acdd5b6f": ["Thanh toán hàng hóa, dịch vụ", "Nhận lương"],
                "b2007d4c-adff-4800-835d-4424c425e342": [
                    {
                        "city": "00",
                        "city_code": None,
                        "country": "Việt Nam",
                        "country_code": "VNM",
                        "county": None,
                        "county_code": None,
                        "detail": "",
                        "district": None,
                        "district_code": None,
                        "state": None,
                        "state_code": None,
                        "subdistrict": None,
                        "subdistrict_code": None,
                        "type": "permanent_address",
                        "type_address": "fixed",
                        "unique_value": "VNM#",
                        "value_cccd": "",
                    }
                ],
                "d9e87a09-057a-4f06-b386-f75abc8c6f96": [
                    {"field_key": "product_type", "key": "6593e360181e893ecbc0776c", "name": "KHCN_TGTT_TGTT"},
                    {"field_key": "product", "key": "67ed18e2d0a111b135011d3d", "name": "19100"},
                    {"field_key": "product_line", "key": "KHCN_TGTT", "name": "Tiền gửi thanh toán"},
                    {
                        "code": "STK_ID_CARD",
                        "field_key": "select_account_type",
                        "key": "STK_ID_CARD",
                        "name": "Tài khoản theo số CCCD",
                        "next_element": ["account_number"],
                        "price_type": "free",
                        "product_config": {
                            "product_code": "19100",
                            "product_id": "67ed18e2d0a111b135011d3d",
                            "product_line_code": "KHCN_TGTT",
                            "product_line_id": "65698fd7f4c905f2797ae126",
                            "product_type_code": "KHCN_TGTT_TGTT",
                            "product_type_id": "6593e360181e893ecbc0776c",
                        },
                    },
                    {
                        "fee": "500000",
                        "field_key": "account_number",
                        "key": "************",
                        "name": "************",
                        "premium_account_number": "************",
                        "premium_account_type_code": "N100000KHCN",
                        "premium_account_type_name": "10 So khong dep",
                    },
                ],
                "f7960662-b9f7-4f81-ba3b-a438d3818611": [
                    {
                        "field_key": "product",
                        "key": "67ff14c86295342bb248cd56",
                        "name": "E-Plus Online Banking (IB+MB) – eKYC Plus",
                    },
                    {"field_key": "product_line", "key": "KHCN_EXIMBANKEDIGI", "name": "Eximbank EDigi"},
                ],
                "field_config_web_form": {
                    "********-5381-4535-88a0-a6b3adc5ba60": {
                        "field_key": "_dyn_nghe_nghiep_cua_khach_hang_1696581120694",
                        "field_module": "profile",
                        "label": "Nghề nghiệp của khách hàng",
                    },
                    "0faf5bdb-1763-4012-bbb8-56a70c6bad0a": {
                        "field_key": "_dyn_ngay_cap_cccd_1714017181392",
                        "field_module": "profile",
                        "label": "Ngày cấp CCCD",
                    },
                    "133b7018-2bd5-4b20-b945-9705acdd5b6f": {
                        "field_key": None,
                        "field_module": "web_form",
                        "label": "Mục đích mở tài khoản",
                    },
                    "35c55dcd-eda7-41a9-8ec2-029c1de6323c": {
                        "field_key": "birthday",
                        "field_module": "profile",
                        "label": "Ngày, tháng, năm sinh",
                    },
                    "53869270-d81f-4f6a-9ddb-2d5b48ab0ea8": {
                        "field_key": "_dyn_noi_cap_cccd_1747291155358",
                        "field_module": "profile",
                        "label": "Nơi cấp CCCD",
                    },
                    "6dabfd57-7023-451a-b027-f010d8ebd663": {
                        "field_key": "_dyn_ngay_het_han_cccd_1714017181394",
                        "field_module": "profile",
                        "label": "Ngày hết hạn CCCD",
                    },
                    "7b9bc09b-043f-48ea-8b5a-e6a734763272": {
                        "field_key": "name",
                        "field_module": "profile",
                        "label": "Họ và tên",
                    },
                    "80c161ca-1af0-42f8-9f13-397fa8e33c39": {
                        "field_key": "nationality",
                        "field_module": "profile",
                        "label": "Quốc gia",
                    },
                    "85a90f7c-9796-4eb8-af38-18e77369520c": {
                        "field_key": "primary_phone",
                        "field_module": "profile",
                        "label": "Số điện thoại",
                    },
                    "90468cc3-7185-44c0-bc34-a0425bcc42e3": {
                        "field_key": "primary_email",
                        "field_module": "profile",
                        "label": "E-mail",
                    },
                    "a01e9d64-ae59-4a73-9698-9a8732c8bf3f": {
                        "field_key": "profile_identify",
                        "field_module": "profile",
                        "label": "Giấy tờ định danh",
                    },
                    "b2007d4c-adff-4800-835d-4424c425e342": {
                        "field_key": "address_contact",
                        "field_module": "web_form",
                        "label": "Địa chỉ liên hệ",
                    },
                    "b89ec16d-6d18-4a02-9ada-485f7c49fc70": {
                        "field_key": "gender",
                        "field_module": "profile",
                        "label": "Giới tính",
                    },
                    "c4407853-d672-4113-bf15-2a6a40f033da": {
                        "field_key": "_dyn_noi_lam_viec_1747734087125",
                        "field_module": "profile",
                        "label": "Nơi làm việc",
                    },
                    "c79af929-3cf1-4ec7-b528-08e374d9c75a": {
                        "field_key": "profile_identify",
                        "field_module": "profile",
                        "label": "Giấy tờ định danh",
                    },
                    "d0ab10f8-b383-4f16-b6d5-de9ca9ef0c98": {
                        "field_key": "_dyn_chuc_vu_1638341818684",
                        "field_module": "profile",
                        "label": "Chức vụ",
                    },
                    "d9e87a09-057a-4f06-b386-f75abc8c6f96": {
                        "field_key": "ekyc_select_product_bank",
                        "field_module": "web_form",
                        "label": "Dòng sản phẩm",
                    },
                    "e44e9feb-a101-4fe1-8389-6f39b92dbb4e": {
                        "field_key": "primary_phone",
                        "field_module": "profile",
                        "label": "Số điện thoại",
                    },
                    "eaf576ce-e07b-4ecf-960b-b8e6fbda09b3": {
                        "field_key": "marital_status",
                        "field_module": "profile",
                        "label": "Tình trạng hôn nhân",
                    },
                    "f7960662-b9f7-4f81-ba3b-a438d3818611": {
                        "field_key": "ekyc_select_product_bank",
                        "field_module": "web_form",
                        "label": "Dòng sản phẩm",
                    },
                },
            },
            "staff_info": {
                "username": "rbo1@eib",
                "fullname": "RBO1",
                "staff_code": "",
                "account_id": "54b9f636-2721-453b-b342-9882ebf2fba4",
                "area_code": "",
                "area_name": "",
                "sol_id": "",
                "sol_name": "",
                "scope_name": "",
                "scope_code": None,
                "position_name": "RBO",
                "position_code": "CD138",
            },
            "card_information": {
                "deviceType": "TEST_DEVICE",
                "dateOfExpiry": "10/09/2035",
                "fatherName": "",
                "ethnic": None,
                "address": "",
                "gender": "Nam",
                "idCard": "************",
                "placeOfResidence": ", Giao Thủy, Nam Định",
                "motherName": "",
                "dateOfBirth": "23/06/2000",
                "dateOfIssuance": "08/12/2024",
                "personalSpecificIdentification": "Sẹo chấm C:1cm5 sau cánh mũi trái",
                "religion": "Phật giáo",
                "nationality": "Việt Nam",
                "oldIdCardNo": "",
                "placeOfOrigin": "Giao Thủy, Nam Định",
                "name": "Nguyễn Anh Quân",
                "spouseName": "",
                "cardImage": "iVBORw0KGgoAAAANSUhEUgAAAlgAAAMgCAIAAABwAouTAAAFi0lEQVR4nO3BMQEAAADCoPVPbQwfoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC+Bv1bAAGXiLn2AAAAAElFTkSuQmCC",
                "typeCard": "CCUOC",
                "placeOfIssuance": "Bo Cong An",
                "codePlaceOfIssuance": "01485",
            },
            "body_save_customer": {
                "cif": None,
                "faceImage": "/media/data/resources/mobile_backend/image_verify/face_image_quick_sales_eebb0bd0-aceb-4492-a63d-e6a90152ae04.json",
                "issuePlace": "Bo Cong An",
                "statusC06": None,
                "resultC06": None,
                "typeCard": "CCUOC",
                "deviceType": "TEST_DEVICE",
                "dateOfExpiry": "10/09/2035",
                "fatherName": "",
                "ethnic": None,
                "address": "",
                "gender": "Nam",
                "idCard": "************",
                "placeOfResidence": ", Giao Thủy, Nam Định",
                "motherName": "",
                "dateOfBirth": "23/06/2000",
                "dateOfIssuance": "08/12/2024",
                "personalSpecificIdentification": "Sẹo chấm C:1cm5 sau cánh mũi trái",
                "religion": "Phật giáo",
                "nationality": "Việt Nam",
                "oldIdCardNo": "",
                "placeOfOrigin": "Giao Thủy, Nam Định",
                "name": "Nguyễn Anh Quân",
                "spouseName": "",
                "nfcImage": "iVBORw0KGgoAAAANSUhEUgAAAlgAAAMgCAIAAABwAouTAAAFi0lEQVR4nO3BMQEAAADCoPVPbQwfoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC+Bv1bAAGXiLn2AAAAAElFTkSuQmCC",
                "placeOfIssuance": "Bo Cong An",
                "codePlaceOfIssuance": "01485",
            },
        }
    )
    print(x)
