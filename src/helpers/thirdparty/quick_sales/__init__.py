#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/02/2025
"""

import base64
import datetime
import hashlib
import hmac
import json
import re
import time
from contextlib import contextmanager
from random import Random

import jwt
from Crypto import Random
from Crypto.Cipher import AES
from Crypto.Util import Counter
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from mobio.libs.logging import MobioLogging
from requests import Response

from scripts.test.script_test_sign_key_vnpay import CMSSignedData
from src.common import ConstantKeyConfigSendThirdParty, StatusCode
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)


class BaseQuickSalesHelper:
    FORMAT_DATE_TIME = "%Y-%m-%dT%H:%M:%S.000"

    def __init__(self):
        self.logger = MobioLogging()

    @contextmanager
    def timer(self):
        start = time.perf_counter()
        yield
        return time.perf_counter() - start

    def _log_request(
        self,
        request_type,
        config,
        payload,
        request_id_start,
        info_request,
        response,
        request_id,
        time_request,
    ):
        """Log the request details."""
        if isinstance(response, Response):
            response = response.text
        log_request_id = LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type=request_type,
            config=config,
            data=payload,
            info_request=info_request,
            response=response,
            log_id=request_id,
            log_id_start=request_id_start,
            time_request=time_request,
        )
        return str(log_request_id)

    def sha512_hash(self, text):
        return hashlib.sha512(text.encode("utf-8")).hexdigest()

    def create_jwt(self, jwt_key, jwt_secret, jwt_audience, expires_in_seconds=86400):
        """
        Creates a JWT token in Python.

        Args:
            jwt_key (str): Key ID for the token.
            jwt_secret (str): Secret key used for signing the token.
            jwt_audience (str): Intended audience for the token.
            expires_in_seconds (int, optional): Expiration time in seconds. Defaults to 30.

        Returns:
            str: The encoded JWT token.
        """

        # Ensure secret is bytes in UTF-8 encoding
        secret_bytes = jwt_secret.encode("utf-8")

        # Create the JWT payload
        payload = {
            "iat": datetime.datetime.now(datetime.UTC),  # Issued at
            "exp": datetime.datetime.now(datetime.UTC) + datetime.timedelta(seconds=expires_in_seconds),  # Expiration
            "aud": jwt_audience,
        }

        # Create the JWT token with PyJWT library
        algorithm = "HS256"

        token = jwt.encode(payload, secret_bytes, algorithm=algorithm, headers={"kid": jwt_key})
        return token

    def generate_sha256_with_concatenated_salt(self, config, plainText):
        """
        Generates an SHA256 hash by concatenating plainText and secretKey (as salt).

        Args:
            plainText (str): The original data.
            config (dict): The configuration dictionary containing the secret key.

        Returns:
            str: The hexadecimal representation of the SHA256 hash.
        """
        # Combine the plainText and secretKey (salt)
        data_with_salt = plainText + config.get(
            ConstantKeyConfigSendThirdParty.HMAC_SECRET, "ecR625dWozUbagJ9bSsp.sh9opr56h.oiudhh7ks@"
        )

        # Encode the combined string to bytes
        data_bytes = data_with_salt.encode("utf-8")

        # Create a SHA256 hash object
        sha256_hash = hashlib.sha256()

        # Update the hash object with the data
        sha256_hash.update(data_bytes)

        # Get the hexadecimal representation of the hash
        return sha256_hash.hexdigest()

    def build_headers(self, config, payload):
        """
        Builds the headers for the request.

        Args:
            config (dict): Configuration for the request.

        Returns:
            dict: The headers for the request.
        """

        jwt_key = config.get(ConstantKeyConfigSendThirdParty.JWT_KEY)
        jwt_secret = config.get(ConstantKeyConfigSendThirdParty.JWT_SECRET)
        jwt_audience = config.get(ConstantKeyConfigSendThirdParty.JWT_AUDIENCE)
        jwt_token = self.create_jwt(jwt_key, jwt_secret, jwt_audience)
        x_client_id = config.get(ConstantKeyConfigSendThirdParty.X_CLIENT_ID)
        # string_body = "".join(list(payload.values()))
        plaintext = json.dumps(payload, separators=(",", ":"), ensure_ascii=False)
        hash_plaintext = self.sha512_hash(plaintext)
        self.logger.info("build_headers :: hash_plaintext :: {}".format(hash_plaintext))
        signature_body = self.build_signature_by_plaintext(config, hash_plaintext)
        basic_token = self.create_basic_auth(
            config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME), config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        )

        headers = {
            "Content-Type": "application/json",
            "X-Authorization": "Bearer " + jwt_token,
            "X-ClientId": x_client_id,
            "X-Signature": signature_body,
            "Authorization": "Basic " + basic_token,
        }
        return headers

    def fix_header_value(self, header_value):
        # Remove leading whitespace
        header_value = header_value.strip()

        # Convert to bytes
        header_value_bytes = header_value.encode("utf-8")

        # Base64 encode and decode to remove invalid characters
        encoded_value = base64.b64encode(header_value_bytes)
        decoded_value = base64.b64decode(encoded_value)

        # Convert back to string
        fixed_value = decoded_value.decode("utf-8")

        return fixed_value

    def encrypt(self, key, plaintext):
        iv = Random.new().read(AES.block_size)

        ctr = Counter.new(128, initial_value=int.from_bytes(iv, byteorder="big"))

        cipher = AES.new(bytes(key, "utf-8"), AES.MODE_CTR, counter=ctr)
        return base64.b64encode(iv + cipher.encrypt(bytes(plaintext, "utf-8")))

    def decrypt(self, key, ciphertext):
        enc = base64.urlsafe_b64decode(ciphertext)
        iv = enc[: AES.block_size]
        ctr = Counter.new(128, initial_value=int.from_bytes(iv, byteorder="big"))

        cipher = AES.new(bytes(key, "utf-8"), AES.MODE_CTR, counter=ctr)

        return cipher.decrypt(enc[AES.block_size :]).decode("utf-8")

    def build_signature_by_plaintext(self, config, plaintext):
        certificates_path = config.get(ConstantKeyConfigSendThirdParty.CERTIFICATES_PATH)
        pem_pass_phrase = config.get(ConstantKeyConfigSendThirdParty.PEM_PASS_PHRASE)
        # signature
        key_signer, cert_signer = CMSSignedData.read_key_cer_p12(
            private_key_path=certificates_path, certificate_password=pem_pass_phrase
        )
        signature = CMSSignedData.sign_message(
            plaintext, key_signer, cert_signer, digest_alg=config.get(ConstantKeyConfigSendThirdParty.DIGEST_ALG)
        )
        signed_cert_b64 = signature.decode("utf-8").replace("\n", "")
        return signed_cert_b64

    def gen_signature_vnpay(self, config, plaintext):
        certificates_path = config.get(ConstantKeyConfigSendThirdParty.VNPAY_CERTIFICATES_PATH)
        # pem_pass_phrase = config.get(ConstantKeyConfigSendThirdParty.PEM_PASS_PHRASE)
        # signature
        private_key = CMSSignedData.load_rsa_private_key(certificates_path)

        # Generate signature
        signature = CMSSignedData.gen_signature_by_file_rsa(private_key, plaintext)
        return signature

    def create_basic_auth(self, user_name: str, password: str) -> str:
        auth_str = f"{user_name}:{password}"
        auth_bytes = auth_str.encode("ascii")
        base64_bytes = base64.b64encode(auth_bytes)
        base64_str = base64_bytes.decode("ascii")
        return base64_str

    def handle_response(
        self,
        key_get_response_code,
        key_get_response_desc,
        response,
        mapping_code_message_error,
        mapping_code_message_success,
    ):
        data_response = None
        reasons = ""
        status_code = None
        try:
            data_response = response.json()
            ResponseCode = data_response.get(key_get_response_code)
            ResponseDesc_response = data_response.get(key_get_response_desc)

            status_code = StatusCode.THIRD_PARTY_FAILURE
            if not ResponseCode:
                reasons = mapping_code_message_error.get(ResponseCode)
            if mapping_code_message_success.get(ResponseCode):
                status_code = StatusCode.SUCCESS
            else:
                if mapping_code_message_error.get(ResponseCode):
                    reasons = mapping_code_message_error.get(ResponseCode)
                else:
                    reasons = "Mã code lỗi {} chưa được khai báo".format(ResponseCode)

        except Exception as e:  # noqa
            MobioLogging().error("ThirdPartyEIB :: handle_response :: {}".format(e))
            result = response.text
            if not re.search("Successful|0</ResponseCode", result):
                reasons = result
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        return data_response, status_code, reasons

    def encrypt_tripledes_ecb_pkcs7(self, encryption_key: str, to_encrypt: str) -> str:
        """
        Mã hóa một chuỗi sử dụng TripleDES với chế độ ECB và padding PKCS7,
        trả về kết quả dưới dạng chuỗi Base64.

        Args:
            encryption_key: Khóa mã hóa (chuỗi). Phải có độ dài khi mã hóa UTF-8 là 16 hoặc 24 byte.
            to_encrypt: Chuỗi cần mã hóa.

        Returns:
            Chuỗi Base64 chứa dữ liệu đã mã hóa.

        Raises:
            ValueError: Nếu độ dài khóa không hợp lệ.
            Exception: Nếu có lỗi trong quá trình mã hóa.
        """
        try:
            # 1. Chuyển đổi khóa và dữ liệu sang bytes (sử dụng UTF-8)
            key_bytes = encryption_key.encode("utf-8")
            data_bytes = to_encrypt.encode("utf-8")

            # TripleDES yêu cầu độ dài khóa là 16 (cho 2-key 3DES) hoặc 24 bytes (cho 3-key 3DES)
            if len(key_bytes) not in [16, 24]:
                raise ValueError("TripleDES key must be 16 or 24 bytes long when encoded in UTF-8.")

            # 2. Chọn backend mặc định
            backend = default_backend()

            # 3. Tạo đối tượng thuật toán TripleDES
            algorithm = algorithms.TripleDES(key_bytes)

            # 4. Tạo đối tượng chế độ mã hóa ECB
            mode = modes.ECB()

            # 5. Tạo đối tượng Cipher
            cipher = Cipher(algorithm, mode, backend=backend)

            # 6. Tạo đối tượng Encryptor
            encryptor = cipher.encryptor()

            # 7. Áp dụng padding PKCS7
            # TripleDES có kích thước khối là 64 bit (8 bytes)
            padder = padding.PKCS7(algorithms.TripleDES.block_size).padder()
            padded_data = padder.update(data_bytes) + padder.finalize()

            # 8. Thực hiện mã hóa
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()

            # 9. Chuyển đổi dữ liệu đã mã hóa sang chuỗi Base64
            base64_encoded = base64.b64encode(encrypted_data).decode("utf-8")

            return base64_encoded

        except ValueError as ve:
            self.logger.error(f"Lỗi về khóa: {ve}")
            raise ValueError(ve)
        except Exception as e:
            self.logger.error(f"Lỗi mã hóa: {e}")
            raise


if __name__ == "__main__":
    print(BaseQuickSalesHelper().encrypt_tripledes_ecb_pkcs7("B89vl8144vwFOHEb", "123454"))
