#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 05/05/2024
"""

import datetime
import json
import uuid

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    ConstantMessageSendRequestToThirdParty,
    ConstantNameFlow,
    KeyConfigNotifySDK,
    StatusCode,
    StatusCodePushSocket,
    ThirdPartyType,
)
from src.common.utils import utf8_to_ascii
from src.controllers.decryption_controller import ConstantBodyIdentificationCard
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.helpers.thirdparty.quick_sales.func_helper import (
    get_type_card_and_address_of_issuance,
)
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_pre_approved_model import LogPreApprovedModel
from src.models.mongo.log_request_card_model import LogRequestCardModel
from src.models.mongo.log_request_check_cif_model import LogRequestCheckCifModel


class SendRequestOnlyReadCard:
    @staticmethod
    def send_request(log_id, merchant_id, request_body, account_id, action_time, flow_name, log_id_start=None):
        func_visit = "SendRequestOnlyReadCard"
        log_id_push_socket = log_id_start if log_id_start else log_id
        data, config, sdk_request_id = SendRequestOnlyReadCard._build_config_send_identification_card(
            merchant_id, request_body
        )
        data_response, status_code, reasons = ThirdPartyEIB.read_card(
            log_id=log_id,
            config=config,
            data=data,
            merchant_id=merchant_id,
            account_id=account_id,
            log_id_start=log_id_start,
            sdk_request_id=sdk_request_id,
        )
        MobioLogging().info(
            "Sending identification_card:: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            data_send_socket = {
                "data": {
                    "status": StatusCodePushSocket.FAIL,
                    "reason": reasons,
                    "log_id": log_id_push_socket,
                    "func_visit": func_visit,
                }
            }
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )
            return

        data_insert_log_card = {
            "merchant_id": merchant_id,
            "log_id_request": log_id_push_socket,
            "account_id": account_id,
            "request_action_time": action_time,
            "flow_name": flow_name,
            "lst_func_handle": [func_visit],
            "isAuthInformation": False,
            "isAuthTemporary": False,
            "isAuthAML": False,
            "isAuthExistCifByCustomerExist": False,
        }

        try:
            data_response_json = json.loads(data_response.get("dataResponse"))
        except Exception as ex:
            data_response_json = None
            data_insert_log_card.update({"error_json_load": str(ex)})

        date_of_issuance = data_response_json.get("dateOfIssuance", "")
        type_card, address_of_issuance, code_address_of_issuance = get_type_card_and_address_of_issuance(
            date_of_issuance
        )
        data_response_json.update(
            {
                "typeCard": type_card,
                "placeOfIssuance": address_of_issuance,
                "codePlaceOfIssuance": code_address_of_issuance,
            }
        )

        data_insert_log_card.update({"cardInformation": data_response_json})
        """
        Data sample::
        {
            'deviceType': 'TEST_DEVICE',
            'dateOfExpiry': '10/09/2035',
            'fatherName': '', 
            'ethnic': 'Kinh', 
            'address': 'HANOI', 
            'gender': 'Nam', 
            'idCard': '056095011725', 
            'placeOfResidence': 'Lô 15, Ô 5, Thôn Đất Lành, Vĩnh Thái, Nha Trang, Khánh Hòa', 
            'motherName': '', 
            'dateOfBirth': '10/09/1995', 
            'personalSpecificIdentification': 'Sẹo chấm C:1cm5 sau cánh mũi trái', 
            'dateOfIssuance': '12/09/2022', 
            'religion': 'Phật giáo', 
            'nationality': 'Việt Nam', 
            'oldIdCardNo': '225576873', 
            'placeOfOrigin': 'Nha Trang, Khánh Hòa', 
            'name': 'Nguyễn Hồ Phi Long', 
            'spouseName': '',
            'cardImage': ''
        }
        """

        MobioLogging().info("SendRequestOnlyReadCard :: data_response_json :: {}".format(data_response_json))

        insert_log_read_card_id = LogRequestCardModel().insert_document(data_insert_log_card)
        MobioLogging().info(
            "SendRequestOnlyReadCard :: insert_log_read_card_id :: {}".format(str(insert_log_read_card_id))
        )

        status_code_push_socket = StatusCodePushSocket.SUCCESS
        data_in_data_send_socket = {
            "status": status_code_push_socket,
            "log_id": log_id_push_socket,
            "func_visit": func_visit,
            "isSameCardNoRequest": True,
        }

        if data_response_json:

            cardImage = data_response_json.get("cardImage")
            if cardImage:
                data_send_gen_avatar = {
                    "log_id": log_id_push_socket,
                    "message_type": "build_avatar_profile",
                    "data_build_avatar": cardImage,
                }
                MobioLogging().info("SendRequestOnlyReadCard :: push message to gen avatar")
                Producer().push_message_to_build_information_related_profile(
                    merchant_id, account_id, action_time, data_send_gen_avatar
                )

        if flow_name == ConstantNameFlow.PRE_APPROVED:
            MobioLogging().info("SendRequestOnlyReadCard :: flow_name == ConstantNameFlow.PRE_APPROVED")
            data_in_data_send_socket.update(
                {
                    "cardInformation": data_response_json,
                    "isNextAuthFace": True,
                    "isAuthTemporary": True,
                }
            )
            LogPreApprovedModel().update_by_set(
                {
                    "merchant_id": merchant_id,
                    "session_id": log_id_push_socket,
                },
                {"cardInformation": data_response_json},
            )
            data_send_socket = {"data": data_in_data_send_socket}
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )
            return
        if log_id_start:
            request_check_cif = LogRequestCheckCifModel().find_by_log_request_id(merchant_id, log_id_start)
            if request_check_cif:
                body_request = request_check_cif.get("body_request")
                identify = body_request.get("identify")
                id_card_request = identify.get("identify_value")
                phone_number_request = request_check_cif.get("phone_number")
                if id_card_request != data_response_json.get("idCard"):
                    data_in_data_send_socket.update(
                        {
                            "isSameCardNoRequest": False,
                            "cardInformation": data_response_json,
                            "idCardRequestCheckCif": id_card_request,
                        }
                    )
                    data_send_socket = {"data": data_in_data_send_socket}
                    Producer().push_message_push_socket_notify_mobile(
                        merchant_id=merchant_id,
                        account_id=account_id,
                        message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                        data_send=data_send_socket,
                    )
                else:
                    # Bỏ logic check customer exist và check unique id của luồng Quick Sales
                    # Bổ sung thêm logic check customer exist với số

                    # Ngày: 29/04/2025
                    # Bổ sung thêm luồng check unique_id với tham số OTHER
                    profile_name_get_read_card = data_response_json.get("name")
                    dateOfBirth = data_response_json.get("dateOfBirth")
                    # Xử lý bỏ dấu và upper case
                    profile_name = utf8_to_ascii(profile_name_get_read_card).upper()
                    body_request_aml = QuickSalesHelper().build_request_body_check_aml(
                        profile_name, phone_number_request, identify
                    )
                    MobioLogging().info("SendRequestOnlyReadCard :: body_request_aml :: {}".format(body_request_aml))

                    response_aml, status_code_aml, reasons_aml, log_id_aml = QuickSalesHelper().check_aml(
                        merchant_id, body_request_aml
                    )
                    MobioLogging().info(
                        "_send_request_check_aml :: log_id: {}, response_aml: {}, status_code: {}, reasons: {}".format(
                            log_id_aml, response_aml, status_code_aml, reasons_aml
                        )
                    )

                    if status_code_aml != StatusCode.SUCCESS:
                        data_in_data_send_socket.update(
                            {
                                "cardInformation": data_response_json,
                                "idCardRequestCheckCif": id_card_request,
                                "status": StatusCodePushSocket.FAIL,
                                "reason": "Đối tượng thuộc danh sách nghi ngờ.",
                                "isAuthAML": True,
                            }
                        )
                        data_send_socket = {"data": data_in_data_send_socket}
                        Producer().push_message_push_socket_notify_mobile(
                            merchant_id=merchant_id,
                            account_id=account_id,
                            message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                            data_send=data_send_socket,
                        )
                        return

                    idCard = data_response_json.get("oldIdCardNo")
                    if not idCard:
                        idCard = data_response_json.get("idCard")

                    # Bổ sung thêm nghiệp vụ call API CheckUniqueId
                    (
                        data_request_unique_id,
                        config_request_unique_id,
                    ) = SendRequestOnlyReadCard()._build_body_config_check_unique_id(
                        merchant_id, profile_name, dateOfBirth
                    )

                    log_id_unique_id = str(uuid.uuid1())

                    data_response_unique_id, status_code_unique_id, reasons_unique_id = ThirdPartyEIB.check_unique_id(
                        log_id_unique_id,
                        config_request_unique_id,
                        data_request_unique_id,
                        account_id=None,
                        merchant_id=merchant_id,
                        log_id_start=log_id_start,
                    )
                    MobioLogging().info(
                        "SendRequestOnlyReadCard :: checkUniqueId :: log_id: {}, status_code: {}, reasons: {}".format(
                            log_id_unique_id, status_code_unique_id, reasons_unique_id
                        )
                    )
                    send_noti_from_check_unique_id = False
                    cifInformation = None
                    if data_response_unique_id and data_response_unique_id.get("errorCode") in ["99", "93"]:
                        cifInformation = data_response.get("errorDesc")
                        if cifInformation:
                            send_noti_from_check_unique_id = True

                    else:
                        if status_code_unique_id == StatusCode.THIRD_PARTY_FAILURE:
                            send_noti_from_check_unique_id = True

                    if send_noti_from_check_unique_id:
                        data_in_data_send_socket.update(
                            {
                                "cardInformation": data_response_json,
                                "idCardRequestCheckCif": id_card_request,
                                "status": StatusCodePushSocket.FAIL,
                                "reason": "Họ tên và Ngày tháng năm sinh của của khách hàng đã tồn tại CIF. Để đăng ký SPDV vui lòng tư vấn khách hàng đến Đơn vị để tiếp tục thực hiện.",
                                "isCheckUniqueIdByNameDob": True,
                            }
                        )
                        data_send_socket = {"data": data_in_data_send_socket}
                        Producer().push_message_push_socket_notify_mobile(
                            merchant_id=merchant_id,
                            account_id=account_id,
                            message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                            data_send=data_send_socket,
                        )
                        return
                    body_push_socket = {
                        "body_customer_exist": {
                            "idCard": idCard,
                            "name": data_response_json.get("name"),
                            "dateOfBirth": data_response_json.get("dateOfBirth"),
                            "gender": data_response_json.get("gender"),
                            "dateOfIssuance": data_response_json.get("dateOfIssuance"),
                        },
                        "body_check_card": request_body,
                    }
                    log_id_new = str(uuid.uuid1())
                    MobioLogging().info("_send_identification_card_third_party :: log_id_new :: {}".format(log_id_new))
                    data_send = {
                        ConstantMessageSendRequestToThirdParty.LOG_ID: log_id_new,
                        ConstantMessageSendRequestToThirdParty.LOG_ID_START: log_id_push_socket,
                        ConstantMessageSendRequestToThirdParty.REQUEST_BODY: body_push_socket,
                        ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_EXIST,
                        ConstantMessageSendRequestToThirdParty.FLOW_NAME: flow_name,
                    }
                    Producer().push_message_to_request_third_party(
                        merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
                    )

        return

    def _build_body_config_check_unique_id(self, merchant_id, profile_name, dob):
        config = ConfigInfoApiModel().get_config_info_api_check_unique_id(merchant_id)
        if not config:
            raise CustomError("Not config send check customer exist")
        messageDateTime = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000")
        request_body = {
            "requestUUID": str(uuid.uuid4()),
            "messageDateTime": messageDateTime,
            "uniqueidType": "",
            "uniqueId": "",
            "name": profile_name,
            "dob": dob,
            "taxId": "",
        }

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: request_body}
            }
        }

        return data, config

    @staticmethod
    def _build_config_send_identification_card(merchant_id, request_body):
        config_send_identification_card = ConfigInfoApiModel().get_config_info_api_sent_identification_card(merchant_id)
        if not config_send_identification_card:
            raise CustomError("Not config send identification card")

        data_decryption = request_body.get(ConstantBodyIdentificationCard.DATA_DECRYPTION)
        sdk_request_round = request_body.get(ConstantBodyIdentificationCard.SDK_REQUEST_ROUND)
        sdk_request_session = request_body.get(ConstantBodyIdentificationCard.SDK_REQUEST_SESSION)
        request_timestamp = request_body.get(ConstantBodyIdentificationCard.REQUEST_TIMESTAMP)
        sdk_request_id = request_body.get(ConstantBodyIdentificationCard.SDK_REQUEST_ID)

        data_request = {
            "encryptedData": data_decryption,
            "requestTimeStamp": int(request_timestamp),
            "requestSession": sdk_request_session,
            "requestRound": sdk_request_round,
        }
        str_data_request = json.dumps(data_request)

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.CONTENT: str_data_request,
                },
            }
        }

        return data, config_send_identification_card, sdk_request_id
