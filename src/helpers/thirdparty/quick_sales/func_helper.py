#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 15/05/2025
"""

import datetime

from mobio.libs.logging import MobioLogging


def get_type_card_and_address_of_issuance(date_of_issuance):

    type_card = "CCUOC"
    address_of_issuance = "Bo Cong An"
    code_address_of_issuance = "01485"
    if not date_of_issuance:
        return type_card, address_of_issuance, code_address_of_issuance

    time_condition_get_type_card = datetime.datetime.strptime("01/07/2024", "%d/%m/%Y")

    # <PERSON><PERSON><PERSON><PERSON> đổi chuỗi ngày tháng thành đối tượng datetime
    try:
        dateTimeOfIssuance = datetime.datetime.strptime(date_of_issuance, "%d/%m/%Y")
    except Exception as ex:
        MobioLogging().error(
            "get_type_card_and_address_of_issuance :: date_of_issuance :: {}, error :: {}".format(
                date_of_issuance, str(ex)
            )
        )
        dateTimeOfIssuance = None

    if dateTimeOfIssuance and dateTimeOfIssuance <= time_condition_get_type_card:
        type_card = "CCCD"
        address_of_issuance = "Cuc truong cuc CS QLHC ve TTXH"
        code_address_of_issuance = "01075"
    return type_card, address_of_issuance, code_address_of_issuance
