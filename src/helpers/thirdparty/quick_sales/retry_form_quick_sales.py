#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 28/03/2025
"""

import datetime

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError, InputNotFoundError

from configs.kafka_config import KAFKA_TOPIC
from src.common import ConstantQuickSales
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.log_customer_full_flow_quick_sales_model import (
    LogCustomerFullFlowQuickSalesModel,
)


class RetryFormQuickSales:

    @staticmethod
    def send_request_retry_form(merchant_id, account_id, form_id, object_id, object_type):
        log_customer_full_flow_quick_sales = (
            LogCustomerFullFlowQuickSalesModel().get_log_customer_full_flow_quick_sales_by_object_id(
                merchant_id, object_id, object_type, form_id
            )
        )
        if not log_customer_full_flow_quick_sales:
            raise InputNotFoundError("Log customer full flow quick sales not found!!")

        status_form = log_customer_full_flow_quick_sales.get("status")
        if status_form != ConstantQuickSales.ConstantStatusFlowQuickSales.FAIL:
            raise CustomError("Không thể retry khi form chưa thất bại")

        # TODO: Gọi lại api submit form
        RetryFormQuickSales.process_retry_form_to_third_party(log_customer_full_flow_quick_sales)

        return

    @staticmethod
    def process_retry_form_to_third_party(log_customer_full_flow_quick_sales):
        run_steps = [
            ConstantQuickSales.ConstantStepFlow.STEP_SUBMIT_FORM,
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF,
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_ACCOUNT_INDIV,
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_REGISTER_EDIGI_ACCOUNT,
            ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_UPDATE_DATA_TO_CRM,
            ConstantQuickSales.ConstantStepFlow.STEP_END_FLOW,
        ]

        log_customer_full_flow_id = str(log_customer_full_flow_quick_sales.get("_id"))
        merchant_id = log_customer_full_flow_quick_sales.get("merchant_id")
        staff_id = log_customer_full_flow_quick_sales.get("staff_id")
        action_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S.%f")
        result_of_previous_step = {}

        LogCustomerFullFlowQuickSalesModel().update_one_query(
            {"_id": log_customer_full_flow_id},
            {"$set": {"status": ConstantQuickSales.ConstantStatusFlowQuickSales.PROCESSING}},
        )

        for step in run_steps:
            result_step = log_customer_full_flow_quick_sales.get(step, {})
            result_step_status = result_step.get("status_step")
            MobioLogging().info("RetryFormQuickSales:process_retry_form_to_third_party:STEP: %s" % step)
            if step == ConstantQuickSales.ConstantStepFlow.STEP_SUBMIT_FORM:
                # TODO: Gọi lại api submit form
                pass
            if step == ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF:
                if result_step_status != ConstantQuickSales.ConstantStatusFlowQuickSales.SUCCESS:
                    # TODO: Gọi lại api create cif
                    log_id = Producer().send_message_to_topic(
                        KAFKA_TOPIC.QUICK_SALES_RES_CIF,
                        {
                            "log_customer_full_flow_id": log_customer_full_flow_id,
                            "merchant_id": merchant_id,
                            "staff_id": staff_id,
                            "action_time": action_time,
                        },
                    )
                    MobioLogging().info("RetryFormQuickSales:process_retry_form_to_third_party:STEP: %s" % step)
                    MobioLogging().info("RetryFormQuickSales:process_retry_form_to_third_party:log_id: %s" % log_id)
                    break
                result_step_current = result_step.get("result_of_previous_step", [])
                for item in result_step_current:
                    if item.get("step") == ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_CIF:
                        result_of_previous_step = item.get("result")

            if step == ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_ACCOUNT_INDIV:
                # TODO: Gọi lại api create account individual

                if result_step_status != ConstantQuickSales.ConstantStatusFlowQuickSales.SUCCESS:
                    # TODO: Gọi lại api create account individual
                    log_topic_kafka_id = Producer().send_message_to_topic(
                        KAFKA_TOPIC.QUICK_SALES_CREATE_ACC,
                        {
                            "log_customer_full_flow_id": log_customer_full_flow_id,
                            "merchant_id": merchant_id,
                            "staff_id": staff_id,
                            "action_time": action_time,
                            "result_of_previous_step": result_of_previous_step,
                        },
                    )
                    MobioLogging().info("RetryFormQuickSales:process_retry_form_to_third_party:STEP: %s" % step)
                    MobioLogging().info(
                        "RetryFormQuickSales:process_retry_form_to_third_party:log_id: %s" % log_topic_kafka_id
                    )
                    break
                result_step_current = result_step.get("result_of_previous_step", [])
                for item in result_step_current:
                    if item.get("step") == ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_CREATE_ACCOUNT_INDIV:
                        result_of_previous_step = item.get("result")

            if step == ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_REGISTER_EDIGI_ACCOUNT:
                # TODO: Gọi lại api register edigi account
                if result_step_status != ConstantQuickSales.ConstantStatusFlowQuickSales.SUCCESS:
                    # push message to topic res end
                    log_push_kafka_id = Producer().send_message_to_topic(
                        KAFKA_TOPIC.QUICK_SALES_REGISTER_EDIGI_ACC,
                        {
                            "log_customer_full_flow_id": log_customer_full_flow_id,
                            "merchant_id": merchant_id,
                            "staff_id": staff_id,
                            "action_time": action_time,
                        },
                    )
                    MobioLogging().info("RetryFormQuickSales:process_retry_form_to_third_party:STEP: %s" % step)
                    MobioLogging().info(
                        "RetryFormQuickSales:process_retry_form_to_third_party:log_id: %s" % log_push_kafka_id
                    )
                    break
                result_step_current = result_step.get("result_of_previous_step", [])
                for item in result_step_current:
                    if item.get("step") == ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_REGISTER_EDIGI_ACCOUNT:
                        result_of_previous_step = item.get("result")
            if step == ConstantQuickSales.ConstantStepFlow.STEP_HANDLE_UPDATE_DATA_TO_CRM:
                if result_step_status != ConstantQuickSales.ConstantStatusFlowQuickSales.SUCCESS:
                    # TODO: Gọi lại api update data to crm
                    log_topic_kafka_id = Producer().send_message_to_topic(
                        KAFKA_TOPIC.QUICK_SALES_UPDATE_TO_CRM,
                        {
                            "log_customer_full_flow_id": log_customer_full_flow_id,
                            "merchant_id": merchant_id,
                            "staff_id": staff_id,
                            "action_time": action_time,
                        },
                    )
                    MobioLogging().info("RetryFormQuickSales:process_retry_form_to_third_party:STEP: %s" % step)
                    MobioLogging().info(
                        "RetryFormQuickSales:process_retry_form_to_third_party:log_id: %s" % log_topic_kafka_id
                    )
                    break
            if step == ConstantQuickSales.ConstantStepFlow.STEP_END_FLOW:
                # TODO: Gọi lại api end flow
                if result_step_status != ConstantQuickSales.ConstantStatusFlowQuickSales.SUCCESS:
                    # TODO: Gọi lại api end flow
                    log_topic_kafka_id = Producer().send_message_to_topic(
                        KAFKA_TOPIC.QUICK_SALES_UPDATE_TO_CRM,
                        {
                            "log_customer_full_flow_id": log_customer_full_flow_id,
                            "merchant_id": merchant_id,
                            "staff_id": staff_id,
                            "action_time": action_time,
                        },
                    )
                    MobioLogging().info("RetryFormQuickSales:process_retry_form_to_third_party:STEP: %s" % step)
                    MobioLogging().info(
                        "RetryFormQuickSales:process_retry_form_to_third_party:log_id: %s" % log_topic_kafka_id
                    )
                    break


if __name__ == "__main__":
    merchant_id = "57d559c1-39a1-4cee-b024-b953428b5ac8"
    object_id = "be4690d7-5ff5-4087-b4ba-798c310c7f90"
    object_type = "profile"
    form_id = "67c13217fc018eabed5a9b7a"
    RetryFormQuickSales.process_retry_form_to_third_party(
        LogCustomerFullFlowQuickSalesModel().get_log_customer_full_flow_quick_sales_by_object_id(
            merchant_id, object_id, object_type, form_id
        )
    )
