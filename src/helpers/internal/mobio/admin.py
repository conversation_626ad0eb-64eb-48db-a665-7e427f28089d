#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 09/05/2024
"""
import os

from mobio.libs.logging import MobioLogging
from mobio.sdks.admin import MobioAdminSD<PERSON>

from configs import MobileBackendApplicationConfig
from src.apis.uri import URI_INTERNAL
from src.common.init_lib import lru_redis_cache
from src.common.key_host import KeyHostService
from src.common.requests_retry import RequestRetryAdapter


class InternalAdminHelper:
    def get_basic_token(self):
        return "Basic {}".format(MobileBackendApplicationConfig.MOBIO_TOKEN)

    def forward_host_to_local(self, key):
        mapping_host = {
            KeyHostService.ADMIN_APP_API_SERVICE_HOST: MobileBackendApplicationConfig.ADMIN_HOST,
            KeyHostService.PROFILING_V4_APP_INTERNAL_API_SERVICE_HOST: "http://localhost:8088",
            KeyHostService.FORM_BUILDER_APP_INTERNAL_API_SERVICE_HOST: "http://localhost:8090",
            KeyHostService.PROFILING_INTERNAL_DATAFLOW_HOST: "http://localhost:8092",
        }
        return mapping_host.get(key, MobileBackendApplicationConfig.ADMIN_HOST)

    def get_internal_host_by_merchant(self, merchant_id, key):
        if os.environ.get("VM") == "local":
            return self.forward_host_to_local(key)

        internal_host = MobioAdminSDK().request_get_merchant_config_host(merchant_id=merchant_id, key=key)
        MobioLogging().info("get_internal_host_by_merchant :: get host :: {}, {}".format(key, internal_host))
        return internal_host

    @lru_redis_cache.add_for_class(expiration=10800)
    def get_sol_id_by_account_id(self, merchant_id, account_id):
        headers = {
            "Content-Type": "application/json",
            "Authorization": self.get_basic_token(),
            "X-Merchant-Id": merchant_id,
        }
        payload = {"account_ids": [account_id]}
        admin_host = self.get_internal_host_by_merchant(merchant_id, "admin-app-api-service-host")
        if not admin_host:
            return None
        url = URI_INTERNAL.ADMIN.GET_INFORMATION_EXTRA_BY_ACCOUNT_IDS
        url = url.format(host=admin_host)
        response = RequestRetryAdapter().retry_a_post_request(url, headers=headers, payload=payload)
        MobioLogging().info("InternalAdminHelper:Response: %s" % response)
        if response:
            response_json = response.json()
            MobioLogging().info("InternalAdminHelper:Response Json: %s" % response_json)
            data_response = response_json.get("data")
            for data in data_response:
                if data.get("account_id") == account_id:
                    return data.get("sol_id")

        return None

    @lru_redis_cache.add_for_class(expiration=10800)
    def get_information_extra_account(self, merchant_id, account_id):
        headers = {
            "Content-Type": "application/json",
            "Authorization": self.get_basic_token(),
            "X-Merchant-Id": merchant_id,
        }
        payload = {"account_ids": [account_id]}
        admin_host = self.get_internal_host_by_merchant(merchant_id, "admin-app-api-service-host")
        if not admin_host:
            return None
        url = URI_INTERNAL.ADMIN.GET_INFORMATION_EXTRA_BY_ACCOUNT_IDS
        url = url.format(host=admin_host)
        response = RequestRetryAdapter().retry_a_post_request(url, headers=headers, payload=payload)
        MobioLogging().info("InternalAdminHelper:get_information_extra_account: %s" % response)
        if response:
            response_json = response.json()
            MobioLogging().info("InternalAdminHelper:get_information_extra_account ::Response Json: %s" % response_json)
            data_response = response_json.get("data")
            if data_response:
                return data_response[0]

        return None

    # @lru_redis_cache.add_for_class(expiration=10800)
    def get_merchant_id_by_merchant_code(self, merchant_code):
        headers = {
            "Content-Type": "application/json",
            "Authorization": self.get_basic_token(),
        }
        payload = {"codes": merchant_code}
        admin_host = self.get_internal_host_by_merchant("mobio", "admin-app-api-service-host")
        if not admin_host:
            return None
        url = URI_INTERNAL.ADMIN.GET_MERCHANT_ID_BY_MERCHANT_CODE
        url = url.format(host=admin_host)
        response = RequestRetryAdapter().retry_a_get_request(url, headers=headers, params=payload)
        MobioLogging().info("InternalAdminHelper:: get_merchant_id_by_merchant_code :: Response: %s" % response)
        if response:
            response_json = response.json()
            MobioLogging().info(
                "InternalAdminHelper:: get_merchant_id_by_merchant_code Response Json: %s" % response_json
            )
            data_response = response_json.get("data")
            for data in data_response:
                if data.get("code") == merchant_code:
                    return data.get("id")

        return None

    # @lru_redis_cache.add_for_class(expiration=10800)
    def get_account_detail_info(self, merchant_id, account_id):
        headers = {
            "Content-Type": "application/json",
            "Authorization": self.get_basic_token(),
            "X-Merchant-ID": merchant_id,
        }

        admin_host = self.get_internal_host_by_merchant("mobio", "admin-app-api-service-host")

        url = URI_INTERNAL.ADMIN.DETAIL_ACCOUNT
        url = url.format(host=admin_host, merchant_id=merchant_id, account_id=account_id)
        MobioLogging().logger.info("get_account_detail_info url: {}".format(url))
        response = RequestRetryAdapter().retry_a_get_request(url, params=None, headers=headers, timeout=5)
        if response:
            MobioLogging().info("get_account_detail_info result: {}".format(response))
            detail_account = response.json()
            if detail_account:
                return {
                    "username": detail_account.get("username"),
                    "fullname": detail_account.get("fullname"),
                    "staff_code": detail_account.get("staff_code"),
                    "account_id": detail_account.get("field_extra", {}).get("account_id"),
                    "area_code": detail_account.get("field_extra", {}).get("area_code"),
                    "area_name": detail_account.get("field_extra", {}).get("area"),
                    "sol_id": detail_account.get("field_extra", {}).get("sol_id"),
                    "sol_name": detail_account.get("field_extra", {}).get("sol_name"),
                    "scope_name": detail_account.get("field_extra", {}).get("scope_name"),
                    "scope_code": detail_account.get("field_extra", {}).get("scope_code"),
                    "position_name": detail_account.get("field_extra", {}).get("TenChucDanhMoiNhat"),
                    "position_code": detail_account.get("field_extra", {}).get("MaChucDanhMoiNhat"),
                }
            return {}
        return {}

    @lru_redis_cache.add_for_class(expiration=3600)
    def get_list_account_by_level_account_current(self, merchant_id, account_id):
        headers = {
            "Content-Type": "application/json",
            "Authorization": self.get_basic_token(),
            "X-Merchant-ID": merchant_id,
        }

        admin_host = self.get_internal_host_by_merchant("mobio", "admin-app-api-service-host")

        url = URI_INTERNAL.ADMIN.GET_ACCOUNT_LEVEL_LOWER
        url = url.format(host=admin_host)
        MobioLogging().logger.info("get_list_account_by_level_account_current url: {}".format(url))
        response = RequestRetryAdapter().retry_a_get_request(
            url, params={"account_id": account_id}, headers=headers, timeout=15
        )
        if response:
            response_data = response.json()
            data = response_data.get("data")
            if data:
                MobioLogging().info("get_list_account_by_level_account_current result: {}".format(data))
                account_ids = [item.get("id") for item in data]
                return account_ids

        return []


if __name__ == "__main__":
    print(
        InternalAdminHelper().get_list_account_by_level_account_current(
            "57d559c1-39a1-4cee-b024-b953428b5ac8", "65eebbe6-24c3-416a-bbe4-0aebe92f6695"
        )
    )
