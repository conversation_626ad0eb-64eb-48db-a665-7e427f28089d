#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 20/02/2025
"""


from typing import Any, Dict, Optional

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError, DBLogicError

from src.apis.uri import URI_INTERNAL
from src.common import ConstantQuickSales
from src.common.key_host import KeyHostService
from src.common.requests_retry import RequestRetryAdapter
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.helpers.internal.mobio.profiling import ProfilingHelper


class FormBuilderHelper:

    def decode_form_token(self, form_token):
        """
        Call api detail form builder by form token
        """
        wfb_host = InternalAdminHelper().get_internal_host_by_merchant(
            merchant_id="mobio", key=KeyHostService.FORM_BUILDER_APP_INTERNAL_API_SERVICE_HOST
        )
        if not wfb_host:
            return None
        url = URI_INTERNAL.FORM_BUILDER.DETAIL_FORM_BUILDER_BY_TOKEN_LANDING_PAGE_EKYC
        url = url.format(domain=wfb_host)
        r = RequestRetryAdapter().retry_a_get_request(
            url, headers={"Content-Type": "application/json"}, params={"code": form_token}
        )
        if r:
            r = r.json()
            return r.get("error_code"), r.get("data")
        return "code_invalid", None

    def get_detail_form_builder_by_form_token(self, form_token):
        """
        Form token được tạo từ merchant_id, form_id, profile_id, active_time
        Dùng thuật toán SHA 256 và secret key để giải mã form_token.
        Trong đó, secret key được lấy từ file .env
        """

        # Lấy secret key từ file .env

        # Decode form_token
        try:
            error_code, decoded_form_token = self.decode_form_token(form_token)
        except Exception as e:
            MobioLogging().error("FormBuilderHelper:get_detail_form_builder_by_form_token:Error: {}".format(e))
            error_code, decoded_form_token = None, None
        MobioLogging().info(
            "FormBuilderHelper:get_detail_form_builder_by_form_token:decoded_form_token: error_code %s, decoded_form_token %s"
            % (error_code, decoded_form_token)
        )
        if not decoded_form_token or error_code:
            raise CustomError("Form token không hợp lệ!")

        # Lấy merchant_id, form_id, profile_id từ decoded_form_token
        merchant_id = decoded_form_token.get("merchant_id")
        form_id = decoded_form_token.get("form_id")
        object_id = decoded_form_token.get("object_id")
        object_type = decoded_form_token.get("object_type")
        staff_id = decoded_form_token.get("staff_id")

        customer_info = None
        if object_type == ConstantQuickSales.ObjectType.PROFILE:
            customer_info = ProfilingHelper().get_information_profile_by_profile_ids(
                merchant_id, [object_id], ["primary_phone"]
            )
        if not customer_info:
            raise DBLogicError("Thông tin khách hàng không tồn tại!")
        customer_info = customer_info[0]
        MobioLogging().info("FormBuilderHelper:get_detail_form_builder_by_form_token:profile_info: %s" % customer_info)

        primary_phone = customer_info.get("primary_phone")
        if not primary_phone:
            raise DBLogicError("Số điện thoại của khách hàng không tồn tại!")
        phone_number = primary_phone.get("phone_number")
        if not phone_number:
            raise DBLogicError("Số điện thoại của khách hàng không tồn tại!")
        return ConstantQuickSales.StatusFormBuilder.ACTIVE, {
            "merchant_id": merchant_id,
            "phone_number": phone_number,
            "profile_name": "",
            "form_id": form_id,
            "profile_id": object_id,
            "staff_id": staff_id,
        }

    def update_status_form_landing_page_ekyc_profile(self, merchant_id, form_id, status, profile_id, reason=None):
        try:
            token = InternalAdminHelper().get_basic_token()
            body = {
                "form_id": form_id,
                "status": status,
                "object_id": profile_id,
                "object_type": ConstantQuickSales.ObjectType.PROFILE,
                "reason": reason,
            }

            header = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}

            wfb_host = InternalAdminHelper().get_internal_host_by_merchant(
                merchant_id=merchant_id, key=KeyHostService.FORM_BUILDER_APP_INTERNAL_API_SERVICE_HOST
            )
            if not wfb_host:
                return None

            url = URI_INTERNAL.FORM_BUILDER.UPDATE_STATUS_FORM_LANDING_PAGE_EKYC
            url = url.format(domain=wfb_host)
            MobioLogging().info("FormBuilderHelper:update_status_form_landing_page_ekyc_profile:BODY: %s" % body)
            MobioLogging().info("FormBuilderHelper:update_status_form_landing_page_ekyc_profile:URL: %s" % url)
            r = RequestRetryAdapter().retry_a_post_request(url, headers=header, payload=body)
            if r:
                r = r.json()
                return r

            return {}
        except Exception as e:
            MobioLogging().error("FormBuilderHelper:update_status_form_landing_page_ekyc_profile:Error: {}".format(e))
            return None

    def get_form_data_submit(self, merchant_id, form_id, profile_id, field_configs):
        """
        Lấy dữ liệu submit từ form builder
        """
        wfb_host = InternalAdminHelper().get_internal_host_by_merchant(
            merchant_id="mobio", key=KeyHostService.FORM_BUILDER_APP_INTERNAL_API_SERVICE_HOST
        )
        if not wfb_host:
            return None
        url = URI_INTERNAL.FORM_BUILDER.GET_DATA_FORM_SUBMIT_BY_FORM_ID
        url = url.format(domain=wfb_host)
        body_request = {
            "form_id": form_id,
            "object_id": profile_id,
            "object_type": ConstantQuickSales.ObjectType.PROFILE,
            "filter_module": {
                # ConstantQuickSales.ObjectType.PROFILE: [],
                # ConstantQuickSales.ObjectType.WEB_FORM: [],
                
            },
        }
        MobioLogging().info("FormBuilderHelper:get_form_data_submit:BODY: %s" % body_request)
        header = {
            "Content-Type": "application/json",
            "Authorization": InternalAdminHelper().get_basic_token(),
            "X-Merchant-Id": merchant_id,
        }

        r = RequestRetryAdapter().retry_a_post_request(url, headers=header, payload=body_request)

        if r:
            r = r.json()
            data = r.get("data")
            field_config = r.get("field_config")
            if field_config and data and data.get("web_form"):
                data["web_form"]["field_config_web_form"] = field_config
            return data
        return {}


if __name__ == "__main__":
    print(
        FormBuilderHelper().get_form_data_submit(
            merchant_id="57d559c1-39a1-4cee-b024-b953428b5ac8",
            form_id="67c13217fc018eabed5a9b7a",
            profile_id="925b0963-9242-4bfa-a40e-5ff7b4a4d961",
            field_configs=[],
        )
    )
