#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 20/02/2025
"""

from mobio.libs.logging import MobioLogging

from src.apis.uri import URI_INTERNAL
from src.common.key_host import KeyHostService
from src.common.requests_retry import RequestRetryAdapter
from src.helpers.internal.mobio.admin import InternalAdminHelper


class ProfilingHelper:
    def get_information_profile_by_profile_ids(self, merchant_id, profile_ids, fields):
        try:
            token = InternalAdminHelper().get_basic_token()
            body = {
                "profile_ids": profile_ids,
                "fields": fields,
            }

            header = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}

            profiling_host = InternalAdminHelper().get_internal_host_by_merchant(
                merchant_id=merchant_id, key=KeyHostService.PROFILING_V4_APP_INTERNAL_API_SERVICE_HOST
            )
            if not profiling_host:
                return None

            url = URI_INTERNAL.PROFILING.PROFILING_LIST_PROFILE_BY_ID
            url = url.format(host=profiling_host, merchant_id=merchant_id)
            MobioLogging().info("ProfilingHelper:get_information_profile_by_profile_ids:BODY: %s" % body)
            MobioLogging().info("ProfilingHelper:get_information_profile_by_profile_ids:URL: %s" % url)
            r = RequestRetryAdapter().retry_a_post_request(url, headers=header, payload=body)
            result = []
            if r:
                r = r.json()
                result = r.get("data")

            return result
        except Exception as e:
            MobioLogging().error("ProfilingHelper:get_information_profile_by_profile_ids:Error: %s" % e)
            return None

    @staticmethod
    def update_cif_profile_by_profile_id(merchant_id, profile_id, cif):
        try:
            token = InternalAdminHelper().get_basic_token()
            header = {"Content-Type": "application/json", "Authorization": token, "X-Merchant-Id": merchant_id}

            url = URI_INTERNAL.PROFILING.PROFILING_UPDATE_CIF_PROFILE_BY_PROFILE_ID
            profiling_host = InternalAdminHelper().get_internal_host_by_merchant(
                merchant_id=merchant_id, key=KeyHostService.PROFILING_INTERNAL_DATAFLOW_HOST
            )
            if not profiling_host:
                return None
            url = url.format(host=profiling_host)
            body = {
                "profile_data": {"profile_id": profile_id, "cif": cif},
                "module": "MOBILE_BACKEND",
                "profile_connector_config": {
                    "id": "BACKEND_MOBILE_EKYC",
                    "merchant_id": merchant_id,
                    "name": "profiles",
                    "fields_verify": [],
                    "is_trust": True,
                    "fields_replace": [],
                    "fields_append": ["cif"],
                    "fields_replace_ignore_empty": [],
                    "unification_rules": {
                        "operators": [
                            {
                                "priority": 1,
                                "fields": {"profile_id": {"match_type": "exact", "normalized_type": "string"}},
                            }
                        ]
                    },
                    "data_recording_rules": {
                        "operators": [
                            {
                                "priority": 1,
                                "fields": {"profile_id": {"match_type": "exact", "normalized_type": "string"}},
                            }
                        ]
                    },
                },
                "connector_id": "BACKEND_MOBILE_EKYC",
                "merchant_id": merchant_id,
            }
            MobioLogging().info("ProfilingHelper:update_cif_profile_by_profile_id:BODY: %s" % body)
            r = RequestRetryAdapter().retry_a_post_request(url, headers=header, payload=body)
            return r
        except Exception as e:
            MobioLogging().error("ProfilingHelper:update_cif_profile_by_profile_id:Error: %s" % e)
            return None


if __name__ == "__main__":
    print(
        ProfilingHelper().get_information_profile_by_profile_ids(
            "57d559c1-39a1-4cee-b024-b953428b5ac8",
            ["aa6c36ef-2b68-453c-a516-f88b39d9a9ce"],
            ["primary_phone"],
        )
    )
