#!/usr/bin/python
# -*- coding: utf8 -*-
import datetime
import json
import uuid

from bson import ObjectId


class MobioJSONEncoder(json.JSONEncoder):
    def default(self, o):
        if type(o) == datetime.date:
            a = datetime.datetime.combine(o, datetime.datetime.min.time())
            return a.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        elif type(o) == datetime.datetime:
            return o.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        elif isinstance(o, uuid.UUID):
            return str(o)
        elif isinstance(o, (bytes, bytearray)):
            return str(o, "utf-8")
        return json.JSONEncoder.default(self, o)


class MobioJSONEncoderV2(json.JSONEncoder):
    def __init__(self, format_time="%Y-%m-%dT%H:%M:%S.000Z"):
        super().__init__()
        self.format_time = format_time

    def default(self, o):
        if isinstance(o, ObjectId):
            return str(o)
        elif type(o) == datetime.date:
            a = datetime.datetime.combine(o, datetime.datetime.min.time())
            return a.strftime(self.format_time)
        elif type(o) == datetime.datetime:
            return o.strftime(self.format_time)
        elif isinstance(o, uuid.UUID):
            return str(o)
        elif isinstance(o, (bytes, bytearray)):
            return str(o, "utf-8")
        return json.JSONEncoder.default(self, o)

    def encode(self, o):
        if '_id' in o:
            o['id'] = str(o['_id'])
        return json.JSONEncoder.encode(self, o)

    def json_loads(self, o):
        return json.loads(self.encode(o))
