#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 11/05/2024
"""

from mobio.libs.caching import LruCache, StoreType

from configs import RedisConfig
from src.common import APP_CONFIG_FILE_PATH


lru_redis_cache = LruCache(
    store_type=StoreType.REDIS,
    config_file_name=APP_CONFIG_FILE_PATH,
    redis_uri=RedisConfig.REDIS_URI,
    redis_cluster_uri=RedisConfig.REDIS_BASE_URI,
    cache_prefix=RedisConfig.CACHE_PREFIX,
    redis_type=RedisConfig.REDIS_BASE_TYPE
)