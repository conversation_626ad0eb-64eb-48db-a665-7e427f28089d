#!/usr/bin/env python
# -*- coding: utf-8 -*-


import os

from configs import MobileBackendApplicationConfig
from src.common.i_base_common import IBaseConfig

WORKING_DIR = MobileBackendApplicationConfig.WORKING_DIR


APP_CONFIG_FILE_PATH = os.path.join(MobileBackendApplicationConfig.CONFIG_DIR, "mobile_backend.conf")
SHARE_FOLDER_STATIC = WORKING_DIR + "/static/"
SHARE_FOLDER_EVIDENT_STATIC = os.path.join(SHARE_FOLDER_STATIC, "evident")
SHARE_FOLDER_EXPORT_STATIC = os.path.join(SHARE_FOLDER_STATIC, "export_excel")

MOBILE_BACKEND_DATA_DIR = os.path.join(MobileBackendApplicationConfig.APPLICATION_DATA_DIR, "mobile_backend")
os.makedirs(MOBILE_BACKEND_DATA_DIR, exist_ok=True)

SHARE_FOLDER_IMAGE_VERIFY = os.path.join(MO<PERSON><PERSON>_BACKEND_DATA_DIR, "image_verify")
os.makedirs(SHARE_FOLDER_STATIC, exist_ok=True)
os.makedirs(SHARE_FOLDER_EVIDENT_STATIC, exist_ok=True)
os.makedirs(SHARE_FOLDER_EXPORT_STATIC, exist_ok=True)
os.makedirs(SHARE_FOLDER_IMAGE_VERIFY, exist_ok=True)


class LANG:
    KEYS = "keys"
    DEFAULT = "default"
    CUSTOM_ERROR = "custom_error"
    BAD_REQUEST = "bad_request"
    UNAUTHORIZED = "unauthorized"
    NOT_ALLOWED = "not_allowed"
    NOT_FOUND = "not_found"
    INTERNAL_SERVER_ERROR = "internal_server_error"
    VALIDATE_ERROR = "validate_error"
    LANG_ERROR = "lang_error"
    MUST_NOT_EMPTY = "must_not_empty"
    NOT_EXIST = "not_exist"
    ALREADY_EXIST = "already_exist"
    MESSAGE_SUCCESS = "message_success"
    MERCHANT_NOT_EXIST = "merchant_not_exist"
    NOT_PERMISSION_UPDATE_TASK = "not_permission_update_task"
    LOG_ID_NOT_EXIST = "log_id_not_exist"


class SecretKey:
    VALUE = b"t3UrsMhLBXPf@cGuC7Q6CcBaHE5nuSmq"


class KeyConfigNotifySDK:
    MOBILEBACKEND_PROFILE_REPLY_SMS_CONFIRM_CONSENT = (
        "mobilebackend_profile_reply_sms_confirm_consent"  # profile trả lời tin nhắn confirm consent
    )

    MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID = "mobilebackend_successful_decoded_citizen_id"
    MOBILEBACKEND_CUSTOMER_FACE_AUTHENTICATION = "mobilebackend_customer_face_authentication"
    MOBILEBACKEND_CUSTOMER_VERIFY_IMAGE = "mobilebackend_customer_verify_image"
    MOBILEBACKEND_EXPORT_REPORT_CIC = "mobilebackend_export_report_cic"

    MOBILEBACKEND_RESULT_QUICK_SALES = "mobilebackend_result_quick_sales"


class StatusCode:
    THIRD_PARTY_FAILURE = "third_party_failure"
    SUCCESS = "success"


class StatusCodePushSocket:
    FAIL = "fail"
    SUCCESS = "success"


class StateNFC:
    CHECK_FACE = "check_face"
    VERIFY_IMAGE = "verify_image"


class ThirdPartyType(IBaseConfig):
    SEND_SMS_CONFIRM_CONSENT = "send_sms_confirm_consent"
    SEND_REQUEST_CHECK_CARD = "send_request_check_card"
    SEND_REQUEST_READ_CARD = "send_request_read_card"
    SEND_REQUEST_CHECK_FACE = "send_request_check_face"
    SEND_REQUEST_CHECK_CUSTOMER_EXIST = "send_request_check_customer_exist"
    SEND_REQUEST_SAVE_CUSTOMER = "send_request_save_customer"
    SEND_REQUEST_INSERT_ID_CHECK = "send_request_insert_id_check"
    SEND_REQUEST_ID_CHECK_INSERT_DATA_LOG = "send_request_id_check_insert_data"
    SEND_REQUEST_CHECK_UNIQUE_ID = "send_request_check_unique_id"
    SEND_REQUEST_VERIFY_IMAGE = "send_request_verify_image"

    # LOS
    SEND_REQUEST_LOS_AUTHENTICATE = "send_request_los_authenticate"
    SEND_REQUEST_LOS_PULL_DANH_SACH = "send_request_los_pull_danh_sach"
    SEND_REQUEST_LOS_PULL_TRANG_THAI = "send_request_los_trang_thai"
    CONFIG_CUSTOM_INFORMATION_SEND_HPT = "config_custom_information_send_hpt"

    # CIC
    SEND_REQUEST_CHECK_CUSTOMER_CIC = "send_request_check_customer_cic"
    SEND_REQUEST_INQUIRY_PRODUCT_CIC = "send_request_inquiry_product_cic"
    SEND_REQUEST_SEARCH_HISTORY_CIC = "send_request_search_history_cic"
    SEND_REQUEST_GET_FILE_REPORT_CIC = "send_request_get_file_report_cic"
    CONFIG_CUSTOM_INFORMATION_SEND_CICH2H = "config_custom_information_send_cich2h"

    # ECM
    SEND_REQUEST_ECM_CIC_AUTHENTICATE = "send_request_ecm_cic_authenticate"
    SEND_REQUEST_ECM_CIC_DETAIL_FILE = "send_request_ecm_cic_detail_file"
    CONFIG_CUSTOM_INFORMATION_SEND_ECM = "config_custom_information_send_ecm"

    RESPONSE_SMS_REPLY_CONFIRM_CONSENT = "response_sms_reply_confirm_consent"

    # Quick Sales
    SEND_REQUEST_QUICK_SALES_LOCK_UNLOCK_ACCOUNT = "send_request_quick_sales_lock_unlock_account"
    SEND_REQUEST_QUICK_SALES_SEND_OTP_CONFIRM_LANDING_PAGE = "send_request_quick_sales_send_otp_confirm_landing_page"
    SEND_REQUEST_QUICK_SALES_CREATE_CIF = "send_request_quick_sales_create_cif"
    SEND_REQUEST_QUICK_SALES_RES_ACCOUNT_EDIGI = "send_request_quick_sales_res_account_edigi"
    SEND_REQUEST_QUICK_SALES_INSERT_IN4_DSA = "send_request_quick_sales_insert_in4_dsa"
    SEND_REQUEST_QUICK_SALES_CHECK_AML = "send_request_quick_sales_check_aml"
    SEND_REQUEST_QUICK_SALES_CA_ACCT_ADD_INDIV = "send_request_quick_sales_ca_acct_add_indiv"

    SEND_REQUEST_QUICK_SALES_CHECK_SO_DEP = "send_request_quick_sales_check_so_dep"
    SEND_REQUEST_QUICK_SALES_GET_LOAI_DETAILS = "send_request_quick_sales_get_loai_details"
    SEND_REQUEST_QUICK_SALES_GET_DANH_SACH_SO_DEP = "send_request_quick_sales_get_danh_sach_so_dep"

    CONFIG_PARAM_QUICK_SALES_PREMIUM_ACCOUNT = "config_param_quick_sales_premium_account"

    SEND_REQUEST_QUICK_SALES_SEND_EMAIL = "send_request_quick_sales_send_email"
    SEND_REQUEST_QUICK_SALES_GET_IN4_DSA = "send_request_quick_sales_get_in4_dsa"
    

    # Exchange Rate
    SEND_REQUEST_EXCHANGE_RATE = "send_request_exchange_rate"


class ConstantKeyConfigSendThirdParty:
    FORMAT_SMS = "format_sms"
    CLIENT_ID = "client_id"
    CHANNEL = "channel"
    PEM_PASS_PHRASE = "pem_pass_phrase"
    SMS_ACCESS_TOKEN = "sms_access_token"
    SMS_LANDINGPAGE_ENCRYPTION_KEY = "sms_landingpage_encryption_key"
    KEY_FILE = "key_file"
    PLAINTEXT = "plaintext"
    DATA_ENCRYPTED = "data_encrypted"
    CERTIFICATES_PATH = "certificates_path"
    VNPAY_CERTIFICATES_PATH = "vnpay_certificates_path"
    DATA_ENCRYPTED_SET_OTP_ENCRYPTED = "data_encrypted_set_otp_encrypted"
    FORMAT_SMS_SET_OTP_ENCRYPTED = "format_sms_set_otp_encrypted"

    LIMIT_NUMBER_VERIFY_IMAGE = "limit_number_verify_image"

    AUTH_NAME = "auth_name"
    AUTH_PASS = "auth_pass"
    X_CLIENT_ID = "x_client_id"
    URI = "uri"

    VALUE_MATCHING_FACE = "value_matching_face"
    MAPPING_CODE_MESSAGE_ERROR = "mapping_code_message_error"
    MAPPING_CODE_MESSAGE_SUCCESS = "mapping_code_message_success"
    METHOD_REQUEST_LOG = "method_request_log"
    SECRET_KEY = "secret_key"

    TIMEOUT_API = "timeout_api"

    JWT_KEY = "jwt_key"
    JWT_SECRET = "jwt_secret"
    JWT_AUDIENCE = "jwt_audience"

    DIGEST_ALG = "digest_alg"

    NUMBER_RETRY = "number_retry"
    HMAC_SECRET = "hmac_secret"
    MESSAGE_ERROR_WHEN_FAIL = "message_error_when_fail"


class ConstantStatusConfig:
    ENABLE = 1
    DISABLED = 0


class NOTIFICATION_STRUCTURE:
    TO = "to"
    BODY = "body"


class NOTIFICATION_TO_STRUCTURE:
    BODY = "body"
    PHONE_NUMBER = "phone_number"


class NOTIFICATION_BODY_STRUCTURE:
    CONTENT = "content"
    DATA_REQUEST = "data_request"

    CARD_DATA_ENCRYPTED = "card_data_encrypted"
    CARD_REQUEST_SESSION = "card_request_session"
    CARD_REQUEST_ROUND = "card_request_round"
    CARD_DATA_REQUEST = "card_data_request"
    CARD_TIME = "card_data_request"

    REQUEST_TIMESTAMP = "requestTimeStamp"


class ConstantMessageSendRequestToThirdParty:
    MERCHANT_ID = "merchant_id"
    REQUEST_BODY = "request_body"
    ACCOUNT_ID = "account_id"
    ACTION_TIME = "action_time"
    REQUEST_TYPE = "request_type"
    LOG_ID = "log_id"
    LOG_ID_START = "log_id_start"
    FLOW_NAME = "flow_name"


class ConstantStatusRequestSmsConsent:
    INIT = "init"
    FAIL = "fail"
    DONE = "done"


class ConstantNumberMaxRequestSmsConsentInRangeTime:
    MAX_REQUEST_SMS_CONSENT_IN_RANGE_TIME = 5


class ConstantValueConfirmTrackingConsent:
    YES = "yes"


class Constant:
    MAX_TIME_REPLY_SMS_CONFIRM_CONSENT_SEC = 180  # Đặt thời gian tối đa là 3p (đơn vị được tính bằng giây)


class ConstantAttachmentType:
    AVATAR = "avatar"
    EVIDENT_CONSENT = "evident_consent"


class ConstantNameFlow(IBaseConfig):
    TELLER_APP = "teller_app"
    CBBH_ADD_PROFILE = "cbbh_add_profile"
    QUICK_SALES = "quick_sales"
    PRE_APPROVED = "pre_approved"


class TypeConfigLos(IBaseConfig):
    STATUS = "status"
    CONFIG_FIELD = "config_field"


class TypeConfigCic(IBaseConfig):
    STATUS = "status"
    PRODUCTS = "products"


class CustomerType:
    KHCN = "KHCN"
    KHDN = "KHDN"
    CN = "CN"
    DN = "DN"

    ALL = [KHCN, KHDN, CN, DN]


class ConstantInitializationTime:
    TYPE = "type"
    START_TIME = "start_time"
    END_TIME = "end_time"

    class ValueType:
        NUMBER_31_DAY_AGO = "31_day_ago"
        CUSTOM_TIME = "custom_time"


class ConstantCicAreaCode(IBaseConfig):
    HOME = "HOME"
    DETAIL_PROFILE = "DETAIL_PROFILE"
    DETAIL_COMPANY = "DETAIL_COMPANY"


class ConstantCicReportType(IBaseConfig):
    USER = "user"
    NUMBER_OF_SEARCHES = "number_of_searches"  # Số lượt tra cứu


class ConstantQuickSales(IBaseConfig):

    class ObjectType(IBaseConfig):
        PROFILE = "profile"
        COMPANY = "company"
        WEB_FORM = "web_form"

    class ConstantActionAccount(IBaseConfig):
        LOCK = "lock"
        UNLOCK = "unlock"

        MAPPING_ACTION_CRM_TO_QUICK_SALES = {
            LOCK: "A",
            UNLOCK: "U",
        }

    class ConstantStatusLogOtpConfirmLandingPage(IBaseConfig):

        MINUTE_EXPIRED = 15

        INIT = "init"
        VERIFY_OTP = "verify_otp"
        SUBMIT = "submit"
        EXPIRED = "expired"

    class StatusFormBuilder(IBaseConfig):
        ACTIVE = "active"
        INACTIVE = "inactive"

    class ConstantStatusSubmitLandingPage(IBaseConfig):
        CONFIRM = "confirm"
        CANCEL = "cancel"

    class ConfigSecretKey(IBaseConfig):
        SECRET_KEY_FORM_BUILDER = "Y3rKXXri_R34NcUlXK7I4hyEVsp_zaaFrqLIZR4gl84="

    class ConstantStepFlow(IBaseConfig):
        STEP_SEARCH_CIF = "step_search_cif"
        STEP_READ_CARD = "step_read_card"
        STEP_CHECK_CARD = "step_check_card"
        STEP_CHECK_FACE = "step_check_face"
        STEP_SUBMIT_FORM = "step_submit_form"
        STEP_HANDLE_CREATE_CIF = "step_create_cif"
        STEP_HANDLE_CREATE_ACCOUNT_INDIV = "step_create_account_indiv"
        STEP_HANDLE_REGISTER_EDIGI_ACCOUNT = "step_register_edigi_account"
        STEP_HANDLE_SEND_EMAIL_TO_PROFILE = "step_send_email_to_profile"
        STEP_HANDLE_UPSERT_CIF_TO_PROFILE = "step_upsert_cif_to_profile"
        STEP_HANDLE_CREATE_DEAL = "step_create_deal"
        STEP_HANDLE_INSERT_IN4_DSA = "step_insert_in4_dsa"
        STEP_HANDLE_UPDATE_DATA_TO_CRM = "step_update_data_to_crm"
        STEP_END_FLOW = "step_end_flow"
        STEP_PUSH_NOTIFY_TO_ACCOUNT = "step_push_notify_to_account"

        STEP_UPDATE_STATUS_FORM_BUILDER = "step_update_status_form_builder"

    class ConstantTypeHandleLogic(IBaseConfig):
        QUEUE_KAFKA = "queue_kafka"
        API = "api"

    class ConstantStatusStepFlow(IBaseConfig):
        SUCCESS = "success"
        FAIL = "fail"

    class ConstantStatusFormBuilder(IBaseConfig):
        SUCCESS = "success"
        LANDING_PAGE_CANCEL = "landing_page_cancel"
        CIF_CREATE_ERROR = "cif_create_error"
        ACCOUNT_CREATE_ERROR = "account_create_error"
        LANDING_PAGE_ACCEPT = "landing_page_accept"
        EDIGI_CREATE_ERROR = "edigi_create_error"
        FAIL = "fail"

    class ConstantStatusFlowQuickSales(IBaseConfig):
        INIT = "init"
        PROCESSING = "processing"
        SUCCESS = "success"
        FAIL = "fail"

    class ConstantStatusStepFlowQuickSales(IBaseConfig):
        SUCCESS = "success"
        FAIL = "fail"


class ConstantKeyConfigMappingFieldCrmEib(IBaseConfig):
    API_SEND_REQUEST_CREATE_CIF = "api_send_request_create_cif"
    MAPPING_ADDRESS_CATEGORY = "mapping_address_category"
    API_SEND_REQUEST_CREATE_ACCOUNT_EDIGI = "api_send_request_create_account_edigi"
    API_SEND_REQUEST_CREATE_CA_ACCT_ADD_INDIV = "api_send_request_create_ca_acct_add_indiv"
    API_SEND_REQUEST_SAVE_CUSTOMER_TO_EDIGI = "api_send_request_save_customer_to_edigi"

    SEND_EMAIL_RES_ACCOUNT_EDIGI = "send_email_res_account_edigi"
    SEND_DYNAMIC_EVENT_TO_PROFILE = "send_dynamic_event_to_profile"
    LIST_FIELD_GET_FROM_CRM = "list_field_get_from_crm"

    SET_VALUE_DEFAULT = "set_value_default"
    INSERT_IN4_DSA = "insert_in4_dsa"


class ConstantConfigConvertDataQuickSales(IBaseConfig):
    class ConstantTypeTargetConvert(IBaseConfig):
        STRING = "string"
        DATE = "date"
        ARRAY_STRING = "array_string"
        ARRAY_INT = "array_int"

    class ConstantActionConvert(IBaseConfig):
        SPILT = "spilt"
