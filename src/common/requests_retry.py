#!/usr/bin/python
# -*- coding: utf8 -*-

import requests
from mobio.libs.logging import Mo<PERSON>Logging
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class RequestRetryAdapter(object):

    def __init__(self):
        self.logging = MobioLogging()

    @staticmethod
    def requests_retry_session(
        retries=3, backoff_factor=0.3, status_forcelist=(500, 501, 502, 504, 400, 401, 404), session=None
    ):
        session = session or requests.Session()
        retry = Retry(
            total=retries,
            read=retries,
            connect=retries,
            backoff_factor=backoff_factor,
            status_forcelist=status_forcelist,
        )
        adapter = HTTPAdapter(max_retries=retry)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    def retry_a_post_request(
        self,
        url,
        headers,
        payload,
        auth=None,
        params=None,
        data=None,
        files=None,
        times=3,
        timeout=10,
        raise_for_status=True,
        verify=None,
    ):
        try:
            response = self.requests_retry_session(retries=times).post(
                url,
                params=params,
                json=payload,
                headers=headers,
                timeout=timeout,
                data=data,
                files=files,
                auth=auth,
                verify=verify,
            )
            if raise_for_status:
                response.raise_for_status()

        except Exception as error:
            raise Exception(error)
        return response

    def retry_a_get_request(
        self,
        url,
        headers,
        params,
        auth=None,
        times=3,
        timeout=10,
        verify=None,
        stream=False,
    ):
        try:
            response = self.requests_retry_session(retries=times).get(
                url, params=params, headers=headers, timeout=timeout, auth=auth, verify=verify, stream=stream
            )
            response.raise_for_status()

        except Exception as error:
            self.logging.error(
                "RequestRetryAdapter::retry_a_get_request: Request Info: {} ==== Error: {}".format(params, error)
            )
            return None
        return response

    def retry_a_delete_request(self, url, headers, params=None, times=3):
        try:
            response = self.requests_retry_session(retries=times).delete(url, params=params, headers=headers)
            response.raise_for_status()

        except Exception as error:
            self.logging.error(
                "RequestRetryAdapter::retry_a_delete_request: Request Info: {} ==== Error: {}".format(params, error)
            )
            return None
        return response

    def retry_a_put_request(self, url, headers, payload=None, times=3):
        try:
            response = self.requests_retry_session(retries=times).put(url, json=payload, headers=headers)
            response.raise_for_status()

        except Exception as error:
            self.logging.error(
                "RequestRetryAdapter::retry_a_put_request: Request Info: {} ==== Error: {}".format(payload, error)
            )
            return None
        return response
