#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 19/04/2024
"""
import inspect


class IBaseConfig:

    def __init__(self):
        self.result = {}

    @classmethod
    def get_all_attribute(cls):
        attributes = inspect.getmembers(cls, lambda a: not (inspect.isroutine(a)))
        values = [
            a[1]
            for a in attributes
            if not (a[0].startswith("__") and a[0].endswith("__"))
        ]
        return values

    def set_all_data(self, **kwargs):
        for key in self.get_all_attribute():
            if key in kwargs:
                self.result[key] = kwargs.get(key)
