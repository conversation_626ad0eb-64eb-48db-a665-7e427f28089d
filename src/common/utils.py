#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 16/04/2024
"""

import base64
import datetime
import re


class Base64(object):
    @staticmethod
    def encode(data):
        try:
            byte_string = data.encode("utf-8")
            encoded_data = base64.b64encode(byte_string)
            return encoded_data.decode(encoding="UTF-8")
        except Exception as ex:
            print("src/common/utils.py:Base64::encode():error: %s" % ex)
            return ""

    @staticmethod
    def decode(encoded_data):
        try:
            if isinstance(encoded_data, bytes):
                encoded_data = encoded_data.decode("UTF-8")
            decoded_data = base64.urlsafe_b64decode(encoded_data + "=" * (-len(encoded_data) % 4))
            return decoded_data.decode(encoding="UTF-8")
        except Exception as ex:
            print("src/common/utils.py:Base64::decode():error: %s" % ex)
            return ""


# def get_request_id():
#     url = request.url
#     request_time = datetime.utcnow()
#     identify = url + str(request_time)
#     identify = identify.encode("utf-8")
#     return hashlib.md5(identify).hexdigest()


patterns = {
    "[àáảãạăắằẵặẳâầấậẫẩ]": "a",
    "[đ]": "d",
    "[èéẻẽẹêềếểễệ]": "e",
    "[ìíỉĩị]": "i",
    "[òóỏõọôồốổỗộơờớởỡợ]": "o",
    "[ùúủũụưừứ<PERSON><PERSON>ự]": "u",
    "[ỳýỷỹỵ]": "y",
}


def utf8_to_ascii(text):
    if text is None:
        return ""
    output = text
    for regex, replace in patterns.items():
        output = re.sub(regex, replace, output)
        # deal with upper case
        output = re.sub(regex.upper(), replace.upper(), output)
    return output


def replace_newlines(s):
    return (
        s.replace("\\r\\n", "")
        .replace("\\r", "")
        .replace("\\n", "")
        .replace("\r\n", "")
        .replace("\r", "")
        .replace("\n", "")
    )


def get_datetime_utc_to_format_datetime():
    return datetime.datetime.now(datetime.UTC)
