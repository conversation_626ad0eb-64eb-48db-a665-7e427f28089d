#!/usr/bin/env python
# -*- coding: utf-8 -*-
from flask import request
from mobio.sdks.admin import MobioAdminSDK
from mobio.sdks.base.common.mobio_exception import CustomError
from mobio.sdks.base.controllers import LANG_VI, LangConfig

from src.common import LANG


def get_param_value_temp(param_name):
    return MobioAdminSDK().get_value_from_token(param_name)


def get_merchant_header():
    language = request.args.get("lang", LANG_VI)
    merchant_id = request.headers.get("X-Merchant-Id")
    if not merchant_id:
        raise CustomError(LangConfig().lang_map(language).get(LANG.MERCHANT_NOT_EXIST).get("message"))

    return merchant_id
