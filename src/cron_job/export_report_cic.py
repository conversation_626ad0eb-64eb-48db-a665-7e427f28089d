#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 28/12/2024
"""

import datetime
import os
from uuid import uuid4

import xlsxwriter
from mobio.libs.logging import MobioLogging
from mobio.sdks.media.mobio_media_sdk import MobioMediaSDK
from mobio.sdks.notify import MobioNotifySD<PERSON>

from configs import MobileBackendApplicationConfig, RedisConfig
from src.common import SHARE_FOLDER_EXPORT_STATIC
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.report_cic_model import ReportCicModel
from src.models.mongo.request_export_report_cic_model import RequestExportReportCicModel

MobioMediaSDK().config(admin_host=MobileBackendApplicationConfig.ADMIN_HOST, cache_prefix=RedisConfig.CACHE_PREFIX)
MobioNotifySDK().config(source="mobile-backend")


def export_to_file_by_filter(merchant_id, filter_query, filename, account_request_id):
    start_time = filter_query.get("start_time")
    end_time = filter_query.get("end_time")
    area_codes = filter_query.get("area_codes")
    sol_ids = filter_query.get("sol_ids")
    area_names = []
    sol_names = []
    lst_account_lower_levels = InternalAdminHelper().get_list_account_by_level_account_current(
        merchant_id, account_request_id
    )

    try:
        start_time = datetime.datetime.strptime(start_time, "%Y-%m-%dT%H:%MZ")
    except Exception as ex:
        raise Exception("Format time error!!!")
    try:
        end_time = datetime.datetime.strptime(end_time, "%Y-%m-%dT%H:%MZ")
    except Exception as ex:
        raise Exception("Format time error!!!")

    filter_option = {
        "merchant_id": merchant_id,
        "$and": [
            {
                "action_time": {"$gte": start_time},
            },
            {"action_time": {"$lte": end_time}},
        ],
    }
    if lst_account_lower_levels:
        filter_option.update({"account_id": {"$in": lst_account_lower_levels}})
    if area_codes:
        filter_option.update({"area_code": {"$in": area_codes}})
    if sol_ids:
        filter_option.update({"sol_id": {"$in": sol_ids}})
    after_token = None
    data_exports = []
    while True:
        reports, after_token = ReportCicModel().find_paginate_load_more(filter_option, 100, after_token)
        for report in reports:
            area_name = report.get("area_name")
            sol_name = report.get("sol_name")
            if area_name and area_name not in area_names:
                area_names.append(area_name)
            if sol_name and sol_name not in sol_names:
                sol_names.append(sol_name)
            data_exports.append(
                {
                    "date": report.get("action_time_day"),
                    "fullname": report.get("fullname"),
                    "staff_code": report.get("staff_code"),
                    "position_name": report.get("position_name"),
                    "sol_name": report.get("sol_name"),
                    "sol_id": report.get("sol_id"),
                    "area_name": report.get("area_name"),
                    "product": "{} & {}".format(report.get("product_code"), report.get("product_name")),
                    "status": "Thành công" if report.get("status") == "success" else "Thất bại",
                }
            )
        if not after_token:
            break
    if data_exports:
        start_index = 1
        mapping_column_excel = {
            "date": "Ngày tra cứu",
            "fullname": "Tên người tra cứu",
            "staff_code": "Mã nhân viên",
            "position_name": "Tên chức danh",
            "sol_name": "Tên ĐVKD của user",
            "sol_id": "Mã ĐVKD/SOLID",
            "area_name": "Khu vực",
            "product": "Tên sản phẩm tra cứu",
            "status": "Trạng thái tra cứu",
        }
        file_path = os.path.join(SHARE_FOLDER_EXPORT_STATIC, filename)

        workbook = xlsxwriter.Workbook(file_path, {"constant_memory": True})
        worksheet = workbook.add_worksheet()
        for col_num, header in enumerate(list(mapping_column_excel.values())):
            worksheet.write(0, col_num, header)
        for row in data_exports:
            worksheet.write_row(start_index, 0, row.values())
            start_index += 1
        workbook.close()
        if area_names:
            area_names = ",".join(list(set(area_names)))
        if sol_names:
            sol_names = ",".join(list(set(sol_names)))

        return file_path, area_names, sol_names
    return "", "", ""


def export_report_cic():
    MobioLogging().info("Start export_report_cic")
    list_request = RequestExportReportCicModel().find({"status": "request"}).sort({"request_time": 1})
    for item in list_request:
        MobioLogging().info("Start process item :: {}".format(item))
        item_id = item.get("_id")
        MobioLogging().info("Update status processing to item :: {}".format(item_id))
        RequestExportReportCicModel().update_by_set(
            {"_id": item_id}, {"status": "processing", "action_processing_time": datetime.datetime.now(datetime.UTC)}
        )
        account_request_id = item.get("account_request_id")
        merchant_id = item.get("merchant_id")
        send_emails = item.get("emails")
        filename = "RequestExportCic_{}.xlsx".format(str(uuid4()))

        item_filter = item.get("filter")

        area_codes = item_filter.get("area_codes")
        sol_ids = item_filter.get("sol_ids")
        start_time = item_filter.get("start_time")
        end_time = item_filter.get("end_time")
        try:
            filepath, area_names, sol_names = export_to_file_by_filter(merchant_id, item.get("filter"), filename, account_request_id)
        except Exception as ex:
            MobioLogging().error("Error :: export_to_file_by_filter :: message :: {}".format(str(ex)))
            RequestExportReportCicModel().update_by_set(
                {"_id": item_id},
                {"status": "fail", "message": str(ex), "action_processed_time": datetime.datetime.now(datetime.UTC)},
            )
            continue
        try:
            start_time = datetime.datetime.strptime(start_time, "%Y-%m-%dT%H:%MZ")
            start_time += datetime.timedelta(hours=7)
            start_time = start_time.strftime("%d/%m/%Y")
        except Exception as ex:
            raise Exception("Format time error!!!")
        try:
            end_time = datetime.datetime.strptime(end_time, "%Y-%m-%dT%H:%MZ")
            end_time += datetime.timedelta(hours=7)
            end_time = end_time.strftime("%d/%m/%Y")
        except Exception as ex:
            raise Exception("Format time error!!!")

        export_time = "{} - {}".format(start_time, end_time)
        if not area_codes:
            area_names = "Tất cả"
        if not sol_ids:
            sol_names = "Tất cả"

        info_upload = MobioMediaSDK().upload_without_kafka(
            merchant_id=merchant_id,
            file_path=filepath,
            filename=filename,
            do_not_delete=True,
        )
        MobioLogging().debug("upload_file :: info_upload %s " % info_upload)
        url_file = info_upload.get("url")
        MobioNotifySDK().send_message_notify_email(
            merchant_id=merchant_id,
            key_config="mb_cic_report_file_export_result",
            account_ids=[],
            other_emails=send_emails,
            **{"export_time": export_time, "area": area_names, "business_unit": sol_names},
            url_file=url_file
        )
        RequestExportReportCicModel().update_by_set(
            {"_id": item_id},
            {
                "status": "done",
                "result_export": info_upload,
                "action_processed_time": datetime.datetime.now(datetime.UTC),
            },
        )


if __name__ == "__main__":
    export_report_cic()
