#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 17/04/2024
"""


import argparse
import datetime
import os

from src.common import ConstantStatusConfig, ThirdPartyType
from src.common.init_lib import lru_redis_cache
from src.models.mongo.config_info_api_model import ConfigInfoApiModel

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-merchant_id", "--merchant_id", help="Merchant ID", required=True)
    parser.add_argument("-domain", "--domain", help="Domain", required=True)
    parser.add_argument("-auth_name", "--auth_name", help="Auth name", default=None)
    parser.add_argument("-auth_pass", "--auth_pass", help="Auth name", default=None)
    parser.add_argument("-pem_pass_phrase", "--pem_pass_phrase", help="Pem pass phrase", default=None, required=True)
    parser.add_argument("-device_type", "--device_type", help="Device Type", default=None, required=True)
    parser.add_argument("-channel", "--channel", help="Channel", default=None, required=True)
    parser.add_argument("-sms_access_token", "--sms_access_token", help="Sms Access Token", default="")
    parser.add_argument(
        "-value_matching_face",
        "--value_matching_face",
        help="Value matching face",
        default=80,
        required=True,
        type=float,
    )
    parser.add_argument(
        "-certificates_path", "--certificates_path", help="Path certificates", default=None, required=True
    )
    parser.add_argument(
        "-vnpay_certificates_path",
        "--vnpay_certificates_path",
        help="Path VNPAY certificates",
        default=None,
        required=True,
    )
    parser.add_argument(
        "-secret_key",
        "--secret_key",
        help="Secret key giải mã SMS",
        default="t3UrsMhLBXPf@cGuC7Q6CcBaHE5nuSmq",
    )
    parser.add_argument(
        "-sms_brand_name_receiver",
        "--sms_brand_name_receiver",
        help="Brand name receiver",
        default="8142",
    )
    parser.add_argument(
        "-sms_brand_name_send",
        "--sms_brand_name_send",
        help="Brand name send",
        default="8142",
    )
    parser.add_argument(
        "-x_client_id",
        "--x_client_id",
        help="X-Client ID",
        default="54f2ba79-1ba0-4196-8245-fd3b8932bce1",
    )
    parser.add_argument(
        "-digest_alg",
        "--digest_alg",
        help="Digest algorithm",
        default="sha256",
    )
    parser.add_argument("-timeout_api", "--timeout_api", help="Thời gian timeout API", default=30, type=int)
    parser.add_argument("-jwt_key", "--jwt_key", help="JWT KEY", required=True)
    parser.add_argument("-jwt_secret", "--jwt_secret", help="JWT Secret", required=True)
    parser.add_argument("-jwt_audience", "--jwt_audience", help="JWT Audience", required=True)

    args = parser.parse_args()

    merchant_id = args.merchant_id
    domain = args.domain
    auth_name = args.auth_name
    auth_pass = args.auth_pass
    certificates_path = args.certificates_path
    pem_pass_phrase = args.pem_pass_phrase
    value_matching_face = args.value_matching_face
    device_type = args.device_type
    channel = args.channel
    secret_key = args.secret_key
    sms_access_token = args.sms_access_token
    sms_brand_name_receiver = args.sms_brand_name_receiver
    sms_brand_name_send = args.sms_brand_name_send
    timeout_api = args.timeout_api
    jwt_key = args.jwt_key
    jwt_secret = args.jwt_secret
    jwt_audience = args.jwt_audience
    x_client_id = args.x_client_id
    digest_alg = args.digest_alg
    vnpay_certificates_path = args.vnpay_certificates_path

    if os.getenv("VM") == "local":
        domain = "http://localhost"
        certificates_path = "/Volumes/mobio/eibmobilebackend/Privatekey.p12"

    config_mapping_code_message_error_default = {
        "500": "Lỗi không thể tạo chữ ký",
        "1001": "Chữ ký số không hợp lệ",
        "1006": "Lỗi giải mã dữ liệu",
        "2001": "License đã hết hạn",
        "2002": "Không tìm thấy License",
        "2003": "License đã bị khoá",
        "2004": "License không hợp lệ",
        "2005": "Dịch vụ không được đăng ký sử dụng",
        "2006": "License quá giới hạn request",
        "2008": "License đã hết số lượt sử dụng",
        "1002": "Sai định dạng bản tin",
        "1028": "Lỗi dịch vụ",
        "1030": "Lỗi không xác định",
        "1040": "Dữ liệu trống",
        "1088": "Lỗi dữ liệu",
        "908": "Thiếu thông tin header",
        "909": "Dữ liệu mã hoá thiếu hoặc sai trường thông tin",
        "5002": "Đọc thẻ không thành công do thiếu hoặc mất dữ liệu",
        "5003": "Quá trình xác thực thẻ đã xảy ra lỗi. Mất kết nối đến thẻ!",
        "5004": "Quá trình xác thực thẻ đã xảy ra lỗi. Mất kết nối đến thẻ!",
        "5009": "Quá trình xác thực thẻ đã xảy ra lỗi. Mất kết nối đến thẻ!",
        "5007": "Thông tin CCCD bị sao chép hoặc làm giả",
        "5008": "Giấy tờ không hợp ợp lệ/giả mạo/ bị làm giả",
        "5012": "Hết thời gian yêu cầu",
        "5013": "Đọc thẻ không thành công do không kết nối được đến thẻ",
        "5014": "Phiên bản iOS đang dùng không hỗ trợtiêu chuẩn chip NFC, yêu cầu iOS 13.0 trở lên",
        "5010": "Certificate hết hạn",
        "5011": "CCCD hết hạn",
        "1031": "Dữ liệu ảnh không hợp lệ",
        "1061": "Định dạng ảnh không hợp lệ",
        "1019": "Ảnh không có khuôn mặt hoặc có nhưng không nhận diện được khuôn mặt",
        "1068": "Hết thời gian yêu cầu",
        "1031": "Dữ liệu ảnh không hợp lệ",
        "102": "Định dạng file ảnh chỉ chấp nhận jpq, jpeg, png",
        "109": "Kích thước ảnh không hợp lệ. Vui lòng chọn kích thước 720p - 1080p - 2k",
        "1019": "Ảnh chụp không hợp lệ",
        "1013": "Lỗi kết nối hệ thống không xác định",
        "1021": "Không phải người thật",
        "1013": "Lỗi kết nối hệ thống không xác định",
        "1015": "Lỗi xác thực",
        "1016": "Yêu cầu xác thực không hợp lệ",
        "1017": "Lỗi truy cập hệ thống",
        "1040": "License partner cấp không hợp lệ hoặc đã hết hạn",
        "1041": "Dịch vụ không được đăng ký sử dụng",
        "1042": "EPAY đã sử dụng hết giới hạn tài nguyên dịch vụ",
        "1043": "Partner không tìm thấy thông tin giấy phép",
        "01": "Sai chữ ký, dữ liệu không hợp lệ",
        "04": "Dữ liệu gửi thiếu thông tin yêu cầu",
        "08": "Lỗi xử lý trên server",
        "99": "Hệ thống gián đoạn",
    }

    config_mapping_code_message_success_default = {
        "1000": "Thành công",
        "00": "Thành công",
        "93": "Thành công",
        "0": "Thành công",
    }

    data_configs = [
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_SMS_CONFIRM_CONSENT,
            "config": {
                "client_id": "CRM",
                "uri": "{}/api/v1/sendNotify".format(domain),
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "sms_access_token": sms_access_token,
                "data_encrypted": """<SendNotifyRequest><ReferenceNo>{log_id}</ReferenceNo><PhoneNumber>{phone_number}</PhoneNumber><Message>{message}</Message><CIF></CIF><BranchCode></BranchCode><SendType>2</SendType></SendNotifyRequest>""",
                "format_sms": "KH {profile_name} dong y cung cap du lieu CCCD, dong y Chinh sach bao mat du lieu ca nhan tren website Eximbank. Soan EIB Y gui 8149 de dong y",
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                "certificates_path": certificates_path,
                "vnpay_certificates_path": vnpay_certificates_path,
                "pem_pass_phrase": pem_pass_phrase,
                "timeout_api": timeout_api,
                "jwt_key": jwt_key,
                "jwt_secret": jwt_secret,
                "jwt_audience": jwt_audience,
                "x_client_id": x_client_id,
                "digest_alg": digest_alg,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_READ_CARD,
            "config": {
                "client_id": "CRM_CCCD",
                "uri": "{}/api/v1/ReadCard".format(domain),
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                "certificates_path": certificates_path,
                "vnpay_certificates_path": vnpay_certificates_path,
                "pem_pass_phrase": pem_pass_phrase,
                "timeout_api": timeout_api,
                "jwt_key": jwt_key,
                "jwt_secret": jwt_secret,
                "jwt_audience": jwt_audience,
                "x_client_id": x_client_id,
                "digest_alg": digest_alg,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_CHECK_CARD,
            "config": {
                "client_id": "CRM_CCCD",
                "uri": "{}/api/v1/CheckCard".format(domain),
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                "certificates_path": certificates_path,
                "vnpay_certificates_path": vnpay_certificates_path,
                "pem_pass_phrase": pem_pass_phrase,
                "method_request_log": "CHECK_CARD",
                "timeout_api": timeout_api,
                "jwt_key": jwt_key,
                "jwt_secret": jwt_secret,
                "jwt_audience": jwt_audience,
                "x_client_id": x_client_id,
                "digest_alg": digest_alg,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_CHECK_FACE,
            "config": {
                "client_id": "CRM_CCCD",
                "uri": "{}/api/v1/CheckFace".format(domain),
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "value_matching_face": float(value_matching_face),
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                "certificates_path": certificates_path,
                "vnpay_certificates_path": vnpay_certificates_path,
                "pem_pass_phrase": pem_pass_phrase,
                "method_request_log": "CHECK_FACE",
                "timeout_api": timeout_api,
                "jwt_key": jwt_key,
                "jwt_secret": jwt_secret,
                "jwt_audience": jwt_audience,
                "x_client_id": x_client_id,
                "digest_alg": digest_alg,
                "number_retry": 10,
                "index_split_raw_img1": 0,
                "index_split_raw_img2": 0,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_EXIST,
            "config": {
                "channel": channel,
                "uri": "{}/api/v1/CheckCustomerExits".format(domain),
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                "certificates_path": certificates_path,
                "vnpay_certificates_path": vnpay_certificates_path,
                "pem_pass_phrase": pem_pass_phrase,
                "timeout_api": timeout_api,
                "jwt_key": jwt_key,
                "jwt_secret": jwt_secret,
                "jwt_audience": jwt_audience,
                "x_client_id": x_client_id,
                "digest_alg": digest_alg,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_SAVE_CUSTOMER,
            "config": {
                "channel": channel,
                "uri": "{}/api/v1/SaveCustomer".format(domain),
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                "certificates_path": certificates_path,
                "vnpay_certificates_path": vnpay_certificates_path,
                "pem_pass_phrase": pem_pass_phrase,
                "timeout_api": timeout_api,
                "jwt_key": jwt_key,
                "jwt_secret": jwt_secret,
                "jwt_audience": jwt_audience,
                "x_client_id": x_client_id,
                "digest_alg": digest_alg,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_ID_CHECK_INSERT_DATA_LOG,
            "config": {
                "channel": channel,
                "uri": "{}/api/v1/IdCheckInsertDataLog".format(domain),
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                "certificates_path": certificates_path,
                "vnpay_certificates_path": vnpay_certificates_path,
                "pem_pass_phrase": pem_pass_phrase,
                "timeout_api": timeout_api,
                "jwt_key": jwt_key,
                "jwt_secret": jwt_secret,
                "jwt_audience": jwt_audience,
                "x_client_id": x_client_id,
                "digest_alg": digest_alg,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_INSERT_ID_CHECK,
            "config": {
                "channel": channel,
                "uri": "{}/api/v1/InsertIDCheck".format(domain),
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                "certificates_path": certificates_path,
                "vnpay_certificates_path": vnpay_certificates_path,
                "pem_pass_phrase": pem_pass_phrase,
                "device_type": device_type,
                "timeout_api": timeout_api,
                "jwt_key": jwt_key,
                "jwt_secret": jwt_secret,
                "jwt_audience": jwt_audience,
                "x_client_id": x_client_id,
                "digest_alg": digest_alg,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_CHECK_UNIQUE_ID,
            "config": {
                "channel": channel,
                "uri": "{}/api/v1/CheckUniqueId".format(domain),
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                "certificates_path": certificates_path,
                "vnpay_certificates_path": vnpay_certificates_path,
                "pem_pass_phrase": pem_pass_phrase,
                "device_type": device_type,
                "timeout_api": timeout_api,
                "jwt_key": jwt_key,
                "jwt_secret": jwt_secret,
                "jwt_audience": jwt_audience,
                "x_client_id": x_client_id,
                "digest_alg": digest_alg,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.RESPONSE_SMS_REPLY_CONFIRM_CONSENT,
            "config": {
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "secret_key": secret_key,
                "sms_brand_name_send": sms_brand_name_send,
                "sms_brand_name_receiver": sms_brand_name_receiver,
                "sms_format_tracking_consent": "Y",
                "information_show_in_evident": "EIB Y",
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
    ]

    for data_config in data_configs:
        filter_option = {
            "status": data_config.get("status"),
            "merchant_id": data_config.get("merchant_id"),
            "type": data_config.get("type"),
        }

        ConfigInfoApiModel().update_by_set(filter_option, data_config, upsert=True)

        # Delete cache
        lru_redis_cache.delete_cache_by_pattern("*get_config_info_api*")

    print("Done")
