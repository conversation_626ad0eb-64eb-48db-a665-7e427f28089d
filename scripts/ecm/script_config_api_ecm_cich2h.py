#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 12/01/2025
"""

import datetime
import sys

from src.apis import MobioAdminSDK
from src.common import (
    ConstantKeyConfigSendThirdParty,
    ConstantStatusConfig,
    ThirdPartyType,
)
from src.common.init_lib import lru_redis_cache
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


def init_config_api_ecm(merchant_code, domain, username, user_pass, repository_id, timeout_api):
    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Merchant not found")
        sys.exit(1)

    if username:
        username_decrypt_object = MobioAdminSDK().encrypt_values(
            merchant_id=merchant_id,
            module="MOBILE_BACKEND",
            field="username",
            values=username,
        )

        username = username_decrypt_object.get("data", {}).get(username)
        auth_name = username

    if user_pass:

        password_decrypt_object = MobioAdminSDK().encrypt_values(
            merchant_id=merchant_id,
            module="MOBILE_BACKEND",
            field="password",
            values=user_pass,
        )
        password = password_decrypt_object.get("data", {}).get(user_pass)
        auth_pass = password
    config_mapping_code_message_error_default = {
        "401": "Unauthorized",
        "402": "Forbidden",
        "500": "Internal server error",
        "400": "Bad Request",
        "4001": "Can not connect to adapter",
        "4002": "Token expired, please try again!",
        "4003": "CIC return undefined error",
        "4004": "Occur error while insert to soap table",
        "4005": "CIC return error",
        "4006": "Not found result",
        "4007": "Occur error while parsing soap response",
        "4008": "Không có sản phẩm tái sử dụng",
        "4009": "Hết lượt tra mới trong tháng",
        "4010": "Không có sản phẩm tái sử dụng và hết lượt tra mới trong tháng",
    }

    config_mapping_code_message_success_default = {
        "200": "Truy vấn thành công",
    }

    data_configs = [
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_ECM_CIC_DETAIL_FILE,
            "config": {
                "auth_name": auth_name,
                "auth_pass": auth_pass,
                "repository_id": repository_id,
                ConstantKeyConfigSendThirdParty.URI: "{}/fncmis/resources".format(domain),
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                ConstantKeyConfigSendThirdParty.TIMEOUT_API: timeout_api,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.CONFIG_CUSTOM_INFORMATION_SEND_ECM,
            "config": {"replace_username": ["@eib"]},
            "action_time": datetime.datetime.now(datetime.UTC),
        },
    ]

    for data_config in data_configs:
        filter_option = {
            "status": data_config.get("status"),
            "merchant_id": data_config.get("merchant_id"),
            "type": data_config.get("type"),
        }

        ConfigInfoApiModel().update_by_set(filter_option, data_config, upsert=True)

        # Delete cache
        lru_redis_cache.delete_cache_by_pattern("*get_config_info_api*")

    print("Done")


if __name__ == "__main__":
    merchant_code = sys.argv[1] if len(sys.argv) >= 1 else None
    if not merchant_code:
        print("Please provide merchant_code as argument.")
        sys.exit(1)
    init_config_api_ecm(merchant_code, "http://localhost", "admin", "admin", "CRM", 10)
