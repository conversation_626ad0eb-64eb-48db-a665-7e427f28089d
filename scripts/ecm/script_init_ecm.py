#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 04/02/2025
"""

import argparse
import sys

from scripts.ecm.script_config_api_ecm_cich2h import init_config_api_ecm

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-merchant_code", "--merchant_code", help="Merchant Code", required=True)
    parser.add_argument("-domain", "--domain", help="Domain", required=True)
    parser.add_argument("-auth_name", "--auth_name", help="Auth name", required=True)
    parser.add_argument("-auth_pass", "--auth_pass", help="Auth pass", required=True)
    parser.add_argument("-repository_id", "--repository_id", help="Repository ID", required=True)
    parser.add_argument(
        "-timeout_api",
        "--timeout_api",
        help="Time out request API",
        default=10,
        type=int,
    )

    args = parser.parse_args()

    merchant_code = args.merchant_code
    domain = args.domain
    auth_name = args.auth_name
    auth_pass = args.auth_pass
    repository_id = args.repository_id
    timeout_api = args.timeout_api
    if not merchant_code:
        print("Please provide merchant_code as argument.")
        sys.exit(1)
    init_config_api_ecm(merchant_code, domain, auth_name, auth_pass, repository_id, timeout_api)
