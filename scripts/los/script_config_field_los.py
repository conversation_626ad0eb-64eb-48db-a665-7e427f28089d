#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 04/11/2024
"""


import sys

from src.common import TypeConfigLos
from src.common.init_lib import lru_redis_cache
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.los_config_model import LosConfigModel


def script_config_field_los(merchant_id):
    data_configs = [
        {
            "field_name": "Mã giao dịch LOS",
            "field_key": "MaLOS",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": True,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 2,
            "order_in_detail": 1,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "<PERSON><PERSON><PERSON> kh<PERSON><PERSON> hàng",
            "field_key": "Ten",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": True,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 1,
            "order_in_quick_view": 1,
            "order_in_detail": 1,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "CIF",
            "field_key": "cif",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": True,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 1,
            "order_in_quick_view": 1,
            "order_in_detail": 1,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "Số CCCD",
            "field_key": "cccd",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": False,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 1,
            "order_in_quick_view": 1,
            "order_in_detail": 1,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "Mã số thuế",
            "field_key": "mst",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": False,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 1,
            "order_in_quick_view": 1,
            "order_in_detail": 1,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "CBBH phụ trách",
            "field_key": "rm_username",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 9,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "Tổng số tiền phê duyệt",
            "field_key": "TongTienPD",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": False,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 8,
            "field_property": 88,
            "format": "",
            "currency_unit": "VND",
        },
        {
            "field_name": "Tổng số tiền đề xuất",
            "field_key": "TongTien",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": True,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 2,
            "field_property": 88,
            "format": "",
            "currency_unit": "VND",
        },
        {
            "field_name": "Trạng thái hồ sơ",
            "field_key": "TrangThai",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": True,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 1,
            "order_in_quick_view": 1,
            "order_in_detail": 1,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "Quy trình LOS",
            "field_key": "QuyTrinh",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 7,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "Kênh tiếp nhận",
            "field_key": "KenhTN",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 6,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "Mã bước",
            "field_key": "MaBuoc",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": False,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 1,
            "order_in_quick_view": 1,
            "order_in_detail": 1,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "Tên bước",
            "field_key": "TenBuoc",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 3,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "User BPM đang xử lý",
            "field_key": "UserXuLy",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 4,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "User EOffice xử lý",
            "field_key": "userEOF",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 5,
            "field_property": 2,
            "format": "",
        },
        {
            "field_name": "Ngày Tiếp Nhận",
            "field_key": "NgayNhan",
            "display_type": "date_picker",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 5,
            "field_property": 3,
            "format": "dd/mm/yyyy hh:mm",
        },
        {
            "field_name": "Lý do từ chối(Nếu có)",
            "field_key": "LyDoTuChoi",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 9,
            "field_property": 2,
        },
        {
            "field_name": "Lý do huỷ(Nếu có)",
            "field_key": "TenHuy",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 10,
            "field_property": 2,
        },
        {
            "field_name": "Chi tiết lý do huỷ(Nếu có)",
            "field_key": "ChiTietHuy",
            "display_type": "single_line",
            "order": 1,
            "display_in_list": True,
            "display_in_quick_view": False,
            "display_in_detail": True,
            "area_in_list": 1,
            "area_in_quick_view": 1,
            "area_in_detail": 2,
            "order_in_quick_view": 1,
            "order_in_detail": 11,
            "field_property": 2,
        },
    ]

    filter_option = {
        "merchant_id": merchant_id,
        "type": TypeConfigLos.CONFIG_FIELD,
    }

    LosConfigModel().update_by_set(filter_option, {"config": data_configs}, upsert=True)
    lru_redis_cache.delete_cache_by_pattern("*get_los_config_field*")
    print("Done")


if __name__ == "__main__":
    merchant_code = sys.argv[1] if len(sys.argv) >= 1 else None
    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Please provide merchant_id as argument.")
        sys.exit(1)
    script_config_field_los(merchant_id)
