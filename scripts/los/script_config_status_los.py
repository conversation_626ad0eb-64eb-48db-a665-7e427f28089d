#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/11/2024
"""

import sys

from src.common import TypeConfigLos
from src.common.init_lib import lru_redis_cache
from src.models.mongo.los_config_model import LosConfigModel


def los_config_status(merchant_id):
    config_data = [
        {"title": {"vi": "Khởi tạo"}, "key": "STA03", "color": "#B9BDC1", "order": 1, "status": "active"},
        {"title": {"vi": "Bổ sung"}, "key": "STA04", "color": "#FDD949", "order": 2, "status": "active"},
        {"title": {"vi": "Kiểm soát"}, "key": "STA05", "color": "#FDD949", "order": 3, "status": "active"},
        {"title": {"vi": "<PERSON>hẩm định"}, "key": "STA06", "color": "#FDD949", "order": 4, "status": "active"},
        {"title": {"vi": "<PERSON><PERSON>m soát thẩm định"}, "key": "STA07", "color": "#FDD949", "order": 5, "status": "active"},
        {"title": {"vi": "Phê duyệt"}, "key": "STA08", "color": "#34B764", "order": 6, "status": "active"},
        {"title": {"vi": "Đồng ý"}, "key": "STA09", "color": "#34B764", "order": 7, "status": "active"},
        {"title": {"vi": "Hoàn thành"}, "key": "STA11", "color": "#34B764", "order": 8, "status": "active"},
        {"title": {"vi": "Từ chối"}, "key": "STA10", "color": "#DC1F18", "order": 9, "status": "active"},
        {"title": {"vi": "Huỷ"}, "key": "STA12", "color": "#DC1F18", "order": 10, "status": "active"},
        {"title": {"vi": "Điều phối"}, "key": "STA13", "color": "#006AF5", "order": 11, "status": "active"},
        {"title": {"vi": "Nhập liệu"}, "key": "STA15", "color": "#006AF5", "order": 12, "status": "active"},
    ]

    filter_option = {
        "merchant_id": merchant_id,
        "type": TypeConfigLos.STATUS,
    }

    LosConfigModel().update_by_set(filter_option, {"config": config_data}, upsert=True)
    lru_redis_cache.delete_cache_by_pattern("*get_los_config_status*")
    print("Done")


if __name__ == "__main__":
    merchant_id = sys.argv[1] if len(sys.argv) >= 1 else None
    if not merchant_id:
        print("Please provide merchant_id as argument.")
        sys.exit(1)
    los_config_status(merchant_id)
