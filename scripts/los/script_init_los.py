#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/12/2024
"""

import argparse
import sys

from scripts.los.script_config_api_los import init_config_api_los
from scripts.los.script_config_field_los import script_config_field_los
from scripts.los.script_config_status_los import los_config_status
from src.helpers.internal.mobio.admin import InternalAdminHelper

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-merchant_code", "--merchant_code", help="Merchant Code", required=True)
    parser.add_argument("-domain", "--domain", help="Domain", required=True)
    parser.add_argument("-auth_name", "--auth_name", help="Auth name", default=None)
    parser.add_argument("-auth_pass", "--auth_pass", help="Auth name", default=None)
    parser.add_argument(
        "-timeout_api",
        "--timeout_api",
        help="Time out request API",
        default=10,
        type=int,
    )

    args = parser.parse_args()

    merchant_code = args.merchant_code
    domain = args.domain
    auth_name = args.auth_name
    auth_pass = args.auth_pass
    timeout_api = args.timeout_api
    if not merchant_code:
        print("Please provide merchant_code as argument.")
        sys.exit(1)
    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Merchant code not found.")
        sys.exit(1)

    print("Start init los config field")
    script_config_field_los(merchant_id)
    print("Start init los config field")

    print("Start init los config status")
    los_config_status(merchant_id)
    print("Start init los config status")

    print("Start init config api los")
    init_config_api_los(merchant_id, domain, auth_name, auth_pass, timeout_api)
    print("Finish init config api los")
