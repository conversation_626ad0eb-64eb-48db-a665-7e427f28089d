#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 14/08/2024
"""
import datetime
import json

from src.common import ThirdPartyType
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)

if __name__ == "__main__":
    request_type = ThirdPartyType.SEND_REQUEST_CHECK_CARD
    start_time = datetime.datetime.utcnow().replace(
        day=1, month=10, year=2024, hour=0, minute=0, second=0
    ) - datetime.timedelta(hours=7)
    end_time = datetime.datetime.utcnow().replace(
        day=31, month=10, year=2024, hour=23, minute=59, second=29
    ) - datetime.timedelta(hours=7)

    request_logs = (
        LogRequestSendThirdPartyModel()
        .find(
            {
                "type": {"$in": [ThirdPartyType.SEND_REQUEST_CHECK_CARD, ThirdPartyType.SEND_REQUEST_CHECK_FACE]},
                "$and": [{"start_time": {"$lte": end_time}}, {"start_time": {"$gte": start_time}}],
            },
            {
                "info_request.payload": 1,
                "info_response": 1,
                "action_time": 1,
                "type": 1,
                "_id": 0,
            },
        )
        .sort({"_id": -1})
    )
    result = {ThirdPartyType.SEND_REQUEST_CHECK_CARD: [], ThirdPartyType.SEND_REQUEST_CHECK_FACE: []}
    for request_log in request_logs:
        request_log_type = request_log["type"]
        request_log["action_time"] = request_log["action_time"].strftime("%Y-%M-%d %H:%M:%S")
        result[request_log_type].append(request_log)

    for key, value in result.items():
        with open("{}_0110_3110.json".format(key), "w") as f:
            f.write(json.dumps(value))
