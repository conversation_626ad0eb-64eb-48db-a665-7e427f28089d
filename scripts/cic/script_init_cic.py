#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/12/2024
"""

import argparse
import sys

from scripts.cic.script_config_api_cic import init_config_api_cic
from scripts.cic.script_config_product_cic import cic_config_product
from scripts.cic.script_config_status_cic import cic_config_report_status

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-merchant_code", "--merchant_code", help="Merchant Code", required=True)
    parser.add_argument("-domain", "--domain", help="Domain", required=True)
    parser.add_argument("-api_key", "--api_key", help="Api Key", required=True)
    parser.add_argument("-application_request", "--application_request", help="Application Request", required=True)
    parser.add_argument(
        "-timeout_api",
        "--timeout_api",
        help="Time out request API",
        default=10,
        type=int,
    )

    args = parser.parse_args()

    merchant_code = args.merchant_code
    domain = args.domain
    api_key = args.api_key
    application_request = args.application_request
    timeout_api = args.timeout_api
    if not merchant_code:
        print("Please provide merchant_code as argument.")
        sys.exit(1)
    print("Start init los report status")
    cic_config_report_status(merchant_code)
    print("Finish init los report status")

    print("Start init config product cic")
    cic_config_product(merchant_code)
    print("Finish init config product cic")

    print("Start init config api cic")
    init_config_api_cic(merchant_code, domain, api_key, application_request, timeout_api)
    print("Finish init config api cic")
