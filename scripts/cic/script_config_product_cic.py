#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 22/11/2024
"""
import sys

from src.common import CustomerType, TypeConfigCic
from src.common.init_lib import lru_redis_cache
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.cic_config_model import CicConfigModel


def cic_config_product(merchant_code):
    config_data = {
        CustomerType.KHCN: [
            {"name": "Sản phẩm chi tiết khách hàng vay thể nhân", "code": "S11A"},
            {"name": "Sản phẩm thông tin chủ thẻ tín dụng thể nhân", "code": "R14"},
            {
                "name": "Sản phẩm thông tin bảo đảm tiền vay thể nhân",
                "code": "R21",
            },
            {
                "name": "<PERSON>ản phẩm chi tiết khách hàng vay thể nhân tức thời",
                "code": "S11T",
            },
            {
                "name": "Sản phẩm thông tin quan hệ tín dụng thể nhân",
                "code": "R11A",
            },
        ],
        CustomerType.KHDN: [
            {
                "name": "Sản phẩm thông tin quan hệ tín dụng pháp nhân",
                "code": "R10A",
            },
            {
                "name": "Sản phẩm thông tin chủ thẻ tín dụng pháp nhân",
                "code": "R14.DN",
            },
            {
                "name": "Sản phẩm thông tin bảo đảm tiền vay pháp nhân",
                "code": "R20",
            },
            {
                "name": "Sản phẩm chi tiết khách hàng vay pháp nhân",
                "code": "S10A",
            },
        ],
    }

    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Merchant id not found with merchant code: {}".format(merchant_code))
        sys.exit(1)

    filter_option = {
        "merchant_id": merchant_id,
        "type": TypeConfigCic.PRODUCTS,
    }

    CicConfigModel().update_by_set(filter_option, {"config": config_data}, upsert=True)
    lru_redis_cache.delete_cache_by_pattern("*get_config_products*")
    print("Done")


if __name__ == "__main__":
    merchant_code = sys.argv[1] if len(sys.argv) >= 1 else None
    if not merchant_code:
        print("Please provide merchant_code as argument.")
        sys.exit(1)
    cic_config_product(merchant_code)
