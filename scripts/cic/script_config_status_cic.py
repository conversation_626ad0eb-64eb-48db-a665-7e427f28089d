#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 22/11/2024
"""

import sys

from src.common import TypeConfigCic
from src.common.init_lib import lru_redis_cache
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.cic_config_model import CicConfigModel


def cic_config_report_status(merchant_code):
    config_data = [
        {
            "title": {"vi": "Bản hỏi tin đang chờ duyệt"},
            "key": "0",
            "order": 1,
            "background_color": "",
            "status": "success",
        },
        {
            "title": {"vi": "Kết quả CIC chưa sẵn sàng"},
            "title_in_cic": "Hỏi tin sang CIC,Thành công",
            "key": "1",
            "order": 2,
            "background_color": "#B9BDC1",
            "status": "success",
            "type": "wait_cic",
        },
        {
            "title": {"vi": "<PERSON><PERSON> kết quả tra cứu"},
            "title_in_cic": "Bản hỏi tin có báo cáo trả lời",
            "key": "2",
            "order": 3,
            "background_color": "#34B764",
            "status": "success",
        },
        {
            "title": {"vi": "Kết quả CIC chưa sẵn sàng"},
            "title_in_cic": "Bản hỏi tin đã duyệt",
            "key": "3",
            "order": 4,
            "background_color": "#B9BDC1",
            "status": "fail",
        },
        {
            "title": {"vi": "Bản tin đã được hỏi trong ngày"},
            "title_in_cic": "Bản hỏi tin trong ngày",
            "key": "4",
            "order": 5,
            "background_color": "#B9BDC1",
            "type": "wait_cic",
            "status": "fail",
        },
        {
            "title": {"vi": "Vi phạm danh sách hạn chế"},
            "key": "6",
            "order": 6,
            "background_color": "",
            "status": "fail",
            "type": "wait_cic",
        },
        {"title": {"vi": "Lỗi hỏi tin sang CIC"}, "key": "7", "order": 7, "background_color": "", "status": "fail"},
        {"title": {"vi": "Phân tích bản tin lỗi"}, "key": "9", "order": 8, "background_color": "", "status": "fail"},
        {"title": {"vi": "Bản hỏi tin bị từ chối"}, "key": "10", "order": 9, "background_color": "", "status": "fail"},
        {"title": {"vi": "Trùng mã số phiếu"}, "key": "OUT_01", "order": 10, "background_color": "", "status": "fail"},
        {
            "title": {"vi": "Mã CIC null hoặc trống"},
            "key": "OUT_02",
            "order": 11,
            "background_color": "#34B764",
            "status": "success",
        },
        {
            "title": {"vi": "Có duy nhất 1 mã CIC không thông tin"},
            "key": "OUT_04",
            "order": 12,
            "background_color": "#34B764",
            "status": "success",
        },
        {
            "title": {"vi": "Có nhiều hơn 1 mã CIC"},
            "key": "OUT_05",
            "order": 13,
            "background_color": "",
            "status": "fail",
        },
        {
            "title": {"vi": "Có duy nhất 1 mã CIC có thông tin"},
            "key": "OUT_06",
            "order": 14,
            "background_color": "#34B764",
            "status": "success",
        },
        {
            "title": {"vi": "Mã số phiếu không tồn tại"},
            "key": "OUT_07",
            "order": 15,
            "background_color": "",
            "status": "fail",
        },
        {
            "title": {"vi": "Có duy nhất 1 mã CIC nhưng CICINFO null"},
            "key": "OUT_08",
            "order": 16,
            "background_color": "",
            "status": "fail",
        },
        {
            "title": {"vi": "Hỏi tin sang CIC lỗi"},
            "key": "AD_01",
            "order": 17,
            "background_color": "",
            "status": "fail",
        },
    ]

    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Merchant id not found with merchant code: {}".format(merchant_code))
        sys.exit(1)

    filter_option = {
        "merchant_id": merchant_id,
        "type": TypeConfigCic.STATUS,
    }

    CicConfigModel().update_by_set(filter_option, {"config": config_data}, upsert=True)
    lru_redis_cache.delete_cache_by_pattern("*get_config_status*")
    print("Done")


if __name__ == "__main__":
    merchant_code = sys.argv[1] if len(sys.argv) >= 1 else None
    if not merchant_code:
        print("Please provide merchant_code as argument.")
        sys.exit(1)
    cic_config_report_status(merchant_code)
