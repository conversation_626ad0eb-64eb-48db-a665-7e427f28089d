#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 27/02/2025
"""

import json
import os
import sys

from bson import ObjectId

from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)

if __name__ == "__main__":
    log_id = sys.argv[1] if len(sys.argv) > 1 else None
    if not log_id:
        print("Error: Log ID is missing.")
        exit(1)

    log_data = LogRequestSendThirdPartyModel().find_one({"_id": ObjectId(log_id)})
    if not log_data:
        print("Error: Log not found.")
        exit(1)

    info_request = log_data.get("info_request", {})
    info_response = log_data.get("info_response", {})
    with open("/media/data/public_resources/static/request.json", "w") as f:
        json.dump(info_request, f)
    
