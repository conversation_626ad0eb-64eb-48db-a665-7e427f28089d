#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 04/06/2025
"""

import argparse
import datetime
import sys

from src.common import ThirdPartyType
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)


def process_agg_los_cic(start_time, end_time):

    filter_aggregate = {
        "type": {
            "$in": [
                ThirdPartyType.SEND_REQUEST_LOS_PULL_DANH_SACH,
                ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_CIC,
                ThirdPartyType.SEND_REQUEST_INQUIRY_PRODUCT_CIC,
                ThirdPartyType.SEND_REQUEST_SEARCH_HISTORY_CIC,
                ThirdPartyType.SEND_REQUEST_ECM_CIC_DETAIL_FILE,
            ]
        }
    }

    if start_time and end_time:
        start_time = datetime.datetime.strptime(start_time, "%Y-%m-%d").replace(hour=0, minute=0, second=0)
        end_time = datetime.datetime.strptime(end_time, "%Y-%m-%d").replace(hour=23, minute=59, second=59)
        filter_aggregate.update({"action_time": {"$gte": start_time, "$lte": end_time}})
    print("filter_aggregate :: ", filter_aggregate)

    results = LogRequestSendThirdPartyModel().process_aggregate_by_pipeline(
        [
            {"$match": filter_aggregate},
            {
                "$group": {
                    "_id": "$type",
                    "total": {"$sum": 1},
                }
            },
        ]
    )

    result_report = {
        ThirdPartyType.SEND_REQUEST_LOS_PULL_DANH_SACH: {"type": "[LOS] Lấy danh sách", "total": 0},
        ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_CIC: {"type": "[CIC] Check Customer", "total": 0},
        ThirdPartyType.SEND_REQUEST_INQUIRY_PRODUCT_CIC: {"type": "[CIC] Tra cứu mới", "total": 0},
        ThirdPartyType.SEND_REQUEST_SEARCH_HISTORY_CIC: {"type": "[CIC] Lấy danh sách lịch sử", "total": 0},
        ThirdPartyType.SEND_REQUEST_ECM_CIC_DETAIL_FILE: {"type": "[CIC] ECM CIC Detail File", "total": 0},
    }

    for result in results:
        result_report.get(result.get("_id"), {}).update({"total": result.get("total")})

    print("Kết quả: ", list(result_report.values()))


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-start_time", "--start_time", help="Start time", required=False)
    parser.add_argument("-end_time", "--end_time", help="End time", required=False)
    args = parser.parse_args()
    start_time = args.start_time
    end_time = args.end_time

    if start_time and not end_time:
        print("Please provide end_time as argument.")
        sys.exit(1)
    if not start_time and end_time:
        print("Please provide start_time as argument.")
        sys.exit(1)
    process_agg_los_cic(start_time, end_time)
