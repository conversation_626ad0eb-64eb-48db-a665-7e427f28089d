#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 03/12/2024
"""

from src.apis import MobioNotifySDK
from src.common import KeyConfigNotifySDK

if __name__ == "__main__":
    # data_send = {
    #     "filter": {
    #         "profile_identify": {"identify_type": "identity_card", "identify_value": "*********"},
    #         "profile_name": "Nguyen Van A",
    #         "cic_code": "*********",
    #         "start_time": "2020-01-01T00:00Z",
    #         "end_time": "2020-01-01T00:00Z",
    #         "list_product_code": ["S111"],
    #         "force_new_flag": False,
    #     },
    #     "data": {
    #         "cic_code": "*********",
    #         "profile_name": "Nguyen Van <PERSON>",
    #         "company_tax_code": "*********",
    #         "business_registration": "*********",
    #         "product_code": "S111",
    #         "report_status": "1",
    #         "requested_date": "2020-01-01T00:00Z",
    #         "received_date": "2020-01-01T00:00Z",
    #         "report_doc_id": "*********",
    #         "report_doc_name": "Nguyen Van A",
    #         "sheet_number": "EIBS11T202407030000000788",
    #     },
    # }
    # MobioNotifySDK().send_message_notify_push_id_mobile_app(
    #     merchant_id="57d559c1-39a1-4cee-b024-b953428b5ac8",
    #     key_config="notify_default",
    #     account_ids=["6b50ae43-6fcb-4395-8adc-38a8b1747f45"],
    #     socket_type="mobilebackend_reply_request_search_cic",
    #     title="Test noti 4",
    #     content="Test noti 4",
    #     **data_send,
    # )
    
    merchant_id = "57d559c1-39a1-4cee-b024-b953428b5ac8"
    staff_id = "6b50ae43-6fcb-4395-8adc-38a8b1747f45"
    detail_log_customer_full_flow = {
        "form_id": "67c7c5ff0b4decaeb478e5cf",
        "status": "fail",
        "object_id": "bc5f37dc-3a37-4649-8691-6ec0ab7daa23",
        "object_type": "profile",
    }
    data_send = {
        "staff_id": staff_id,
        "form_id": detail_log_customer_full_flow.get("form_id"),
        "status": detail_log_customer_full_flow.get("status"),
        "object_id": detail_log_customer_full_flow.get("object_id"),
        "object_type": detail_log_customer_full_flow.get("object_type"),
        "object_name": "Nguyen Van A",
    }

    # Push socket
    MobioNotifySDK().send_message_notify_push_id_mobile_app(
        merchant_id=merchant_id,
        key_config="notify_default",
        account_ids=[staff_id],
        socket_type=KeyConfigNotifySDK.MOBILEBACKEND_RESULT_QUICK_SALES,
        title="Test noti kết quả Quick Sales",
        content="Test noti kết quả Quick Sales",
        **data_send,
    )
    # MobioLogging().info("PushSocketNotifyMobileConsumer() :: log_id :: %s" % log_id)
