#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 27/02/2025
"""
import base64
import json
import uuid


def replace_newlines(s):
    return (
        s.replace("\\r\\n", "")
        .replace("\\r", "")
        .replace("\\n", "")
        .replace("\r\n", "")
        .replace("\r", "")
        .replace("\n", "")
    )


if __name__ == "__main__":
    with open("input_request_check_face.json", "r") as f:
        data_check = json.load(f)

    info_request_payload = data_check["info_request_payload"]
    info_response = data_check["info_response"]
    data_request = info_request_payload.get("dataRequest")

    # convert string to json
    info_response_json = json.loads(info_response)
    data_request_json = json.loads(data_request)
    data_response = info_response_json.get("dataResponse")
    data_response_json = json.loads(data_response)
    raw_img2 = replace_newlines(data_request_json.get("rawImg2"))
    img2 = data_response_json.get("img2")
    img2 = img2[:0]
    img2 += raw_img2
    face_image = img2

    imgdata = base64.b64decode(s=replace_newlines(face_image))  # I assume you have a way of picking unique filenames
    filename = "{}.jpeg".format(str(uuid.uuid4()))
    with open(filename, "wb") as f:
        f.write(imgdata)
