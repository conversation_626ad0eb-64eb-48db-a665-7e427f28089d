#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 16/04/2024
"""


import base64
import datetime
import hashlib
import json
import re
import time
import uuid
from copy import deepcopy
from datetime import timezone

import jwt
import jwt.algorithms
from asn1crypto import algos, cms, core, x509
from Crypto import Random
from Crypto.Cipher import AES
from Crypto.Util import Counter
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
from mobio.libs.logging import MobioLogging
from oscrypto import asymmetric

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    ConstantKeyConfigSendThirdParty,
    ConstantMessageSendRequestToThirdParty,
    ConstantNameFlow,
    KeyConfigNotifySDK,
    StatusCode,
    StatusCodePushSocket,
    ThirdPartyType,
)
from src.common.requests_retry import RequestRetryAdapter
from src.helpers.thirdparty.eib.base_send_request import BaseSendRequestThirdParty
from src.message_queue.kafka.producer.push_message_to_topic import Producer
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_card_model import LogRequestCardModel
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)


class ThirdPartyEIB(object):

    @staticmethod
    def sha512_hash(text):
        return hashlib.sha512(text.encode("utf-8")).hexdigest()

    @staticmethod
    def create_jwt(jwt_key, jwt_secret, jwt_audience, expires_in_seconds=86400):
        """
        Creates a JWT token in Python.

        Args:
            jwt_key (str): Key ID for the token.
            jwt_secret (str): Secret key used for signing the token.
            jwt_audience (str): Intended audience for the token.
            expires_in_seconds (int, optional): Expiration time in seconds. Defaults to 30.

        Returns:
            str: The encoded JWT token.
        """

        # Ensure secret is bytes in UTF-8 encoding
        secret_bytes = jwt_secret.encode("utf-8")

        # Create the JWT payload
        payload = {
            "iat": datetime.datetime.now(datetime.UTC),  # Issued at
            "exp": datetime.datetime.now(datetime.UTC) + datetime.timedelta(seconds=expires_in_seconds),  # Expiration
            "aud": jwt_audience,
        }

        # Create the JWT token with PyJWT library
        algorithm = "HS256"

        token = jwt.encode(payload, secret_bytes, algorithm=algorithm, headers={"kid": jwt_key})
        return token

    @staticmethod
    def build_headers(config, payload):
        """
        Builds the headers for the request.

        Args:
            config (dict): Configuration for the request.

        Returns:
            dict: The headers for the request.
        """

        jwt_key = config.get(ConstantKeyConfigSendThirdParty.JWT_KEY)
        jwt_secret = config.get(ConstantKeyConfigSendThirdParty.JWT_SECRET)
        jwt_audience = config.get(ConstantKeyConfigSendThirdParty.JWT_AUDIENCE)
        jwt_token = ThirdPartyEIB.create_jwt(jwt_key, jwt_secret, jwt_audience)
        x_client_id = config.get(ConstantKeyConfigSendThirdParty.X_CLIENT_ID)
        # string_body = "".join(list(payload.values()))
        plaintext = json.dumps(payload, separators=(",", ":"), ensure_ascii=False)
        hash_plaintext = ThirdPartyEIB.sha512_hash(plaintext)
        MobioLogging().info("build_headers :: hash_plaintext :: {}".format(hash_plaintext))
        signature_body = ThirdPartyEIB.build_signature_by_plaintext(config, hash_plaintext)
        basic_token = ThirdPartyEIB.create_basic_auth(
            config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME), config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        )

        headers = {
            "Content-Type": "application/json",
            "X-Authorization": "Bearer " + jwt_token,
            "X-ClientId": x_client_id,
            "X-Signature": signature_body,
            "Authorization": "Basic " + basic_token,
        }
        return headers

    @staticmethod
    def fix_header_value(header_value):
        # Remove leading whitespace
        header_value = header_value.strip()

        # Convert to bytes
        header_value_bytes = header_value.encode("utf-8")

        # Base64 encode and decode to remove invalid characters
        encoded_value = base64.b64encode(header_value_bytes)
        decoded_value = base64.b64decode(encoded_value)

        # Convert back to string
        fixed_value = decoded_value.decode("utf-8")

        return fixed_value

    @staticmethod
    def handle_response(
        key_get_response_code,
        key_get_response_desc,
        response,
        mapping_code_message_error,
        mapping_code_message_success,
    ):
        data_response = None
        reasons = ""
        status_code = None
        try:
            data_response = response.json()
            ResponseCode = data_response.get(key_get_response_code)
            ResponseDesc_response = data_response.get(key_get_response_desc)

            status_code = StatusCode.THIRD_PARTY_FAILURE
            if not ResponseCode:
                reasons = mapping_code_message_error.get(ResponseCode)
            if mapping_code_message_success.get(ResponseCode):
                status_code = StatusCode.SUCCESS
            else:
                if mapping_code_message_error.get(ResponseCode):
                    reasons = mapping_code_message_error.get(ResponseCode)
                else:
                    reasons = "Mã code lỗi {} chưa được khai báo".format(ResponseCode)

        except Exception as e:  # noqa
            MobioLogging().error("ThirdPartyEIB :: send_sms :: {}".format(e))
            result = response.text
            if not re.search("Successful|0</ResponseCode", result):
                reasons = result
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        return data_response, status_code, reasons

    @staticmethod
    def check_face(log_id, config, data, merchant_id, account_id, log_id_start, sdk_request_id):
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_FACE
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)

        ClientId = config.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        method_request_log = config.get(ConstantKeyConfigSendThirdParty.METHOD_REQUEST_LOG)
        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.CONTENT)
        sent_time = datetime.datetime.now(datetime.UTC)
        sent_timestamp = sent_time.strftime("%Y-%m-%d %H:%M:%S")

        payload = {
            "requestId": log_id,
            "clientId": ClientId,
            "timestamp": sdk_request_id,
            "dataRequest": dataRequest,
            "signature": "null",
        }

        headers = ThirdPartyEIB.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            response_text = response.text
            BaseSendRequestThirdParty().push_socket_send_request_insert_data_log(
                merchant_id=merchant_id,
                request=dataRequest,
                response_text=response_text,
                method=method_request_log,
                error_message=reasons,
                account_id=account_id,
                log_id_start=log_id,
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: check_face :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )
        response_text = response.text
        BaseSendRequestThirdParty().push_socket_send_request_insert_data_log(
            merchant_id=merchant_id,
            request=dataRequest,
            response_text=response_text,
            method=method_request_log,
            error_message=reasons,
            account_id=account_id,
            log_id_start=log_id,
        )

        return ThirdPartyEIB.handle_response(
            "responseCode",
            "responseDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def check_customer_exists(log_id, config, data, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: check_customer_exists :: log_id :: {}, config :: {}, data :: {}".format(
                log_id, config, data
            )
        )
        request_type = ThirdPartyType.SEND_REQUEST_CHECK_CUSTOMER_EXIST
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST)
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        msg_signature = "".join(
            [
                log_id,
                sent_time,
                dataRequest.get("idCard"),
                dataRequest.get("name"),
                dataRequest.get("dateOfBirth"),
                dataRequest.get("gender"),
                dataRequest.get("dateOfIssuance"),
            ]
        )
        signed_cert_b64 = ThirdPartyEIB.gen_signature_vnpay(config, msg_signature)

        payload = {
            "requestId": log_id,
            "channel": channel,
            "timestamp": sent_time,
            "data": dataRequest,
            "signature": signed_cert_b64,
        }

        headers = ThirdPartyEIB.build_headers(config, payload)

        info_request = {"headers": headers, "payload": payload, "config": config}

        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            response = str(e)
            time_request = time.perf_counter() - start_time
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: check_customer_exists :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )

        return ThirdPartyEIB.handle_response(
            "code",
            "desc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def save_customer(log_id, config, data, log_id_start):
        MobioLogging().info(
            "ThirdPartyEIB :: save_customer :: log_id :: {}, config :: {}, data :: {}".format(log_id, config, data)
        )
        request_type = ThirdPartyType.SEND_REQUEST_SAVE_CUSTOMER
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        body_data = to_data.get(NOTIFICATION_TO_STRUCTURE.BODY)

        auth_name = config.get(ConstantKeyConfigSendThirdParty.AUTH_NAME)
        auth_pass = config.get(ConstantKeyConfigSendThirdParty.AUTH_PASS)
        channel = config.get(ConstantKeyConfigSendThirdParty.CHANNEL)

        dataRequest = body_data.get(NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST)
        sent_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S")
        msg_signature = "".join(
            [
                log_id,
                sent_time,
                dataRequest.get("idCard"),
                dataRequest.get("name"),
                dataRequest.get("dateOfBirth"),
                dataRequest.get("gender"),
                dataRequest.get("dateOfIssuance"),
            ]
        )
        signed_cert_b64 = ThirdPartyEIB.gen_signature_vnpay(config, msg_signature)

        payload = {
            "requestId": log_id,
            "channel": channel,
            "data": dataRequest,
            "signature": signed_cert_b64,
        }

        headers = ThirdPartyEIB.build_headers(config, payload)
        info_request = {"headers": headers, "payload": payload, "config": config}

        reasons = ""
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        start_time = time.perf_counter()
        try:

            response = RequestRetryAdapter().retry_a_post_request(
                config.get(ConstantKeyConfigSendThirdParty.URI),
                headers=headers,
                payload=payload,
                timeout=config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API),
            )
            time_request = time.perf_counter() - start_time
        except Exception as e:
            time_request = time.perf_counter() - start_time
            response = str(e)
            LogRequestSendThirdPartyModel().save_log_send_eib(
                request_type, config, data, info_request, response, log_id, log_id_start, time_request
            )
            return data_response_json, status_code, response

        MobioLogging().info("ThirdPartyEIB :: save_customer :: response :: {}".format(response.text))
        # save log
        LogRequestSendThirdPartyModel().save_log_send_eib(
            request_type, config, data, info_request, response.text, log_id, log_id_start, time_request
        )
        return ThirdPartyEIB.handle_response(
            "code",
            "desc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    @staticmethod
    def encrypt(key, plaintext):
        iv = Random.new().read(AES.block_size)

        ctr = Counter.new(128, initial_value=int.from_bytes(iv, byteorder="big"))

        cipher = AES.new(bytes(key, "utf-8"), AES.MODE_CTR, counter=ctr)
        return base64.b64encode(iv + cipher.encrypt(bytes(plaintext, "utf-8")))

    @staticmethod
    def decrypt(key, ciphertext):
        enc = base64.urlsafe_b64decode(ciphertext)
        iv = enc[: AES.block_size]
        ctr = Counter.new(128, initial_value=int.from_bytes(iv, byteorder="big"))

        cipher = AES.new(bytes(key, "utf-8"), AES.MODE_CTR, counter=ctr)

        return cipher.decrypt(enc[AES.block_size :]).decode("utf-8")

    @staticmethod
    def build_signature_by_plaintext(config, plaintext):
        certificates_path = config.get(ConstantKeyConfigSendThirdParty.CERTIFICATES_PATH)
        pem_pass_phrase = config.get(ConstantKeyConfigSendThirdParty.PEM_PASS_PHRASE)
        # signature
        key_signer, cert_signer = CMSSignedData.read_key_cer_p12(
            private_key_path=certificates_path, certificate_password=pem_pass_phrase
        )
        signature = CMSSignedData.sign_message(
            plaintext, key_signer, cert_signer, digest_alg=config.get(ConstantKeyConfigSendThirdParty.DIGEST_ALG)
        )
        signed_cert_b64 = signature.decode("utf-8").replace("\n", "")
        return signed_cert_b64

    @staticmethod
    def gen_signature_vnpay(config, plaintext):
        certificates_path = "prv.rsa"
        # pem_pass_phrase = config.get(ConstantKeyConfigSendThirdParty.PEM_PASS_PHRASE)
        # signature
        private_key = CMSSignedData.load_rsa_private_key(certificates_path)

        # Generate signature
        signature = CMSSignedData.gen_signature_by_file_rsa(private_key, plaintext)
        return signature

    @staticmethod
    def create_basic_auth(user_name: str, password: str) -> str:
        auth_str = f"{user_name}:{password}"
        auth_bytes = auth_str.encode("ascii")
        base64_bytes = base64.b64encode(auth_bytes)
        base64_str = base64_bytes.decode("ascii")
        return base64_str


class SendRequestCheckCustomerExist:
    @staticmethod
    def _send(log_id, merchant_id, request_body, account_id, action_time, log_id_start, flow_name):
        func_visit = "SendRequestCheckCustomerExist"
        log_id_push_socket = log_id_start if log_id_start else log_id
        request_body_check_customer_exist = request_body.get("body_customer_exist")
        request_body_check_check_card = request_body.get("body_check_card")

        data, config = SendRequestCheckCustomerExist._build_config_send_request_check_customer_exist(
            merchant_id, request_body_check_customer_exist
        )
        response, status_code, reasons = ThirdPartyEIB.check_customer_exists(
            log_id, config, data, log_id_start=log_id_start
        )
        MobioLogging().info(
            "_send_request_check_customer_exist :: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            data_send_socket = {
                "data": {
                    "status": StatusCodePushSocket.FAIL,
                    "reason": reasons,
                    "log_id": log_id_push_socket,
                    "func_visit": func_visit,
                }
            }
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )
            return
        data_update_read_card = {"isAuthInformation": False, "isAuthTemporary": False}
        if flow_name == ConstantNameFlow.TELLER_APP:
            data_update_read_card.update({"isNextAuthFace": True})

        data_response = response.get("data", {})
        userExists = data_response.get("userExists")
        log_request_card = LogRequestCardModel().find_by_log_request_id(merchant_id, log_id_start)
        if not log_request_card:
            MobioLogging().info("_send_request_check_customer_exist :: not log_request_card")
            return
        log_request_card_id = log_request_card.get("_id")
        cardInformation = log_request_card.get("cardInformation")
        lst_func_handle = log_request_card.get("lst_func_handle", [])
        lst_func_handle.append(func_visit)
        if userExists and userExists == "true":
            data_update_read_card.update(
                {"isAuthInformation": True, "isAuthTemporary": True, "lst_func_handle": lst_func_handle}
            )
            if flow_name == ConstantNameFlow.TELLER_APP:
                data_update_read_card.update({"isNextAuthFace": False})

            LogRequestCardModel().update_by_set({"_id": log_request_card_id}, data_update_read_card)
            body_send_socket = {
                "status": StatusCodePushSocket.SUCCESS,
                "isAuthInformation": data_update_read_card.get("isAuthInformation"),
                "isAuthTemporary": data_update_read_card.get("isAuthTemporary"),
                "cardInformation": cardInformation,
                "func_visit": func_visit,
                "log_id": log_id_push_socket,
            }
            if flow_name == ConstantNameFlow.TELLER_APP:
                body_send_socket.update(
                    {
                        "isNextAuthFace": data_update_read_card.get("isNextAuthFace"),
                    }
                )
            if flow_name == ConstantNameFlow.CBBH_ADD_PROFILE and "SendRequestCheckUniqueId" in lst_func_handle:
                body_send_socket.update(
                    {
                        "cif": log_request_card.get("cif"),
                    }
                )
            if (
                flow_name == ConstantNameFlow.CBBH_ADD_PROFILE and "SendRequestCheckUniqueId" in lst_func_handle
            ) or flow_name == ConstantNameFlow.TELLER_APP:
                data_send_socket = {"data": body_send_socket}
                Producer().push_message_push_socket_notify_mobile(
                    merchant_id=merchant_id,
                    account_id=account_id,
                    message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                    data_send=data_send_socket,
                )
        else:
            log_id_new = str(uuid.uuid1())
            if flow_name == ConstantNameFlow.TELLER_APP:
                MobioLogging().info("_send_request_check_customer_exist :: log_id_new :: {}".format(log_id_new))
                data_send = {
                    ConstantMessageSendRequestToThirdParty.LOG_ID: log_id_new,
                    ConstantMessageSendRequestToThirdParty.LOG_ID_START: log_id_start,
                    ConstantMessageSendRequestToThirdParty.REQUEST_BODY: request_body_check_check_card,
                    ConstantMessageSendRequestToThirdParty.REQUEST_TYPE: ThirdPartyType.SEND_REQUEST_CHECK_CARD,
                }
                Producer().push_message_to_request_third_party(
                    merchant_id, account_id=account_id, action_time=action_time, data_send=data_send
                )
            body_send_socket = {
                "status": StatusCodePushSocket.SUCCESS,
                "isAuthInformation": data_update_read_card.get("isAuthInformation"),
                "isAuthTemporary": data_update_read_card.get("isAuthTemporary"),
                "cardInformation": cardInformation,
                "func_visit": func_visit,
                "log_id": log_id_push_socket,
            }
            if flow_name == ConstantNameFlow.TELLER_APP:
                body_send_socket.update(
                    {
                        "isNextAuthFace": data_update_read_card.get("isNextAuthFace"),
                    }
                )
            if flow_name == ConstantNameFlow.CBBH_ADD_PROFILE and "SendRequestCheckUniqueId" in lst_func_handle:
                body_send_socket.update(
                    {
                        "cif": log_request_card.get("cif"),
                    }
                )
            data_send_socket = {"data": body_send_socket}
            Producer().push_message_push_socket_notify_mobile(
                merchant_id=merchant_id,
                account_id=account_id,
                message_type=KeyConfigNotifySDK.MOBILEBACKEND_SUCCESSFUL_DECODED_CITIZEN_ID,
                data_send=data_send_socket,
            )
            LogRequestCardModel().update_by_set({"_id": log_request_card_id}, {"lst_func_handle": lst_func_handle})

    @staticmethod
    def _build_config_send_request_check_customer_exist(merchant_id, request_body):
        config = ConfigInfoApiModel().get_config_info_api_check_customer_exist(merchant_id)
        # if not config:
        #     raise CustomError("Not config send check customer exist")

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: request_body}
            }
        }

        return data, config


class CMSSignedData(object):
    @staticmethod
    def sign_bytes(
        data_unsigned,
        key_signer,
        cert_signer,
        digest_alg="sha256",
        sig_alg="rsa",
        attrs=True,
        include_cert_signer=True,
        additional_certs=None,
        signed_value=None,
    ):
        """Takes bytes, creates a ContentInfo structure and returns it as signed bytes

        Notes:
            cert_signer is mandatory (needed to get Issuer and Serial Number ) but can be
                excluded from signed data.

        Args:
            data_unsigned (bytes): data
            key_signer (:obj:`oscrypto.asymmetric.PrivateKey`): Private key used to sign the
                message.
            cert_signer (:obj:`asn1crypto.x509.Certificate`): Certificate/Public Key
                (belonging to Private Key) that will be included in the signed message.
            digest_alg (str): Digest (Hash) Algorithm - e.g. "sha256"
            sig_alg (str): Signature Algorithm
            attrs (bool): Whether to include signed attributes (signing time). Default
                to True
            include_cert_signer (bool): Whether to include the public certificate of the signer
                in the signed data. Default to True
            additional_certs (:obj:`list` of :obj:`asn1crypto.x509.Certificate`): List of
                additional certificates to be included (e.g. Intermediate or Root CA certs).
            signed_value: unknown


        Returns:
             bytes: signed bytes

        """

        if not isinstance(data_unsigned, bytes):
            raise AttributeError("only bytes supported")

        if not isinstance(key_signer, asymmetric.PrivateKey):
            raise AttributeError("only asn1crypto.keys.PrivateKeyInfo supported")

        if not isinstance(cert_signer, x509.Certificate):
            raise AttributeError("only asn1crypto.x509.Certificate supported")

        if include_cert_signer:
            certificates = [cert_signer]
        else:
            certificates = []

        if additional_certs:
            for additional in additional_certs:
                if not isinstance(additional, x509.Certificate):
                    raise AttributeError("only asn1crypto.x509.Certificate supported")
                certificates.append(additional)

        if digest_alg not in ["md5", "sha1", "sha256", "sha512"]:
            raise AttributeError("digest algorithm unsupported: {}".format(digest_alg))

        if signed_value is None:
            signed_value = getattr(hashlib, digest_alg)(data_unsigned).digest()
        signed_time = datetime.datetime.now(tz=timezone.utc)

        signer = {
            "version": "v1",
            "sid": cms.SignerIdentifier(
                {
                    "issuer_and_serial_number": cms.IssuerAndSerialNumber(
                        {
                            "issuer": cert_signer.issuer,
                            "serial_number": cert_signer.serial_number,
                        }
                    ),
                }
            ),
            "digest_algorithm": algos.DigestAlgorithm({"algorithm": digest_alg}),
            "signature": signed_value,
        }

        pss_digest_alg = digest_alg  # use same digest algorithm for pss signature as for message

        if sig_alg == "rsa":
            signer["signature_algorithm"] = algos.SignedDigestAlgorithm({"algorithm": "rsassa_pkcs1v15"})

        elif sig_alg == "pss":
            salt_length = getattr(hashlib, pss_digest_alg)().digest_size
            signer["signature_algorithm"] = algos.SignedDigestAlgorithm(
                {
                    "algorithm": "rsassa_pss",
                    "parameters": algos.RSASSAPSSParams(
                        {
                            "hash_algorithm": algos.DigestAlgorithm({"algorithm": pss_digest_alg}),
                            "mask_gen_algorithm": algos.MaskGenAlgorithm(
                                {
                                    "algorithm": algos.MaskGenAlgorithmId("mgf1"),
                                    "parameters": {
                                        "algorithm": algos.DigestAlgorithmId(pss_digest_alg),
                                    },
                                }
                            ),
                            "salt_length": algos.Integer(salt_length),
                            "trailer_field": algos.TrailerField(1),
                        }
                    ),
                }
            )

        else:
            raise AttributeError("signature algorithm unsupported: {}".format(sig_alg))

        if attrs:
            if attrs is True:
                signer["signed_attrs"] = [
                    cms.CMSAttribute(
                        {
                            "type": cms.CMSAttributeType("content_type"),
                            "values": ("data",),
                        }
                    ),
                    cms.CMSAttribute(
                        {
                            "type": cms.CMSAttributeType("message_digest"),
                            "values": (signed_value,),
                        }
                    ),
                    cms.CMSAttribute(
                        {
                            "type": cms.CMSAttributeType("signing_time"),
                            "values": (cms.Time({"utc_time": core.UTCTime(signed_time)}),),
                        }
                    ),
                ]
            else:
                signer["signed_attrs"] = attrs

        config = {
            "version": "v1",
            "digest_algorithms": cms.DigestAlgorithms((algos.DigestAlgorithm({"algorithm": digest_alg}),)),
            "encap_content_info": {
                "content_type": "data",
            },
            "certificates": certificates,
            "signer_infos": [
                signer,
            ],
        }
        data_signed = cms.ContentInfo(
            {
                "content_type": cms.ContentType("signed_data"),
                "content": cms.SignedData(config),
            }
        )
        if attrs:
            to_sign = data_signed["content"]["signer_infos"][0]["signed_attrs"].dump()
            to_sign = b"\x31" + to_sign[1:]
        else:
            to_sign = data_unsigned

        if sig_alg == "rsa":
            signed_value_signature = asymmetric.rsa_pkcs1v15_sign(key_signer, to_sign, digest_alg.lower())

        elif sig_alg == "pss":
            signed_value_signature = asymmetric.rsa_pss_sign(key_signer, to_sign, pss_digest_alg)

        else:
            raise AttributeError("signature algorithm unsupported: {}".format(sig_alg))

        data_signed["content"]["signer_infos"][0]["signature"] = signed_value_signature

        return data_signed.dump()

    @staticmethod
    def read_key_cer_p12(private_key_path, certificate_password):
        """
        * Tạo private key
        - B1 : Tạo Private key RSA 2048 có bảo vệ bằng mật khẩu
        openssl genrsa -des3 -out Privatekey.pem 2048

        cho em xin cái pass
        - B2 : Tạo file .pem
        openssl req -new -x509 -sha256 -key Privatekey.pem -out cert.pem -days 7300
        - B3 : Tạo file .p12
        openssl pkcs12 -export -in cert.pem -inkey Privatekey.pem -out Privatekey.p12
        *, Tạo public key
        openssl pkcs12 -in Privatekey.p12 -clcerts -nokeys -out PubliKey.cer
        """

        # Đường dẫn tới tệp tin Privatekey.p12 và mật khẩu của nó
        password = certificate_password

        # Đọc nội dung của tệp tin P12
        with open(private_key_path, "rb") as p12_file:
            p12_data = p12_file.read()

        # Trích xuất khóa riêng tư từ tệp tin P12
        private_key, certificates, _ = serialization.pkcs12.load_key_and_certificates(
            p12_data, password.encode(), default_backend()
        )

        private_key = private_key.private_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption(),
        )

        certificate = certificates.public_bytes(encoding=serialization.Encoding.DER)

        return private_key, certificate

    @staticmethod
    def sign_message(
        msg,
        k_signer,
        c_signer,
        digest_alg="sha256",
        sig_alg="rsa",
        attrs=True,
        include_cert_signer=True,
        additional_certs=None,
    ):

        # private key
        if not isinstance(k_signer, asymmetric.PrivateKey):
            k_signer = asymmetric.load_private_key(k_signer)

        # cert
        if not isinstance(c_signer, x509.Certificate):
            cert_signer_oscrypto = asymmetric.load_certificate(c_signer)
            c_signer = cert_signer_oscrypto.asn1

        additional_x509 = []
        if additional_certs:
            for additional in additional_certs:
                if not isinstance(additional, x509.Certificate):
                    additional_oscrypto = asymmetric.load_certificate(additional)
                    additional = additional_oscrypto.asn1

                additional_x509.append(additional)

        # make a deep copy of original message to avoid any side effects (original will not be touched)
        copied_msg = deepcopy(msg)

        data_unsigned = copied_msg.encode("utf-8")
        data_signed = CMSSignedData.sign_bytes(
            data_unsigned,
            k_signer,
            c_signer,
            digest_alg,
            sig_alg,
            attrs=attrs,
            include_cert_signer=include_cert_signer,
            additional_certs=additional_x509,
        )
        data_signed = base64.encodebytes(data_signed)

        return data_signed

    @staticmethod
    def verify_signature(public_key, data, signature):
        try:
            public_key.verify(base64.b64decode(signature), data.encode("utf-8"), padding.PKCS1v15(), hashes.SHA256())
            return True
        except Exception as e:
            return False

    @staticmethod
    def load_rsa_private_key(filepath):
        with open(filepath, "r") as key_file:
            key_data = key_file.read()
            data_load = (
                f"-----BEGIN PRIVATE KEY-----\n{key_data}\n-----END PRIVATE KEY-----"
                if "BEGIN PRIVATE KEY" not in key_data
                else key_data
            )
            print("Rsa :: ", data_load)
            try:
                # Decode Base64 content and load as PEM format
                private_key = serialization.load_pem_private_key(
                    data_load.encode(),
                    password=None,
                    backend=default_backend(),
                )
            except ValueError:
                raise ValueError("Failed to load private key. The key data may be in an incorrect format.")
        return private_key

    @staticmethod
    def load_rsa_public_key(filepath):
        with open(filepath, "r") as key_file:
            key_data = key_file.read()
            data_load = (
                f"-----BEGIN PUBLIC KEY-----\n{key_data}\n-----END PUBLIC KEY-----"
                if "BEGIN PUBLIC KEY" not in key_data
                else key_data
            )
            try:
                # Decode Base64 content and load as PEM format
                public_key = serialization.load_pem_public_key(
                    data_load.encode(),
                    backend=default_backend(),
                )
            except ValueError:
                raise ValueError("Failed to load public key. The key data may be in an incorrect format.")
        return public_key

    @staticmethod
    def gen_signature_by_file_rsa(private_key, data):
        signature = private_key.sign(data.encode("utf-8"), padding.PKCS1v15(), hashes.SHA256())
        return base64.b64encode(signature).decode("utf-8")


if __name__ == "__main__":
    message = {
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "account_id": "afc0da24-ce42-40d0-aa21-6c8124669afe",
        "action_time": "2024-07-29 07:47:55",
        "log_id": "dffb8f81-4d7e-11ef-9e66-e7447059ebe8",
        "log_id_start": "68c0b16b-4d7e-11ef-b4e5-8d639ae39eef",
        "request_body": {
            "body_customer_exist": {
                "idCard": "************",
                "name": "Qu\u00e1ch Kh\u00f4i Nguy\u00ean",
                "dateOfBirth": "16/12/2000",
                "gender": "N\u1eef",
                "dateOfIssuance": "14/08/2021",
            },
            "body_check_unique_id": {
                "uniqueidType": "CCUOC",
                "uniqueId": "CCUOC_************",
                "name": "",
                "dob": "",
                "taxId": "",
            },
            "body_check_card": {
                "data_decryption": "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:47:59.783282364Z 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:47:59.783282364Z 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",
                "sdk_request_round": "X/M=",
                "sdk_request_session": "9D76FBB2-34E7-4CE1-AD01-02C0E6BE11C4",
                "request_timestamp": "*************",
                "sdk_request_id": "EP2023120402450807876_*************",
            },
        },
        "request_type": "send_request_check_customer_exist",
        "flow_name": "cbbh_add_profile",
    }

    SendRequestCheckCustomerExist._send(
        log_id=message.get("log_id"),
        merchant_id=message.get("merchant_id"),
        request_body=message.get("request_body"),
        account_id=message.get("account_id"),
        action_time=message.get("action_time"),
        log_id_start=message.get("log_id_start"),
        flow_name=message.get("flow_name"),
    )
