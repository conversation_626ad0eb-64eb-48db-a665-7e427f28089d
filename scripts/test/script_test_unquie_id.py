#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 10/06/2025
"""

import uuid

from scripts.quick_sales.call.t1 import QuickSalesHelper

if __name__ == "__main__":
    merchant_id = "a0d4a74d-b475-4ef3-af7e-2d890ccce00b"
    profile_name = "Tung"
    phone_number = "0909090909"
    identify = "052091016855"

    result_check_aml, result_check_cif, response_request = QuickSalesHelper().check_cif_by_information_profile(
        str(uuid.uuid4()),
        merchant_id,
        profile_name,
        phone_number,
        identify={"identify_value": identify, "identify_type": "citizen_identity"},
    )
    print(result_check_aml)
    print(result_check_cif)
    print(response_request)
