#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/11/2024
"""
import argparse
import datetime
import os

from src.common import ConstantKeyConfigSendThirdParty, ConstantStatusConfig
from src.common.init_lib import lru_redis_cache
from src.models.mongo.config_info_api_model import ConfigInfoApiModel

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-merchant_code", "--merchant_code", help="Merchant Code", required=True)
    parser.add_argument("-domain", "--domain", help="Domain", required=True)
    parser.add_argument("-auth_name", "--auth_name", help="Auth name", default=None)
    parser.add_argument("-auth_pass", "--auth_pass", help="Auth name", default=None)
    parser.add_argument(
        "-timeout_api",
        "--timeout_api",
        help="Time out request API",
        default=10,
        type=int,
    )

    args = parser.parse_args()

    merchant_code = args.merchant_code
    domain = args.domain
    auth_name = args.auth_name
    auth_pass = args.auth_pass
    timeout_api = args.timeout_api

    merchant_id = "a0d4a74d-b475-4ef3-af7e-2d890ccce00b"

    if os.getenv("VM") == "local":
        domain = "http://localhost"

    config_mapping_code_message_error_default = {}

    config_mapping_code_message_success_default = {"1": "Success", 1: "Success"}

    data_configs = [
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": "send_request_los_authenticate",
            "config": {
                ConstantKeyConfigSendThirdParty.URI: "{}/crm/authenticate".format(domain),
                ConstantKeyConfigSendThirdParty.AUTH_NAME: auth_name,
                ConstantKeyConfigSendThirdParty.AUTH_PASS: auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                ConstantKeyConfigSendThirdParty.TIMEOUT_API: timeout_api,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": "send_request_los_pull_danh_sach",
            "config": {
                ConstantKeyConfigSendThirdParty.URI: "{}/crm/Pull_DanhsachLOS".format(domain),
                ConstantKeyConfigSendThirdParty.AUTH_NAME: auth_name,
                ConstantKeyConfigSendThirdParty.AUTH_PASS: auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                ConstantKeyConfigSendThirdParty.TIMEOUT_API: timeout_api,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": "send_request_los_trang_thai",
            "config": {
                ConstantKeyConfigSendThirdParty.URI: "{}/crm/Pull_trangthai".format(domain),
                ConstantKeyConfigSendThirdParty.AUTH_NAME: auth_name,
                ConstantKeyConfigSendThirdParty.AUTH_PASS: auth_pass,
                "mapping_code_message_error": config_mapping_code_message_error_default,
                "mapping_code_message_success": config_mapping_code_message_success_default,
                ConstantKeyConfigSendThirdParty.TIMEOUT_API: timeout_api,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": "config_custom_information_send_hpt",
            "config": {
                "replace_username": ["@eib"],
                "list_field_not_result": ["ThongTinSP"],
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        },
    ]

    for data_config in data_configs:
        filter_option = {
            "status": data_config.get("status"),
            "merchant_id": data_config.get("merchant_id"),
            "type": data_config.get("type"),
        }

        ConfigInfoApiModel().update_by_set(filter_option, data_config, upsert=True)

        # Delete cache
        lru_redis_cache.delete_cache_by_pattern("*get_config_info_api*")

    print("Done")
