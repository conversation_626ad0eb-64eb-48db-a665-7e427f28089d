#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 10/03/2025
"""

import sys

from src.common import ConstantStatusConfig, ThirdPartyType
from src.common.init_lib import lru_redis_cache
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


def config_param_quick_sales(merchant_code):
    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Merchant not found")
        sys.exit(1)

    config_param_quick_sales = {
        "status": ConstantStatusConfig.ENABLE,
        "merchant_id": merchant_id,
        "type": ThirdPartyType.CONFIG_PARAM_QUICK_SALES_PREMIUM_ACCOUNT,
        "account_lengths": [
            {"name": "<PERSON><PERSON><PERSON> kho<PERSON>n 8 số", "code": "08", "fee": 0},
            {"name": "<PERSON><PERSON><PERSON> kho<PERSON>n 9 số", "code": "09", "fee": 0},
            {"name": "<PERSON><PERSON><PERSON> kho<PERSON>n 10 số", "code": "10", "fee": 0},
            {"name": "Tài khoản 12 số", "code": "12", "fee": 0},
        ],
        "premium_number_types": [
            {"name": "Số giống nhau", "code": "01"},
            {"name": "Số phát lộc 6,8", "code": "02"},
            {"name": "Số phát tài 39,79", "code": "03"},
            {"name": "Số tiến", "code": "04"},
            {"name": "Số lục, ngũ, tứ, tam hoa", "code": "05"},
            {"name": "Số phát lộc 368, 386", "code": "06"},
            {"name": "Số lặp lại", "code": "07"},
            {"name": "Số soi gương", "code": "08"},
            {"name": "Số ngũ, tứ, tam hoa nhụy giữa", "code": "10"},
            {"name": "Số ngũ, tứ, tam hoa không nhụy", "code": "11"},
            {"name": "Số ngũ, tứ, tam hoa bằng nhau", "code": "12"},
            {"name": "Số ngũ, tứ, tam hoa khác nhau", "code": "13"},
            {"name": "Số lưu ý đặc biệt", "code": "14"},
            {"name": "Số tiến cặp", "code": "15"},
            {"name": "Số hỗn hợp", "code": "19"},
        ],
        "premium_account_types": [
            {
                "name": "Số tài khoản ngân hàng tự tạo",
                "code": "STK_CUSTOM",
                "price_type": "free",
                "next_element": [],
                "product_config": {
                    "product_code": "19009",
                    "product_id": "659405b60e86cdd888a9845d",
                    "product_type_code": "KHCN_TGTT_TGTT",
                    "product_type_id": "6572d009c303a2ab7ecc99bd",
                    "product_line_code": "KHCN_TGTT",
                    "product_line_id": "65695f91234df94974a44af6",
                },
            },
            {
                "name": "Tài khoản theo số CCCD",
                "code": "STK_ID_CARD",
                "price_type": "free",
                "next_element": ["account_number"],
                "product_config": {
                    "product_code": "19100",
                    "product_id": "65940fc7fa6e9ef1c2feb906",
                    "product_type_code": "KHCN_TGTT_TGTT",
                    "product_type_id": "6572d009c303a2ab7ecc99bd",
                    "product_line_code": "KHCN_TGTT",
                    "product_line_id": "65695f91234df94974a44af6",
                },
            },
            {
                "name": "Tài khoản theo số điện thoại",
                "code": "STK_PHONE_NUMBER",
                "price_type": "free",
                "next_element": ["account_number"],
                "product_config": {
                    "product_code": "19100",
                    "product_id": "65940fc7fa6e9ef1c2feb906",
                    "product_type_code": "KHCN_TGTT_TGTT",
                    "product_type_id": "6572d009c303a2ab7ecc99bd",
                    "product_line_code": "KHCN_TGTT",
                    "product_line_id": "65695f91234df94974a44af6",
                },
            },
            {
                "name": "Tài khoản số đẹp",
                "code": "STK_PREMIUM_NUMBER",
                "price_type": "charges",
                "next_element": ["beauti_number_type", "account_number"],
                "product_config": {
                    "product_code": "19100",
                    "product_id": "65940fc7fa6e9ef1c2feb906",
                    "product_type_code": "KHCN_TGTT_TGTT",
                    "product_type_id": "6572d009c303a2ab7ecc99bd",
                    "product_line_code": "KHCN_TGTT",
                    "product_line_id": "65695f91234df94974a44af6",
                },
            },
        ],
    }

    filter_option = {
        "status": config_param_quick_sales.get("status"),
        "merchant_id": config_param_quick_sales.get("merchant_id"),
        "type": config_param_quick_sales.get("type"),
    }

    ConfigInfoApiModel().update_by_set(filter_option, config_param_quick_sales, upsert=True)

    # Delete cache
    lru_redis_cache.delete_cache_by_pattern("*get_config_info_api*")

    return


if __name__ == "__main__":
    merchant_code = sys.argv[1] if len(sys.argv) > 1 else None
    config_param_quick_sales(merchant_code)
