#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 04/03/2025
"""

import sys

from src.common import ConstantKeyConfigMappingFieldCrmEib
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.config_mapping_field_crm_to_eib import (
    ConfigMappingFieldCrmToEibModel,
)


def config_mapping_field_crm_eib(merchant_code):
    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Merchant not found")
        sys.exit(1)

    """
        Cấu trúc bộ format:
        {
            "merchant_id": merchant_id,
            "#type_api": {
                "field_mapping_crm": "field_mapping_eib",
            },
        }
    """

    config_mapping_field_crm_eib = {
        "merchant_id": merchant_id,
        "type": "quick_sales",
        ConstantKeyConfigMappingFieldCrmEib.LIST_FIELD_GET_FROM_CRM: [
            "CIFId",
            "AccountNumber",
        ],
        ConstantKeyConfigMappingFieldCrmEib.API_SEND_REQUEST_CREATE_CIF: {
            "AddrDtls": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "4c573b9e-0237-4af7-b459-9d5e22022f14",
                "object_source": "web_form",
                "func_transform_string": """def process(value):\n\tfrom src.common.utils import utf8_to_ascii\n\timport datetime\n\tvalue = value[0] if isinstance(value, list) else {}\n\taddr_line1 = value.get("detail") if value.get("detail") else value.get("value_cccd") if value.get("value_cccd") else "."\n\tAddrDtls = []\n\tfor address_category in ["Mailing", "Home", "Work"]:\n\t\tAddrDtls.append(\n\t\t\t{\n\t\t\t\t"AddrLine1": utf8_to_ascii(addr_line1),\n\t\t\t\t"AddrLine2": ".",\n\t\t\t\t"AddrLine3": ".",\n\t\t\t\t"AddrCategory": address_category,\n\t\t\t\t"City": "00",\n\t\t\t\t"Country": "VN",\n\t\t\t\t"HoldMailFlag": "N",\n\t\t\t\t"HouseNum": ".",\n\t\t\t\t"PrefAddr": "Y" if address_category == "Mailing" else "N",\n\t\t\t\t"PrefFormat": "FREE_TEXT_FORMAT",\n\t\t\t\t"StartDt": datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.000"),\n\t\t\t\t"State": "MIGR",\n\t\t\t\t"StreetName": ".",\n\t\t\t\t"StreetNum": ".",\n\t\t\t\t"PostalCode": "XXXXX",\n\t\t\t\t"FreeTextLabel": utf8_to_ascii(addr_line1),\n\t\t\t}\n\t)\n\treturn AddrDtls""",
                "key_get_data_input_func_process": "AddrDtls",
            },
            "PhoneEmailDtls": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_phone",
                "object_source": "profile",
                "func_transform_string": """def process(phone_number):\n\tfrom src.common.utils import utf8_to_ascii\n\timport datetime\n\tPhoneEmailDtls = []\n\tphone_num = phone_number\n\tif phone_number.startswith("+"):\n\t\tphone_number = phone_number[1:]\n\tif phone_number.startswith("0"):\n\t\tphone_number = "84" + phone_number[1:]\n\tif phone_number.startswith("84"):\n\t\tphone_num = phone_number[2:]\n\n\tphone_num_city_code = "0"\n\tphone_num_local_code = phone_num[0:]\n\n\tfor phone_email_type in ["CELLPH", "HOMEPH1"]:\n\t\tPhoneEmailDtls.append(\n\t\t\t{\n\t\t\t\t"PhoneEmailType": phone_email_type,\n\t\t\t\t"PhoneNum": phone_num,\n\t\t\t\t"PhoneNumCityCode": phone_num_city_code,\n\t\t\t\t"PhoneNumCountryCode": "84",\n\t\t\t\t"PhoneNumLocalCode": phone_num_local_code,\n\t\t\t"PhoneOrEmail": "PHONE",\n\t\t\t"PrefFlag": "Y" if phone_email_type == "CELLPH" else "N",\n\t\t}\n\t)\n\treturn PhoneEmailDtls""",
                "key_get_data_input_func_process": "PhoneEmailDtls",
            },
            "Salutation": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "gender",
                "object_source": "profile",
                "func_transform_string": """def process(gender):\n\treturn "MR." if gender == 2 else "MRS." """,
                "key_get_data_input_func_process": "Salutation",
            },
            "ShortName": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "name",
                "object_source": "profile",
                "func_transform_string": """def process(name):\n\tfrom src.common.utils import utf8_to_ascii\n\treturn utf8_to_ascii(name).upper().split(" ")[-1]""",
                "key_get_data_input_func_process": "ShortName",
            },
            "AccessOwnerGroupName": {
                "use_default": True,
                "value_default": "19",
                "set_to_result": True,
            },
            "AccessOwnerSegment": {
                "use_default": True,
                "value_default": "General Banking",
                "set_to_result": True,
            },
            "CoreInterfaceFreeText1": {
                "use_default": True,
                "value_default": "N",
                "set_to_result": True,
            },
            "CoreInterfaceFreeText2": {
                "use_default": True,
                "value_default": "ABCD",
                "set_to_result": True,
            },
            "CoreInterfaceFreeText13": {
                "use_default": False,
                "value_default": "",
                "field_source": "",
                "object_source": "",
                "type_target_convert": "datetime",
                "action_convert": "",
                "func_transform_string": """def process():\n\timport datetime\n\treturn datetime.datetime.now(datetime.timezone.utc).strftime("%d-%m-%Y")""",
                "set_to_result": True,
            },
            "CoreInterfaceFreeText14": {
                "use_default": False,
                "value_default": "",
                "field_source": "",
                "object_source": "",
                "type_target_convert": "datetime",
                "action_convert": "",
                "func_transform_string": """def process():\n\timport datetime\n\treturn datetime.datetime.now(datetime.timezone.utc).strftime("%d-%m-%Y")""",
                "set_to_result": True,
            },
            "DemographicData_AnnualSalaryIncome": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "DemographicMiscData_EntityCreFlag": {
                "use_default": True,
                "value_default": "Y",
                "set_to_result": True,
            },
            "DemographicMiscData_Type": {
                "use_default": True,
                "value_default": "CURRENT_EMPLOYMENT",
                "set_to_result": True,
            },
            "DSAId": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "staff_code",
                "object_source": "staff_info",
                "func_transform_string": """""",
            },
            "EntityDoctData_CountryOfIssue": {
                "use_default": True,
                "value_default": "VN",
                "set_to_result": True,
            },
            "EntityDoctData_ExpDt": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "dateOfExpiry",
                "object_source": "card_information",
                "func_transform_string": """def process(value):\n\timport datetime\n\treturn datetime.datetime.strptime(value, "%d/%m/%Y").strftime("%Y-%m-%dT%H:%M:%S.000")""",
                "key_get_data_input_func_process": "EntityDoctData_ExpDt",
            },
            "EntityDoctData_IssueDt": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "dateOfIssuance",
                "object_source": "card_information",
                "func_transform_string": """def process(value):\n\timport datetime\n\treturn datetime.datetime.strptime(value, "%d/%m/%Y").strftime("%Y-%m-%dT%H:%M:%S.000")""",
                "key_get_data_input_func_process": "EntityDoctData_IssueDt",
            },
            "EntityDoctData_ReferenceNum": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "idCard",
                "object_source": "card_information",
            },
            "FatcaRemarks": {
                "use_default": True,
                "value_default": "remarks for fatca",
                "set_to_result": True,
            },
            "IsEbankingEnabled": {
                "use_default": True,
                "value_default": "N",
                "set_to_result": True,
            },
            "Manager": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "username",
                "object_source": "staff_info",
                "func_transform_string": """def process(value):\n\treturn value.upper() if not value or '@' not in value else value.split('@')[0].upper()""",
                "key_get_data_input_func_process": "Manager",
            },
            "preferredChannelID": {
                "use_default": True,
                "value_default": "999",
                "set_to_result": True,
            },
            "PrimarySolId": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "sol_id",
                "object_source": "staff_info",
            },
            "PsychographMiscData_DTDt1": {
                "use_default": True,
                "value_default": "2099-12-31T00:00:00.000",
                "set_to_result": True,
            },
            "RelationshipCreatedByID": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "RelationshipDtls_ChildCustId": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "RelationshipDtls_ChildEntity": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "RelationshipDtls_ChildEntityType": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "RelationshipDtls_Relationship": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "RelationshipDtls_RelationshipCategory": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "RelationshipDtls_Type": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "RelationshipMgrID": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "RevenueUnits": {
                "use_default": True,
                "value_default": "VND",
                "set_to_result": True,
            },
            "StaffEmployeeId": {
                "use_default": True,
                "value_default": "",
                "set_to_result": True,
            },
            "SubSegment": {
                "use_default": True,
                "value_default": "02",
                "set_to_result": True,
            },
            "TaxDeductionTable": {
                "use_default": True,
                "value_default": "ZERO",
                "set_to_result": True,
            },
            "TradeFinData_CustNative": {
                "use_default": True,
                "value_default": "Y",
                "set_to_result": True,
            },
            "TradeFinData_InlandTradeAllowed": {
                "use_default": True,
                "value_default": "Y",
                "set_to_result": True,
            },
            "TradeFinData_Name": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "name",
                "object_source": "card_information",
                "func_transform_string": """def process(name):\n\tfrom src.common.utils import utf8_to_ascii\n\treturn utf8_to_ascii(name).upper()""",
                "key_get_data_input_func_process": "TradeFinData_Name",
            },
            "TreasuryCounterparty": {
                "use_default": True,
                "value_default": "N",
                "set_to_result": True,
            },
            "CreatedBySystemId": {
                "use_default": True,
                "value_default": "MANAGER",
                "set_to_result": True,
            },
            "Language": {
                "use_default": True,
                "value_default": "VN",
                "set_to_result": True,
            },
            "Community": {
                "use_default": True,
                "value_default": "99",
                "set_to_result": True,
            },
            "IsMinor": {
                "use_default": True,
                "value_default": "N",
                "set_to_result": True,
            },
            "IsCustNRE": {
                "use_default": True,
                "value_default": "N",
                "set_to_result": True,
            },
            "DefaultAddrType": {
                "use_default": True,
                "value_default": "Mailing",
                "set_to_result": True,
            },
            "NativeLanguageCode": {
                "use_default": True,
                "value_default": "VN",
                "set_to_result": True,
            },
            "RelationshipOpeningDt": {
                "use_default": False,
                "value_default": "",
                "field_source": "",
                "object_source": "",
                "type_target_convert": "datetime",
                "action_convert": "",
                "func_transform_string": """def process():\n\timport datetime\n\treturn datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.000")""",
                "set_to_result": True,
            },
            "SegmentationClass": {
                "use_default": True,
                "value_default": "G",
                "set_to_result": True,
            },
            "StaffFlag": {
                "use_default": True,
                "value_default": "N",
                "set_to_result": True,
            },
            "TradeFinFlag": {
                "use_default": True,
                "value_default": "Y",
                "set_to_result": True,
            },
            "CustType": {
                "use_default": True,
                "value_default": "10001",
                "set_to_result": True,
            },
            "CustTypeCode": {
                "use_default": True,
                "value_default": "10001",
                "set_to_result": True,
            },
            "DemographicData_CustType": {
                "use_default": True,
                "value_default": "10001",
                "set_to_result": True,
            },
            "DemographicData_EmploymentStatus": {
                "use_default": True,
                "value_default": "Other",
                "set_to_result": True,
            },
            "DemographicData_NameOfEmployer": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "_dyn_noi_lam_viec_1747734087125",
                "object_source": "profile",
                "func_transform_string": """def process(value):\n\tfrom src.common.utils import utf8_to_ascii\n\treturn utf8_to_ascii(value).upper()""",
                "key_get_data_input_func_process": "DemographicData_NameOfEmployer",
            },
            "DemographicData_MaritalStatus": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "marital_status",
                "object_source": "profile",
                "func_transform_string": """def process(value):\n\treturn {1:"02",3:"01",5:"03",7:"04",6:"04",2:"99",4:"99"}.get(value, "99")""",
                "key_get_data_input_func_process": "DemographicData_MaritalStatus",
            },
            "DemographicData_Nationality": {
                "use_default": True,
                "value_default": "VN",
                "set_to_result": True,
            },
            "EntityDoctData_DocCode": {
                "use_default": True,
                "value_default": "CCUOC",
                "set_to_result": True,
            },
            "EntityDoctData_TypeCode": {
                "use_default": True,
                "value_default": "100",
                "set_to_result": True,
            },
            "EntityDoctData_PlaceOfIssue": {
                "use_default": True,
                "value_default": "00",
                "set_to_result": True,
            },
            "EntityDoctData_preferredUniqueId": {
                "use_default": True,
                "value_default": "Y",
                "set_to_result": True,
            },
            "EntityDoctData_IDIssuedOrganisation": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "codePlaceOfIssuance",
                "object_source": "card_information",
            },
            "PsychographMiscData_StrText10": {
                "use_default": True,
                "value_default": "VND",
                "set_to_result": True,
            },
            "PsychographMiscData_Type": {
                "use_default": True,
                "value_default": "CURRENCY",
                "set_to_result": True,
            },
        },
        ConstantKeyConfigMappingFieldCrmEib.API_SEND_REQUEST_CREATE_ACCOUNT_EDIGI: {
            "profile_name": {
                "use_default": False,
                "value_default": "",
                "set_to_result": False,
                "field_source": "name",
                "object_source": "profile",
            },
            "name": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "name",
                "object_source": "profile",
                "func_transform_string": """def process(name):\n\tfrom src.common.utils import utf8_to_ascii\n\treturn utf8_to_ascii(name).upper()""",
                "key_get_data_input_func_process": "profile_name",
            },
            "username": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_phone",
                "object_source": "profile",
                "func_transform_string": """def process(phone):\n\tif phone.startswith("+"):\n\t\tphone=phone[1:]\n\tif phone.startswith("84"):\n\t\tphone="0"+phone[2:]\n\treturn phone""",
                "key_get_data_input_func_process": "username",
            },
            "mobileOtp": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_phone",
                "object_source": "profile",
                "func_transform_string": """def process(phone):\n\tif phone.startswith("+"):\n\t\tphone=phone[1:]\n\tif phone.startswith("84"):\n\t\tphone="0"+phone[2:]\n\treturn phone""",
                "key_get_data_input_func_process": "mobileOtp",
            },
            "type": {"use_default": True, "value_default": "10001", "set_to_result": True},
            "packageCode": {"use_default": True, "value_default": "PRO57", "set_to_result": True},
            "packageType": {"use_default": True, "value_default": "PRO57", "set_to_result": True},
            "idNumber": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "idCard",
                "object_source": "card_information",
                "func_transform_string": """""",
            },
            "issueDate": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "dateOfIssuance",
                "object_source": "card_information",
                # "func_transform_string": """def process(value):\n\timport datetime\n\treturn datetime.datetime.strptime(value, "%d/%m/%Y").strftime("%Y-%m-%dT%H:%M:%S.000")""",
                # "key_get_data_input_func_process": "issueDate",
            },
            "issuePlace": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "placeOfIssuance",
                "object_source": "card_information",
            },
            "email": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_email",
                "object_source": "profile",
            },
            "address": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "address",
                "object_source": "card_information",
                "func_transform_string": """def process(address):\n\tfrom src.common.utils import utf8_to_ascii\n\treturn utf8_to_ascii(address)""",
                "key_get_data_input_func_process": "address",
            },
            "cifCore": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "CustId",
                "object_source": "response_create_cif",
            },
            "dateOfBirth": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "dateOfBirth",
                "object_source": "card_information",
            },
            "resident": {"use_default": True, "value_default": "N", "set_to_result": True},
            "nationality": {"use_default": True, "value_default": "Viet Nam", "set_to_result": True},
            "gender": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "gender",
                "object_source": "profile",
                "func_transform_string": """def process(gender):\n\treturn "M" if gender == 2 else "F" """,
                "key_get_data_input_func_process": "gender",
            },
            "emailReceived": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_email",
                "object_source": "profile",
            },
            "mobileContact": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_phone",
                "object_source": "profile",
                "func_transform_string": """def process(phone):\n\tif phone.startswith("+"):\n\t\tphone=phone[1:]\n\tif phone.startswith("84"):\n\t\tphone="0"+phone[2:]\n\treturn phone""",
                "key_get_data_input_func_process": "mobileContact",
            },
            "branchCodeCif": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "sol_id",
                "object_source": "staff_info",
            },
            "posCodeCif": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "sol_id",
                "object_source": "staff_info",
            },
            "accountNo": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "AcctId",
                "object_source": "response_create_account",
            },
            "rank": {"use_default": True, "value_default": "S", "set_to_result": True},
            "authenMethod": {"use_default": True, "value_default": "1", "set_to_result": True},
            "idType": {"use_default": True, "value_default": "CCUOC", "set_to_result": True},
            "mainCcy": {"use_default": True, "value_default": "VND", "set_to_result": True},
            "userAlias": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_phone",
                "object_source": "profile",
                "func_transform_string": """def process(phone):\n\tif phone.startswith("+"):\n\t\tphone=phone[1:]\n\tif phone.startswith("84"):\n\t\tphone="0"+phone[2:]\n\treturn phone""",
                "key_get_data_input_func_process": "mobileContact",
            },
            "staffCode": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "staff_code",
                "object_source": "staff_info",
            },
        },
        ConstantKeyConfigMappingFieldCrmEib.API_SEND_REQUEST_CREATE_CA_ACCT_ADD_INDIV: {
            "ChannelId": {"use_default": True, "value_default": "COR", "set_to_result": True},
            "CustId": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "CustId",
                "object_source": "response_create_cif",
            },
            "AcctCurr": {"use_default": True, "value_default": "VND", "set_to_result": True},
            "BranchId": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "sol_id",
                "object_source": "staff_info",
            },
            "GenLedgerSubHeadCode": {"use_default": True, "value_default": "42110", "set_to_result": True},
            "AcctName": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "name",
                "object_source": "card_information",
                "func_transform_string": """def process(name):\n\tfrom src.common.utils import utf8_to_ascii\n\treturn utf8_to_ascii(name).upper()""",
                "key_get_data_input_func_process": "AcctName",
            },
            "AcctShortName": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "key_get_data_input_func_process": "AcctName",
                "func_transform_string": """def process(name):\n\treturn name.upper().split(" ")[-1]""",
            },
            "AcctStmtMode": {"use_default": True, "value_default": "S", "set_to_result": True},
            "Type": {"use_default": True, "value_default": "Y", "set_to_result": True},
            "StartDt": {"use_default": True, "value_default": "31", "set_to_result": True},
            "HolStat": {"use_default": True, "value_default": "S", "set_to_result": True},
            "DespatchMode": {"use_default": True, "value_default": "N", "set_to_result": True},
            "DSAID": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "staff_code",
                "object_source": "staff_info",
                "func_transform_string": """""",
            },
            "NEXTINTERESTRUNDATECR": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "func_transform_string": """def process():\n\timport datetime\n\tdate_now=datetime.datetime.now(datetime.UTC)\n\tif date_now.day > 15:\n\t\treturn (date_now + datetime.timedelta(days=30)).replace(day=15).strftime("%d-%m-%Y")\n\treturn (date_now.replace(day=15)).strftime("%d-%m-%Y")""",
                "type_target_convert": "datetime",
            },
            "LOCALCALENDAR": {"use_default": True, "value_default": "N", "set_to_result": True},
            "INTCRACC": {"use_default": True, "value_default": "S", "set_to_result": True},
            "FREETEXT1": {"use_default": True, "value_default": "08-10-2019", "set_to_result": True},
            "FREETEXT2": {"use_default": True, "value_default": "31-12-2099", "set_to_result": True},
            "FREETEXT3": {"use_default": True, "value_default": "", "set_to_result": True},
            "FREETEXT4": {"use_default": True, "value_default": "", "set_to_result": True},
            "FREETEXT8": {"use_default": True, "value_default": "", "set_to_result": True},
            "FREECODE4": {"use_default": True, "value_default": "", "set_to_result": True},
            "FREECODE8": {"use_default": True, "value_default": "", "set_to_result": True},
            "FREECODE9": {"use_default": True, "value_default": "", "set_to_result": True},
            "INTRATECODE": {"use_default": True, "value_default": "DRKKH", "set_to_result": True},
            "ACCTPREFINTCR": {"use_default": True, "value_default": "0.2", "set_to_result": True},
            "INTCRACCNUM": {"use_default": True, "value_default": "", "set_to_result": True},
            "ACCTMGRID": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "username",
                "object_source": "staff_info",
                "func_transform_string": """def process(value):\n\treturn value.upper() if not value or '@' not in value else value.split('@')[0].upper()""",
                "key_get_data_input_func_process": "ACCTMGRID",
            },
            "MINBALIND": {"use_default": True, "value_default": "A", "set_to_result": True},
            "MINBAL": {"use_default": True, "value_default": "50000", "set_to_result": True},
            "PrefLangCode": {"use_default": True, "value_default": "VN", "set_to_result": True},
            "relation": {"use_default": True, "value_default": "O", "set_to_result": True},
            "title": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "gender",
                "object_source": "profile",
                "func_transform_string": """def process(gender):\n\treturn "MR." if gender == 2 else "MRS." """,
                "key_get_data_input_func_process": "gender",
            },
            "userName": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "name",
                "object_source": "profile",
                "func_transform_string": """def process(name):\n\tfrom src.common.utils import utf8_to_ascii\n\treturn utf8_to_ascii(name).upper().split(" ")[-1]""",
                "key_get_data_input_func_process": "userName",
            },
            "addrLine1": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "address",
                "object_source": "card_information",
                "func_transform_string": """def process(address):\n\tfrom src.common.utils import utf8_to_ascii\n\treturn utf8_to_ascii(address)""",
                "key_get_data_input_func_process": "address",
            },
            "notes": {"use_default": True, "value_default": "", "set_to_result": True},
            "phone": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_phone",
                "object_source": "profile",
                "func_transform_string": """def process(phone):\n\tif phone.startswith("+"):\n\t\tphone=phone[1:]\n\tif phone.startswith("84"):\n\t\tphone="0"+phone[2:]\n\treturn phone""",
                "key_get_data_input_func_process": "phone",
            },
            "email": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_email",
                "object_source": "profile",
            },
            "MINBALCRNCY": {"use_default": True, "value_default": "VND", "set_to_result": True},
        },
        ConstantKeyConfigMappingFieldCrmEib.SEND_EMAIL_RES_ACCOUNT_EDIGI: {
            "profile_name": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "name",
                "object_source": "profile",
                "func_transform_string": """def process(name):\n\tfrom src.common.utils import utf8_to_ascii\n\treturn utf8_to_ascii(name).upper()""",
                "key_get_data_input_func_process": "profile_name",
            },
            "profile_cif": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "CustId",
                "object_source": "response_create_cif",
            },
            "account_number": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "AcctId",
                "object_source": "response_create_account",
            },
            "date_account_active": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "action_time",
                "object_source": "response_create_account",
                "func_transform_string": """def process(action_time):\n\timport datetime\n\treturn datetime.datetime.strptime(action_time, "%Y-%m-%d %H:%M:%S.%f").strftime("%d/%m/%Y")""",
                "key_get_data_input_func_process": "date_account_active",
            },
            "username_edigi": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "username",
                "object_source": "data_request_res_edigi",
                "func_transform_string": """""",
            },
            "package_code": {"use_default": True, "value_default": "PRO57", "set_to_result": True},
        },
        ConstantKeyConfigMappingFieldCrmEib.SEND_DYNAMIC_EVENT_TO_PROFILE: {
            "action_time": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "action_time",
                "object_source": "data_send_email",
            },
            "tieu_de": {
                "use_default": True,
                "value_default": "Thông tin đăng ký tài khoản thanh toán tại Eximbank",
                "set_to_result": True,
            },
            "email_gui_den": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "primary_email",
                "object_source": "profile",
            },
            "email_gui_di": {"use_default": True, "value_default": "<EMAIL>", "set_to_result": True},
            "ten_nguoi_gui": {
                "use_default": True,
                "value_default": "Eximbank",
                "set_to_result": True,
            },
            "trang_thai_gui": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "status_send_email",
                "object_source": "data_send_email",
            },
            "cif": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "CustId",
                "object_source": "response_create_cif",
            },
            "so_tai_khoan": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "AcctId",
                "object_source": "response_create_account",
            },
            "ngay_bat_dau_hoat_dong": {
                "use_default": False,
                "value_default": "",
                "set_to_result": True,
                "field_source": "action_time",
                "object_source": "response_create_account",
                "func_transform_string": """def process(action_time):\n\timport datetime\n\treturn datetime.datetime.strptime(action_time, "%Y-%m-%d %H:%M:%S.%f").strftime("%Y-%m-%d")""",
                "key_get_data_input_func_process": "ngay_bat_dau_hoat_dong",
            },
            "han_muc_giao_dich": {
                "use_default": True,
                "value_default": *********,
                "set_to_result": True,
            },
            "event_key": {
                "use_default": True,
                "value_default": "gui_email_thong_bao_tai_khoan_thanh_cong_1750906540",
                "set_to_result": True,
            },
            "dynamic_event_id": {
                "use_default": True,
                "value_default": "685cb6ac53851c94b77a4740",
                "set_to_result": True,
            },
        },
        "code_decode": {
            "city": {
                "KHONG XAC DINH": "00",
                "THANH PHO HA NOI": "01",
                "TINH HA GIANG": "02",
                "TINH CAO BANG": "04",
                "TINH BAC KAN": "06",
                "TINH TUYEN QUANG": "08",
                "TINH LAO CAI": "10",
                "TINH DIEN BIEN": "11",
                "TINH LAI CHAU": "12",
                "TINH SON LA": "14",
                "TINH YEN BAI": "15",
                "TINH HOA BINH": "17",
                "TINH THAI NGUYEN": "19",
                "TINH LANG SON": "20",
                "TINH QUANG NINH": "22",
                "TINH BAC GIANG": "24",
                "TINH PHU THO": "25",
                "TINH VINH PHUC": "26",
                "TINH BAC NINH": "27",
                "TINH HAI DUONG": "30",
                "THANH PHO HAI PHONG": "31",
                "TINH HUNG YEN": "33",
                "TINH THAI BINH": "34",
                "TINH HA NAM": "35",
                "TINH NAM DINH": "36",
                "TINH NINH BINH": "37",
                "TINH THANH HOA": "38",
                "TINH NGHE AN": "40",
                "TINH HA TINH": "42",
                "TINH QUANG BINH": "44",
                "TINH QUANG TRI": "45",
                "TINH THUA THIEN HUE": "46",
                "THANH PHO DA NANG": "48",
                "TINH QUANG NAM": "49",
                "TINH QUANG NGAI": "51",
                "TINH BINH DINH": "52",
                "TINH PHU YEN": "54",
                "TINH KHANH HOA": "56",
                "TINH NINH THUAN": "58",
                "TINH BINH THUAN": "60",
                "TINH KON TUM": "62",
                "TINH GIA LAI": "64",
                "TINH DAK LAK": "66",
                "TINH DAK NONG": "67",
                "TINH LAM DONG": "68",
                "TINH BINH PHUOC": "70",
                "TINH TAY NINH": "72",
                "TINH BINH DUONG": "74",
                "TINH DONG NAI": "75",
                "TINH BA RIA - VUNG TAU": "77",
                "THANH PHO HO CHI MINH": "79",
                "TINH LONG AN": "80",
                "TINH TIEN GIANG": "82",
                "TINH BEN TRE": "83",
                "TINH TRA VINH": "84",
                "TINH VINH LONG": "86",
                "TINH DONG THAP": "87",
                "TINH AN GIANG": "89",
                "TINH KIEN GIANG": "91",
                "THANH PHO CAN THO": "92",
                "TINH HAU GIANG": "93",
                "TINH SOC TRANG": "94",
                "TINH BAC LIEU": "95",
                "TINH CA MAU": "96",
                "SIM 7": "99",
                "MIGRATION": "MIGR",
            }
        },
    }
    print("config_mapping_field_crm_eib :: ", config_mapping_field_crm_eib)

    filter_option = {"merchant_id": merchant_id, "type": "quick_sales"}
    exist_config_in_database = ConfigMappingFieldCrmToEibModel().find_one(filter_option)
    if exist_config_in_database:
        ConfigMappingFieldCrmToEibModel().update(filter_option, {"$set": config_mapping_field_crm_eib})
    else:
        ConfigMappingFieldCrmToEibModel().insert(config_mapping_field_crm_eib)
    print("Done")


if __name__ == "__main__":
    merchant_code = sys.argv[1] if len(sys.argv) > 1 else None

    if not merchant_code:
        print("Merchant code is required")
        sys.exit(1)

    config_mapping_field_crm_eib(merchant_code)
