#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 24/04/2025
"""


from src.common import ThirdPartyType
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


def update_config_by_type():

    config_update = [
        {
            "type": ThirdPartyType.SEND_REQUEST_QUICK_SALES_SEND_OTP_CONFIRM_LANDING_PAGE,
            "config": {
                "sms_landingpage_encryption_key": "B89vl8144vwFOHEb",
                "sms_access_token": "B89vl8144vwFOHEb",
                "client_id": "CRM_EKYC",
                "data_encrypted": """<SendOTPRequest><ReferenceNo>{log_id}</ReferenceNo><PhoneNumber>{phone_number}</PhoneNumber><Message>{message}</Message><OTP>{otp}</OTP><CIF></CIF><BranchCode></BranchCode><SendType>2</SendType></SendOTPRequest>""",
                "format_sms": "Nhap OTP {otp_str} de hoan tat dang ky san pham dich vu tai Eximbank. Tran trong cam on Quy khach.",
            },
        },
    ]
    for item in config_update:
        detail_config_by_type = ConfigInfoApiModel().find_one({"type": item.get("type")})
        if not detail_config_by_type:
            continue
        config_in_db = detail_config_by_type.get("config", {})
        if item.get("type") == ThirdPartyType.SEND_REQUEST_QUICK_SALES_SEND_OTP_CONFIRM_LANDING_PAGE:
            config_in_db.update(item.get("config"))

        ConfigInfoApiModel().update_by_set({"type": item.get("type")}, {"config": config_in_db}, upsert=True)


if __name__ == "__main__":
    update_config_by_type()
