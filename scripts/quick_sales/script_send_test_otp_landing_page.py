#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 28/04/2025
"""

import datetime
import uuid

from mobio.libs.logging import MobioLogging

from src.common import (
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    ConstantKeyConfigSendThirdParty,
    StatusCode,
    ThirdPartyType,
)
from src.common.requests_retry import RequestR<PERSON>ryAdapter
from src.common.utils import utf8_to_ascii
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper


def send_otp_confirm_landing_page(merchant_id, phone_number, profile_name, otp):
    log_id = str(uuid.uuid4())
    request_body = {"profile_name": profile_name, "otp": otp, "otp_str": "{OTP}"}
    config_send_sms, data = QuickSalesHelper()._build_config_send_sms_confirm_landing_page(
        merchant_id, log_id, phone_number, request_body
    )
    request_type = ThirdPartyType.SEND_REQUEST_QUICK_SALES_SEND_OTP_CONFIRM_LANDING_PAGE
    to_data = data.get(NOTIFICATION_STRUCTURE.TO)
    format_sms_send = config_send_sms.get(ConstantKeyConfigSendThirdParty.FORMAT_SMS)
    request_body["profile_name"] = utf8_to_ascii(request_body["profile_name"])

    access_token = config_send_sms.get(ConstantKeyConfigSendThirdParty.SMS_ACCESS_TOKEN) or ""
    encryption_key = config_send_sms.get(ConstantKeyConfigSendThirdParty.SMS_LANDINGPAGE_ENCRYPTION_KEY) or ""

    phone_number = to_data.get(NOTIFICATION_TO_STRUCTURE.PHONE_NUMBER)
    ClientId = config_send_sms.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
    sent_time = datetime.datetime.now(datetime.UTC).strftime(
        config_send_sms.get("format_date_time", "%Y-%m-%d %H:%M:%S")
    )

    otp_encrypted = QuickSalesHelper().encrypt_tripledes_ecb_pkcs7(encryption_key, otp)
    MobioLogging().info("send_request_sms_confirm_consent :: otp_encrypted: {}".format(otp_encrypted))
    request_body["otp"] = otp_encrypted

    message = format_sms_send.format(**request_body)

    data_encrypted = config_send_sms.get(ConstantKeyConfigSendThirdParty.DATA_ENCRYPTED).format(
        log_id=log_id, phone_number=phone_number, message=message, otp=otp_encrypted
    )
    # MobioLogging().info("EibSender::data:: %s" % plaintext)
    ciphertext = ThirdPartyEIB.encrypt(access_token, data_encrypted)
    ciphertext = ciphertext.decode("utf-8")

    # signature
    msg_signature = str(log_id) + str(ClientId) + sent_time + data_encrypted
    signed_cert_b64 = QuickSalesHelper().build_signature_by_plaintext(config_send_sms, msg_signature)

    payload = {
        "SendSMSRequestDto": {
            "RequestId": log_id,
            "ClientId": ClientId,
            "Timestamp": sent_time,
            "DataEncrypted": ciphertext,
            "Signature": signed_cert_b64,
        }
    }

    headers = QuickSalesHelper().build_headers(config_send_sms, payload)
    status_code = StatusCode.THIRD_PARTY_FAILURE
    data_response_json = None
    info_request = {"headers": headers, "payload": payload, "config": config_send_sms}
    uri = config_send_sms.get(ConstantKeyConfigSendThirdParty.URI)
    timeout = config_send_sms.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API) or 10
    with QuickSalesHelper().timer() as time_request:
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                uri, headers=headers, payload=payload, timeout=timeout, raise_for_status=True
            )

        except Exception as e:
            response = str(e)
            QuickSalesHelper()._log_request(
                request_type=request_type,
                config=config_send_sms,
                payload=payload,
                info_request=info_request,
                response=None,
                request_id=log_id,
                time_request=time_request,
                request_id_start=log_id,
            )
            return data_response_json, status_code, str(e)
    response_text = response.text
    # save log
    QuickSalesHelper()._log_request(
        request_type=request_type,
        config=config_send_sms,
        payload=payload,
        info_request=info_request,
        response=response_text,
        request_id=log_id,
        time_request=time_request,
        request_id_start=log_id,
    )
    return QuickSalesHelper().handle_response(
        "ResponseCode",
        "ResponseDesc",
        response,
        config_send_sms.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
        config_send_sms.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
    )


if __name__ == "__main__":
    # merchant_id = "57d559c1-39a1-4cee-b024-b953428b5ac8"
    merchant_id = "a0d4a74d-b475-4ef3-af7e-2d890ccce00b"
    data_response, status_send_otp, reasons = send_otp_confirm_landing_page(
        merchant_id, "0326951757", "Dao Duc Tung", "123456"
    )
    print(data_response, status_send_otp, reasons)
