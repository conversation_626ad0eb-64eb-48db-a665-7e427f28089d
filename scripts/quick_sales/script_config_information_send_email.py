#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 23/06/2025
"""

import argparse
import datetime
import sys

from src.common import ConstantStatusConfig, ThirdPartyType
from src.common.init_lib import lru_redis_cache
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.config_info_api_model import ConfigInfoApiModel

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("-merchant_code", "--merchant_code", help="Merchant Code", required=True)
    parser.add_argument("-host", "--host", help="Host", required=True)
    parser.add_argument("-cert_file", "--cert_file", help="Cert file", default=None)
    parser.add_argument("-key_file", "--key_file", help="Key file", default=None)
    parser.add_argument("-verify_smtp", "--verify_smtp", help="Verify smtp", default=None, type=bool)
    parser.add_argument("-port", "--port", help="Port", default=None, type=int)
    parser.add_argument("-ssl_type", "--ssl_type", help="Ssl type", default=None, type=int)
    parser.add_argument("-config_set", "--config_set", help="Config set", default=None, type=int)
    parser.add_argument("-auth_name", "--auth_name", help="Auth name", default=None)
    parser.add_argument("-auth_pass", "--auth_pass", help="Auth pass", default=None)
    parser.add_argument("-user_is_sender", "--user_is_sender", help="Auth pass", default=None, type=bool)
    parser.add_argument(
        "-path_template_send_email_success",
        "--path_template_send_email_success",
        help="Path template send email success",
        default=None,
    )
    parser.add_argument(
        "-path_template_send_email_fail",
        "--path_template_send_email_fail",
        help="Path template send email fail",
        default=None,
    )

    args = parser.parse_args()

    merchant_code = args.merchant_code

    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)

    if not merchant_id:
        print("Merchant not found")
        sys.exit(1)

    data_configs = [
        {
            "status": ConstantStatusConfig.ENABLE,
            "merchant_id": merchant_id,
            "type": ThirdPartyType.SEND_REQUEST_QUICK_SALES_SEND_EMAIL,
            "config": {
                "host": args.host,
                "cert_file": args.cert_file,
                "key_file": args.key_file,
                "verify_smtp": args.verify_smtp,
                "port": args.port,
                "ssl_type": args.ssl_type,
                "config_set": args.config_set,
                "auth_name": args.auth_name,
                "auth_pass": args.auth_pass,
                "user_is_sender": args.user_is_sender,
                "path_template_send_email_success": args.path_template_send_email_success,
                "path_template_send_email_fail": args.path_template_send_email_fail,
            },
            "action_time": datetime.datetime.now(datetime.UTC),
        }
    ]

    for data_config in data_configs:
        filter_option = {
            "status": data_config.get("status"),
            "merchant_id": data_config.get("merchant_id"),
            "type": data_config.get("type"),
        }

        ConfigInfoApiModel().update_by_set(filter_option, data_config, upsert=True)

        # Delete cache
        lru_redis_cache.delete_cache_by_pattern("*get_config_info_api*")
