#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 13/02/2025
"""


import datetime
import hashlib
import hmac
import logging
import re
import uuid
from typing import Any, Dict

from mobio.libs.logging import MobioLogging
from mobio.sdks.base.common.mobio_exception import CustomError

from src.common import (
    NOTIFICATION_BODY_STRUCTURE,
    NOTIFICATION_STRUCTURE,
    NOTIFICATION_TO_STRUCTURE,
    ConstantKeyConfigSendThirdParty,
    ConstantQuickSales,
    StatusCode,
    ThirdPartyType,
)
from src.common.requests_retry import RequestRetryAdapter
from src.common.utils import utf8_to_ascii
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.helpers.thirdparty.eib.send_request_check_customer_exist import (
    SendRequestCheckCustomerExist,
)
from src.helpers.thirdparty.quick_sales import BaseQuick<PERSON><PERSON><PERSON>elper
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


def create_hmac(config, plaintext):
    return hmac.new(config.get("hmac_secret").encode("utf-8"), plaintext.encode("utf-8"), hashlib.sha256).hexdigest()


class QuickSalesHelper(BaseQuickSalesHelper):
    logger = logging.getLogger(__name__)

    @classmethod
    def _build_request_body_check_cif(cls, merchant_id, profile_name, phone_number, identify):
        result = {
            "idCard": identify.get("identify_value"),
            "name": profile_name if profile_name else "",
            "dateOfBirth": "",
            "gender": "",
            "dateOfIssuance": "",
        }

        return result

    def build_request_body_check_aml(cls, profile_name, phone_number, identify):
        return {
            "FORMAT_TYPE": "TF",
            "F_BRANCH": "",
            "F_TRX_REF": "",
            "F_TRX_TYPE": "",
            "F_CURRENCY": "VND",
            "F_AMOUNT": "",
            "F_ISSUER_NAME": profile_name,
            "F_ISSUER_ID": identify.get("identify_value"),
            "F_ISSUER_CATEGORY": "",
            "F_ISSUER_ADDRESS": "",
            "F_ISSUER_CITY": "",
            "F_ISSUER_COUNTRY": "",
            "F_FROM_BANK": "EXIMBANK",
            "F_TO_BANK": "",
            "F_BEN_NAME": "",
            "F_BEN_ID": "",
            "F_BEN_CATEGORY": "",
            "F_BEN_ADDRESS": "",
            "F_BEN_CITY": "",
            "F_BEN_COUNTRY": "",
        }

    def _build_body_config_check_unique_id(self, merchant_id, profile_name, phone_number, identify):
        config = ConfigInfoApiModel().get_config_info_api_check_unique_id(merchant_id)
        if not config:
            raise CustomError("Not config send check customer exist")
        messageDateTime = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%dT%H:%M:%S.000")
        request_body = {
            "requestUUID": str(uuid.uuid4()),
            "messageDateTime": messageDateTime,
            "uniqueidType": "CCUOC",
            "uniqueId": "{}".format(identify.get("identify_value")),
            "name": "",
            "dob": "",
            "taxId": "",
        }

        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {NOTIFICATION_BODY_STRUCTURE.DATA_REQUEST: request_body}
            }
        }

        return data, config

    def check_unique_id_by_profile(self, log_id_start, merchant_id, profile_name, phone_number, identify):
        log_id = str(uuid.uuid4())

        data_request, config_request = self._build_body_config_check_unique_id(
            merchant_id, profile_name, phone_number, identify
        )
        data_response, status_code, reasons = ThirdPartyEIB.check_unique_id(
            log_id, config_request, data_request, account_id=None, merchant_id=merchant_id, log_id_start=log_id_start
        )
        MobioLogging().info(
            "_send_request_check_customer_exist :: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        # if status_code == StatusCode.THIRD_PARTY_FAILURE:
        #     raise Exception(reasons)
        cifInformation = None
        if data_response and data_response.get("errorCode") in ["99", "93"]:
            cifInformation = data_response.get("errorDesc")

        if cifInformation:
            return True
        return False

    def check_cif_by_information_profile(self, log_id_start, merchant_id, profile_name, phone_number, identify):
        """
        Cập nhật logic ngày 29/04/2025: https://mobiojsc.sg.larksuite.com/wiki/SslpwGnrKiXO8NkXD5Bl0PeGggh?table=tblIAM0iorADgxkI&view=vewgSUfPi5
        - Bỏ API check AML ở phần check xem profile đã tồn tại chưa
        - Call api 4.10 là check unique id để check xem cif đã tồn tại chưa
        - Sau đó call API 4.9 để lấy thông tin ghi vào database để giữ nguyên luồng cũ.
        """
        log_id = str(uuid.uuid4())
        MobioLogging().info(f"log_id_start: {log_id_start}, log_id: {log_id}")
        MobioLogging().info(f"profile_name: {profile_name}, phone_number: {phone_number}, identify: {identify}")
        body_request = self._build_request_body_check_cif(merchant_id, profile_name, phone_number, identify)

        data_request, config_request = SendRequestCheckCustomerExist._build_config_send_request_check_customer_exist(
            merchant_id, body_request
        )

        response_customer_exist, status_code, reasons = ThirdPartyEIB.check_customer_exists(
            merchant_id, log_id, config_request, data_request, log_id_start=log_id_start
        )
        MobioLogging().info(
            "_send_request_check_customer_exist :: log_id: {}, status_code: {}, reasons: {}".format(
                log_id, status_code, reasons
            )
        )
        # if status_code == StatusCode.THIRD_PARTY_FAILURE:
        #     raise Exception(reasons)

        result_check_aml = True
        result_check_cif = self.check_unique_id_by_profile(
            log_id_start, merchant_id, profile_name, phone_number, identify
        )

        # body_request_aml = self._build_request_body_check_aml(profile_name, phone_number, identify)

        # response_aml, status_code_aml, reasons_aml, log_id_aml = self.check_aml(merchant_id, body_request_aml)
        # MobioLogging().info(
        #     "_send_request_check_aml :: log_id: {}, response_aml: {}, status_code: {}, reasons: {}".format(
        #         log_id_aml, response_aml, status_code_aml, reasons_aml
        #     )
        # )

        # if status_code_aml == StatusCode.SUCCESS:
        #     result_check_aml = True
        return result_check_aml, result_check_cif, response_customer_exist

    def check_aml(self, merchant_id, payload):
        """Lock or unlock a Quick Sales account.

        Args:

        Returns:
            Tuple containing (response_data, status_code, message)
        """
        request_id = str(uuid.uuid4())
        logger = MobioLogging()
        logger.info(f"Processing check aml request. Request ID: {request_id}")

        # Get configuration and validate
        config_request = ConfigInfoApiModel().get_config_info_api(
            merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_CHECK_AML
        )
        if not config_request:
            raise ValueError("Missing Quick Sales check aml configuration")

        request_type = ThirdPartyType.SEND_REQUEST_QUICK_SALES_CHECK_AML
        headers = self.build_headers(config_request, payload)
        info_request = {"headers": headers, "payload": payload}

        uri = config_request.get(ConstantKeyConfigSendThirdParty.URI)
        timeout = config_request.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API) or 10

        with self.timer() as time_request:
            try:
                response = RequestRetryAdapter().retry_a_post_request(
                    url=uri, headers=headers, payload=payload, timeout=timeout
                )
                self.logger.info(f"Response received: {response.text}")

            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"Request failed: {error_msg}", exc_info=True)
                log_id = self._log_request(
                    request_type=request_type,
                    config=config_request,
                    info_request=info_request,
                    payload=payload,
                    response=None,
                    request_id=request_id,
                    time_request=time_request,
                    request_id_start=request_id,
                )
                return None, StatusCode.THIRD_PARTY_FAILURE, error_msg, log_id

        log_id = self._log_request(
            request_type=request_type,
            config=config_request,
            info_request=info_request,
            payload=payload,
            response=response,
            request_id=request_id,
            time_request=time_request,
            request_id_start=request_id,
        )
        try:
            reasons = ""
            data_response = response.json()
            ResultMsg = data_response.get("ResultMsg")
            if ResultMsg == "OK":
                status_code = StatusCode.SUCCESS
            else:
                status_code = StatusCode.THIRD_PARTY_FAILURE
                reasons = ResultMsg

        except Exception as e:  # noqa
            MobioLogging().error("ThirdPartyEIB :: handle_response :: {}".format(e))
            result = response.text
            if not re.search("Successful|0</ResponseCode", result):
                reasons = result
                status_code = StatusCode.THIRD_PARTY_FAILURE
            else:
                status_code = StatusCode.SUCCESS
        return data_response, status_code, reasons, log_id

    def lock_unlock_account(self, merchant_id, account_action_id, account_number, action):
        """Lock or unlock a Quick Sales account.

        Args:
            merchant_id: ID of the merchant
            account_action_id: ID of the account action
            account_number: Account number to lock/unlock
            action: Action to perform (lock/unlock)

        Returns:
            Tuple containing (response_data, status_code, message)
        """
        request_id = str(uuid.uuid4())
        logger = MobioLogging()
        logger.info(f"Processing lock/unlock request. Request ID: {request_id}")

        # Get configuration and validate
        config_request = ConfigInfoApiModel().get_config_info_api(
            merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_LOCK_UNLOCK_ACCOUNT
        )
        if not config_request:
            raise ValueError("Missing Quick Sales lock/unlock account configuration")

        # Build request payload
        payload = self._build_request_payload_lock_unlock_account(
            request_id=request_id,
            merchant_id=merchant_id,
            account_action_id=account_action_id,
            account_number=account_number,
            action=action,
            config=config_request,
        )
        request_type = ThirdPartyType.SEND_REQUEST_QUICK_SALES_LOCK_UNLOCK_ACCOUNT
        headers = self.build_headers(config_request, payload)
        info_request = {"headers": headers, "payload": payload}

        uri = config_request.get(ConstantKeyConfigSendThirdParty.URI)
        timeout = config_request.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API) or 10

        with self.timer() as time_request:
            try:
                response = RequestRetryAdapter().retry_a_post_request(
                    url=uri, headers=headers, payload=payload, timeout=timeout
                )
                self.logger.info(f"Response received: {response.text}")

            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"Request failed: {error_msg}", exc_info=True)
                self._log_request(
                    request_type=request_type,
                    config=config_request,
                    info_request=info_request,
                    payload=payload,
                    response=None,
                    request_id=request_id,
                    time_request=time_request,
                    request_id_start=request_id,
                )
                return None, StatusCode.THIRD_PARTY_FAILURE, error_msg

        log_id = self._log_request(
            request_type=request_type,
            config=config_request,
            info_request=info_request,
            payload=payload,
            response=response,
            request_id=request_id,
            time_request=time_request,
            request_id_start=request_id,
        )
        result = self.handle_response(
            "ErrorCode",
            "ErrorDesc",
            response,
            config_request.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config_request.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )
        return *result, log_id

    def _build_request_payload_lock_unlock_account(
        self, request_id, merchant_id, account_action_id, account_number, action, config: Dict
    ):
        """Build the request payload."""
        function = ConstantQuickSales.ConstantActionAccount.MAPPING_ACTION_CRM_TO_QUICK_SALES.get(action)
        sol_id = InternalAdminHelper().get_sol_id_by_account_id(merchant_id, account_action_id)

        return {
            "RequestUUID": request_id,
            "ChannelId": config.get(ConstantKeyConfigSendThirdParty.CHANNEL),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime(
                config.get("format_date_time", self.FORMAT_DATE_TIME)
            ),
            "SolId": sol_id,
            "AccountNumber": account_number,
            "Function": function,
            "SchemeCode": config.get("scheme_code", "10101"),
            "CIFId": "",
            "HOVerifyFlag": "",
        }

    def send_otp_confirm_landing_page(self, merchant_id, phone_number, profile_name, otp):
        log_id = str(uuid.uuid4())
        request_body = {"profile_name": profile_name, "otp": otp, "otp_str": "{OTP}"}
        config_send_sms, data = self._build_config_send_sms_confirm_landing_page(
            merchant_id, log_id, phone_number, request_body
        )
        request_type = ThirdPartyType.SEND_REQUEST_QUICK_SALES_SEND_OTP_CONFIRM_LANDING_PAGE
        to_data = data.get(NOTIFICATION_STRUCTURE.TO)
        format_sms_send = config_send_sms.get(ConstantKeyConfigSendThirdParty.FORMAT_SMS)
        request_body["profile_name"] = utf8_to_ascii(request_body["profile_name"])

        access_token = config_send_sms.get(ConstantKeyConfigSendThirdParty.SMS_ACCESS_TOKEN) or ""
        encryption_key = config_send_sms.get(ConstantKeyConfigSendThirdParty.SMS_LANDINGPAGE_ENCRYPTION_KEY) or ""

        phone_number = to_data.get(NOTIFICATION_TO_STRUCTURE.PHONE_NUMBER)
        ClientId = config_send_sms.get(ConstantKeyConfigSendThirdParty.CLIENT_ID)
        sent_time = datetime.datetime.now(datetime.UTC).strftime(
            config_send_sms.get("format_date_time", "%Y-%m-%d %H:%M:%S")
        )

        otp_encrypted = self.encrypt_tripledes_ecb_pkcs7(encryption_key, otp)
        MobioLogging().info("send_request_sms_confirm_consent :: otp_encrypted: {}".format(otp_encrypted))
        request_body["otp"] = otp_encrypted

        message = format_sms_send.format(**request_body)

        data_encrypted = config_send_sms.get(ConstantKeyConfigSendThirdParty.DATA_ENCRYPTED).format(
            log_id=log_id, phone_number=phone_number, message=message, otp=otp_encrypted
        )
        # MobioLogging().info("EibSender::data:: %s" % plaintext)
        ciphertext = ThirdPartyEIB.encrypt(access_token, data_encrypted)
        ciphertext = ciphertext.decode("utf-8")

        # signature
        msg_signature = str(log_id) + str(ClientId) + sent_time + data_encrypted
        signed_cert_b64 = self.build_signature_by_plaintext(config_send_sms, msg_signature)

        payload = {
            "SendSMSRequestDto": {
                "RequestId": log_id,
                "ClientId": ClientId,
                "Timestamp": sent_time,
                "DataEncrypted": ciphertext,
                "Signature": signed_cert_b64,
            }
        }

        headers = self.build_headers(config_send_sms, payload)
        status_code = StatusCode.THIRD_PARTY_FAILURE
        data_response_json = None
        info_request = {"headers": headers, "payload": payload, "config": config_send_sms}
        uri = config_send_sms.get(ConstantKeyConfigSendThirdParty.URI)
        timeout = config_send_sms.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API) or 10
        with self.timer() as time_request:
            try:
                response = RequestRetryAdapter().retry_a_post_request(
                    uri, headers=headers, payload=payload, timeout=timeout, raise_for_status=True
                )

            except Exception as e:
                response = str(e)
                self._log_request(
                    request_type=request_type,
                    config=config_send_sms,
                    payload=payload,
                    info_request=info_request,
                    response=None,
                    request_id=log_id,
                    time_request=time_request,
                    request_id_start=log_id,
                )
                return data_response_json, status_code, str(e)
        response_text = response.text
        # save log
        self._log_request(
            request_type=request_type,
            config=config_send_sms,
            payload=payload,
            info_request=info_request,
            response=response_text,
            request_id=log_id,
            time_request=time_request,
            request_id_start=log_id,
        )
        return self.handle_response(
            "ResponseCode",
            "ResponseDesc",
            response,
            config_send_sms.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config_send_sms.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def _build_config_send_sms_confirm_landing_page(self, merchant_id, log_id, phone_number, request_body):
        config_send_sms = ConfigInfoApiModel().get_config_info_api(
            merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_SEND_OTP_CONFIRM_LANDING_PAGE
        )
        if not config_send_sms:
            raise ValueError("Not config send sms")
        format_sms_send = config_send_sms.get(ConstantKeyConfigSendThirdParty.FORMAT_SMS)
        request_body["profile_name"] = utf8_to_ascii(request_body["profile_name"])
        content = format_sms_send.format(**request_body)

        MobioLogging().info("send_request_sms_confirm_consent :: update content: %s", content)
        data = {
            NOTIFICATION_STRUCTURE.TO: {
                NOTIFICATION_TO_STRUCTURE.BODY: {
                    NOTIFICATION_BODY_STRUCTURE.CONTENT: content,
                },
                NOTIFICATION_TO_STRUCTURE.PHONE_NUMBER: phone_number,
            }
        }
        return config_send_sms, data

    def send_request_create_cif(self, merchant_id, staff_id, action_time, data_register_cif: Dict[str, Any]):
        """Creates a new CIF in the QuickSales system.

        Sends a request to the QuickSales API to register a new customer and obtain a CIF code.
        This method handles request preparation, sending, error handling, and comprehensive logging.

        Args:
            merchant_id: Unique identifier for the merchant
            staff_id: ID of the staff member initiating the request
            action_time: ISO-formatted timestamp of the action
            data_register_cif: Customer registration data containing personal information

        Returns:
            A tuple containing:
                - data: Response data with CIF information if successful, None otherwise
                - status_code: HTTP status code indicating success or failure
                - message: Descriptive message about the result or error
                - log_request_id: ID of the logged request for traceability

        Response data structure when successful:
        ```
        {
            "CIFCode": "string",        # Generated CIF code
            "ResponseCode": "string",   # API response code
            "ResponseDesc": "string",   # Description of the response
            "Status": "string",         # Transaction status
            "TransactionId": "string"   # Unique transaction identifier
        }
        ```
        """
        request_id = str(uuid.uuid4())
        self.logger.info(
            "Initiating CIF creation request - merchant_id: %s, staff_id: %s, request_id: %s",
            merchant_id,
            staff_id,
            request_id,
        )

        # Step 1: Retrieve and validate configuration
        config = self._get_create_cif_config(merchant_id)
        if not config:
            self.logger.error("Missing configuration for CIF creation - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, "Not config request create cif", None

        # Step 2: Prepare request payload and headers
        try:
            payload = self._build_request_payload_create_cif(
                request_id, merchant_id, staff_id, action_time, data_register_cif, config
            )
            headers = self.build_headers(config, payload)
        except Exception as e:
            self.logger.exception("Failed to build CIF creation payload - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, f"Payload preparation failed: {str(e)}", None

        # Step 3: Send request with proper error handling
        uri = config.get(ConstantKeyConfigSendThirdParty.URI)
        timeout = int(config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10))

        request_meta = {
            "request_type": ThirdPartyType.SEND_REQUEST_QUICK_SALES_CREATE_CIF,
            "config": config,
            "payload": payload,
            "info_request": {"headers": headers, "payload": payload},
            "request_id": request_id,
            "request_id_start": request_id,
        }

        with self.timer() as elapsed:
            response, error = self._execute_api_request_post(uri, headers, payload, timeout)

            if error:
                log_id = self._log_request(**request_meta, response=str(error), time_request=elapsed)
                return None, StatusCode.THIRD_PARTY_FAILURE, str(error), log_id

        # Step 4: Log the complete transaction
        log_id = self._log_request(**request_meta, response=response, time_request=elapsed)

        # Step 5: Process and validate the response
        result = self._process_create_cif_response(config, response)
        return (*result, log_id)

    def _get_create_cif_config(self, merchant_id):
        """Retrieves QuickSales CIF creation configuration for a merchant.

        Args:
            merchant_id: Merchant identifier

        Returns:
            Configuration dict if found, None otherwise
        """
        return ConfigInfoApiModel().get_config_info_api(merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_CREATE_CIF)

    def _get_config_res_account_edigi(self, merchant_id):
        """Retrieves QuickSales account creation configuration for a merchant.

        Args:
            merchant_id: Merchant identifier

        Returns:
            Configuration dict if found, None otherwise
        """
        return ConfigInfoApiModel().get_config_info_api(
            merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_RES_ACCOUNT_EDIGI
        )

    def _get_config_insert_in4_dsa(self, merchant_id):
        """Retrieves QuickSales account creation configuration for a merchant.

        Args:
            merchant_id: Merchant identifier

        Returns:
            Configuration dict if found, None otherwise
        """
        return ConfigInfoApiModel().get_config_info_api(
            merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_INSERT_IN4_DSA
        )

    def _execute_api_request_post(self, uri, headers: Dict[str, str], payload: Dict[str, Any], timeout: int):
        """Executes an API request with error handling.

        Args:
            uri: API endpoint
            headers: Request headers
            payload: Request payload
            timeout: Request timeout in seconds

        Returns:
            Tuple of (response, error) where error is None if successful
        """
        try:
            response = RequestRetryAdapter().retry_a_post_request(
                uri, headers=headers, payload=payload, timeout=timeout, raise_for_status=True
            )
            return response, None
        except Exception as e:
            self.logger.exception("API request failed: {}".format(str(e)))
            return None, e

    def _process_create_cif_response(self, config: Dict[str, Any], response: Any):
        """Processes the CIF creation response.

        Args:
            config: API configuration
            response: API response object

        Returns:
            Tuple of (data, status_code, message)
        """
        return self.handle_response(
            "ErrorCode",
            "ErrorDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def _build_request_payload_create_cif(
        self, request_id, merchant_id, staff_id, action_time, data_register_cif, config
    ):
        """Builds the request payload for CIF creation.

        Args:
            request_id: Unique request identifier
            merchant_id: Merchant identifier
            staff_id: Staff identifier
            action_time: Action timestamp
            data_register_cif: Customer registration data

        Returns:
            Formatted payload for the QuickSales API
        """

        payload = {
            "RequestUUID": request_id,
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime(
                config.get("format_date_time", self.FORMAT_DATE_TIME)
            ),
        }
        payload.update(data_register_cif)

        return payload

    def _get_branch_code_of_staff_id(self, merchant_id, staff_id):
        """Gets the branch code for a staff member.

        Args:
            merchant_id: Merchant identifier
            staff_id: Staff identifier

        Returns:
            Branch code string
        """
        try:
            return InternalAdminHelper().get_sol_id_by_account_id(merchant_id, staff_id) or ""
        except Exception as e:
            self.logger.warning("Failed to get branch code: %s", str(e))
            return ""

    @classmethod
    def _convert_to_format_date(cls, date_str):
        """Formats a date string to YYYY-MM-DD.

        Args:
            date_str: Input date string

        Returns:
            Formatted date string
        """
        if not date_str:
            return ""

        try:
            # Handle different possible formats
            for fmt in ("%d/%m/%Y", "%Y-%m-%d", "%Y/%m/%d", "%d-%m-%Y"):
                try:
                    dt = datetime.datetime.strptime(date_str, fmt)
                    return dt.strftime("%Y-%m-%d")
                except ValueError:
                    continue

            return date_str  # Return original if parsing fails
        except Exception:
            return ""

    def send_request_res_account_edigi(
        self, merchant_id, staff_id, action_time, data_res_account_edigi: Dict[str, Any]
    ):
        """Sends a request to create an account in the QuickSales system.

        Args:
            merchant_id: Unique identifier for the merchant
            staff_id: ID of the staff member initiating the request
            action_time: ISO-formatted timestamp of the action
            data_res_account_edigi: Data for the account creation request

        Returns:
            A tuple containing:
                - data: Response data with account information if successful, None otherwise
                - status_code: HTTP status code indicating success or failure
                - message: Descriptive message about the result or error
                - log_request_id: ID of the logged request for traceability
        """
        request_id = str(uuid.uuid4())
        self.logger.info(
            "Initiating account creation request - merchant_id: %s, staff_id: %s, request_id: %s",
            merchant_id,
            staff_id,
            request_id,
        )

        # Step 1: Retrieve and validate configuration
        config = self._get_config_res_account_edigi(merchant_id)
        if not config:
            self.logger.error("Missing configuration for account creation - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, "Not config request create account", None
        config.update({"hmac_secret": "ecR625dWozUbagJ9bSsp.sh9opr56h.oiudhh7ks@"})

        # Step 2: Prepare request payload and headers
        try:
            payload = self._build_request_payload_res_account_edigi(
                request_id=request_id,
                merchant_id=merchant_id,
                staff_id=staff_id,
                action_time=action_time,
                data_res_account_edigi=data_res_account_edigi,
                config=config,
            )
            headers = self.build_headers(config, payload)
        except Exception as e:
            self.logger.exception("Failed to build account creation payload - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, f"Payload preparation failed: {str(e)}", None

        # Step 3: Send request with proper error handling
        uri = config.get(ConstantKeyConfigSendThirdParty.URI)
        timeout = int(config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10))

        request_meta = {
            "request_type": ThirdPartyType.SEND_REQUEST_QUICK_SALES_RES_ACCOUNT_EDIGI,
            "config": config,
            "payload": payload,
            "info_request": {"headers": headers, "payload": payload},
            "request_id": request_id,
            "request_id_start": request_id,
        }

        with self.timer() as elapsed:
            response, error = self._execute_api_request_post(uri, headers, payload, timeout)

            if error:
                log_id = self._log_request(**request_meta, response=str(error), time_request=elapsed)
                return None, StatusCode.THIRD_PARTY_FAILURE, str(error), log_id

        # Step 4: Log the complete transaction
        log_id = self._log_request(**request_meta, response=response, time_request=elapsed)

        # Step 5: Process and validate the response
        result = self._process_res_account_edigi_response(config, response)
        return (*result, log_id)

    def _process_res_account_edigi_response(self, config: Dict[str, Any], response: Any):
        """Processes the account creation response.

        Args:
            config: API configuration
            response: API response object

        Returns:
            Tuple of (data, status_code, message)
        """
        return self.handle_response(
            "code",
            "desc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def _build_request_payload_res_account_edigi(
        self,
        request_id,
        merchant_id,
        staff_id,
        action_time,
        data_res_account_edigi: Dict[str, Any],
        config: Dict[str, Any],
    ):
        """Builds the request payload for account creation.

        Args:
            request_id: Unique request identifier
            merchant_id: Merchant identifier
            staff_id: Staff identifier
            action_time: ISO-formatted timestamp of the action
            data_res_account_edigi: Data for the account creation request

        Returns:
            Formatted payload for the QuickSales API
        """

        msg_signature = (
            str(data_res_account_edigi.get("name"))
            + str(data_res_account_edigi.get("accountNo"))
            + str(data_res_account_edigi.get("cifCore"))
            + str(data_res_account_edigi.get("userAlias"))
            + str(data_res_account_edigi.get("username"))
            + str(data_res_account_edigi.get("mobileOtp"))
            + str(data_res_account_edigi.get("emailReceived"))
        )

        signed_cert_b64 = create_hmac(config, msg_signature)

        data_res_account_edigi["signature"] = signed_cert_b64
        return data_res_account_edigi

    def send_request_insert_in4_dsa(self, merchant_id, staff_id, action_time, data_insert_in4_dsa: Dict[str, Any]):
        """Sends a request to insert data into IN4 DSA.

        Args:
            merchant_id: Unique identifier for the merchant
            staff_id: ID of the staff member initiating the request
            action_time: ISO-formatted timestamp of the action
            data_insert_in4_dsa: Data for the insert request
            Cấu trúc:
                sol_id: Mã số phòng giao dịch
                cif_id: Mã số khách hàng
                flg_id: Mã số loại hình khách hàng
                emp_id: Mã số nhân viên
                rbo_id: Mã số đối tác
            Sample:
            {
                "sol_id": "**********",
                "cif_id": "**********",
                "flg_id": "**********",
                "emp_id": "**********",
                "rbo_id": "**********",
            }

        Returns:
            A tuple containing:
                - data: Response data if successful, None otherwise
                - status_code: HTTP status code indicating success or failure
                - message: Descriptive message about the result or error
                - log_request_id: ID of the logged request for traceability
        """
        request_id = str(uuid.uuid4())
        self.logger.info(
            "Initiating insert data into IN4 DSA request - merchant_id: %s, staff_id: %s, request_id: %s",
            merchant_id,
            staff_id,
            request_id,
        )

        # Step 1: Retrieve and validate configuration
        config = self._get_config_insert_in4_dsa(merchant_id)
        if not config:
            self.logger.error("Missing configuration for insert data into IN4 DSA - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, "Not config request insert data into IN4 DSA", None

        # Step 2: Prepare request payload and headers
        try:
            payload = self._build_request_payload_insert_in4_dsa(
                request_id, merchant_id, staff_id, action_time, data_insert_in4_dsa, config
            )
            headers = self.build_headers(config, payload)
        except Exception as e:
            self.logger.exception("Failed to build insert data into IN4 DSA payload - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, f"Payload preparation failed: {str(e)}", None

        # Step 3: Send request with proper error handling
        uri = config.get(ConstantKeyConfigSendThirdParty.URI)
        timeout = int(config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10))

        request_meta = {
            "request_type": ThirdPartyType.SEND_REQUEST_QUICK_SALES_INSERT_IN4_DSA,
            "config": config,
            "payload": payload,
            "info_request": {"headers": headers, "payload": payload},
            "request_id": request_id,
            "request_id_start": request_id,
        }

        with self.timer() as elapsed:
            response, error = self._execute_api_request_post(uri, headers, payload, timeout)

            if error:
                log_id = self._log_request(**request_meta, response=str(error), time_request=elapsed)
                return None, StatusCode.THIRD_PARTY_FAILURE, str(error), log_id

        # Step 4: Log the complete transaction
        log_id = self._log_request(**request_meta, response=response, time_request=elapsed)

        # Step 5: Process and validate the response
        result = self._process_insert_in4_dsa_response(config, response)
        return (*result, log_id)

    def _process_insert_in4_dsa_response(self, config: Dict[str, Any], response: Any):
        """Processes the insert data into IN4 DSA response.

        Args:
            config: API configuration
            response: API response object

        Returns:
            Tuple of (data, status_code, message)
        """
        return self.handle_response(
            "ErrorCode",
            "ErrorDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def _build_request_payload_insert_in4_dsa(
        self, request_id, merchant_id, staff_id, action_time, data_insert_in4_dsa, config
    ):
        """Builds the request payload for insert data into IN4 DSA.

        Args:
            request_id: Unique request identifier
            merchant_id: Merchant identifier
            staff_id: Staff identifier
            action_time: ISO-formatted timestamp of the action
            data_insert_in4_dsa: Data for the insert request

        Returns:
            Formatted payload for the QuickSales API
        """
        return {
            "requestUUID": request_id,
            "messageDateTime": datetime.datetime.now(datetime.UTC).strftime(
                config.get("format_date_time", self.FORMAT_DATE_TIME)
            ),
            "solId": data_insert_in4_dsa.get("sol_id", ""),
            "cifId": data_insert_in4_dsa.get("cif_id", ""),
            "flgId": data_insert_in4_dsa.get("flg_id", "0"),
            "empId": data_insert_in4_dsa.get("emp_id", ""),
            "rboId": data_insert_in4_dsa.get("rbo_id"),
        }

    def _get_config_ca_acct_add_indiv(self, merchant_id):
        """Retrieves the configuration for CA account addition.

        Args:
            merchant_id: Unique identifier for the merchant

        Returns:
            Configuration dictionary if found, None otherwise
        """
        return ConfigInfoApiModel().get_config_info_api(
            merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_CA_ACCT_ADD_INDIV
        )

    def _build_request_payload_ca_acct_add_indiv(
        self, request_id, merchant_id, staff_id, action_time, data_ca_acct_add_indiv, config
    ):
        """Builds the request payload for CA account addition.

        Args:
            request_id: Unique request identifier
            merchant_id: Merchant identifier
            staff_id: Staff identifier
            action_time: ISO-formatted timestamp of the action
            data_ca_acct_add_indiv: Data for the account creation request

        Returns:
            Formatted payload for the QuickSales API
        """
        payload = {
            "RequestUUID": request_id,
            "ChannelId": "CRM",
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime(
                config.get("format_date_time", self.FORMAT_DATE_TIME)
            ),
        }
        payload.update(data_ca_acct_add_indiv)
        return payload

    def send_request_ca_acct_add_indiv(
        self, merchant_id, staff_id, action_time, data_ca_acct_add_indiv: Dict[str, Any]
    ):
        """Sends a request to create an account in the QuickSales system.

        Args:
            merchant_id: Unique identifier for the merchant
            staff_id: ID of the staff member initiating the request
            action_time: ISO-formatted timestamp of the action
            data_ca_acct_add_indiv: Data for the account creation request

        Returns:
            A tuple containing:
                - data: Response data if successful, None otherwise
                - status_code: HTTP status code indicating success or failure
                - message: Descriptive message about the result or error
                - log_request_id: ID of the logged request for traceability
        """
        request_id = str(uuid.uuid4())
        self.logger.info(
            "Initiating account creation request - merchant_id: %s, staff_id: %s, request_id: %s",
            merchant_id,
            staff_id,
            request_id,
        )

        # Step 1: Retrieve and validate configuration
        config = self._get_config_ca_acct_add_indiv(merchant_id)
        if not config:
            self.logger.error("Missing configuration for account creation - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, "Not config request create account", None

        # Step 2: Prepare request payload and headers
        try:
            payload = self._build_request_payload_ca_acct_add_indiv(
                request_id, merchant_id, staff_id, action_time, data_ca_acct_add_indiv, config
            )
            headers = self.build_headers(config, payload)
        except Exception as e:
            self.logger.exception("Failed to build account creation payload - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, f"Payload preparation failed: {str(e)}", None

        # Step 3: Send request with proper error handling
        uri = config.get(ConstantKeyConfigSendThirdParty.URI)
        timeout = int(config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10))

        request_meta = {
            "request_type": ThirdPartyType.SEND_REQUEST_QUICK_SALES_CA_ACCT_ADD_INDIV,
            "config": config,
            "payload": payload,
            "info_request": {"headers": headers, "payload": payload},
            "request_id": request_id,
            "request_id_start": request_id,
        }

        with self.timer() as elapsed:
            response, error = self._execute_api_request_post(uri, headers, payload, timeout)

            if error:
                log_id = self._log_request(**request_meta, response=str(error), time_request=elapsed)
                return None, StatusCode.THIRD_PARTY_FAILURE, str(error), log_id

        # Step 4: Log the complete transaction
        log_id = self._log_request(**request_meta, response=response, time_request=elapsed)

        # Step 5: Process and validate the response
        result = self._process_ca_acct_add_indiv_response(config, response)
        return (*result, log_id)

    def _process_ca_acct_add_indiv_response(self, config: Dict[str, Any], response: Any):
        """Processes the CA account addition response.

        Args:
            config: API configuration
            response: API response object

        Returns:
            Tuple of (data, status_code, message)
        """
        return self.handle_response(
            "ErrorCode",
            "ErrorDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

    def get_loai_details(self, merchant_id, staff_id, data_get_loai_details: Dict[str, Any]):
        """Sends a request to get loai details.

        Args:
            merchant_id: Unique identifier for the merchant
            staff_id: ID of the staff member initiating the request
            data_get_loai_details: Data for the get loai details request

        Returns:
            A tuple containing:
                - data: Response data if successful, None otherwise
                - status_code: HTTP status code indicating success or failure
                - message: Descriptive message about the result or error
                - log_request_id: ID of the logged request for traceability
        """
        request_id = str(uuid.uuid4())
        self.logger.info(
            "Initiating get loai details request - merchant_id: %s, staff_id: %s, request_id: %s",
            merchant_id,
            staff_id,
            request_id,
        )

        # Step 1: Retrieve and validate configuration
        config = self._get_config_get_loai_details(merchant_id)
        if not config:
            self.logger.error("Missing configuration for get loai details - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, "Not config request get loai details", None

        # Step 2: Prepare request payload and headers
        try:
            payload = self._build_request_payload_get_loai_details(request_id, data_get_loai_details, config)
            headers = self.build_headers(config, payload)
        except Exception as e:
            self.logger.exception("Failed to build get loai details payload - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, f"Payload preparation failed: {str(e)}", None

        # Step 3: Send request with proper error handling
        uri = config.get(ConstantKeyConfigSendThirdParty.URI)
        timeout = int(config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10))

        request_meta = {
            "request_type": ThirdPartyType.SEND_REQUEST_QUICK_SALES_GET_LOAI_DETAILS,
            "config": config,
            "payload": payload,
            "info_request": {"headers": headers, "payload": payload},
            "request_id": request_id,
            "request_id_start": request_id,
        }

        with self.timer() as elapsed:
            response, error = self._execute_api_request_post(uri, headers, payload, timeout)

            if error:
                log_id = self._log_request(**request_meta, response=str(error), time_request=elapsed)
                return None, StatusCode.THIRD_PARTY_FAILURE, str(error), log_id

        # Step 4: Log the complete transaction
        log_id = self._log_request(**request_meta, response=response, time_request=elapsed)

        # Step 5: Process and validate the response
        result = self._process_get_loai_details_response(config, response)
        return (*result, log_id)

    def _process_get_loai_details_response(self, config: Dict[str, Any], response: Any):
        """Processes the get loai details response.

        Args:
            config: API configuration
            response: API response object

        Returns:
            Tuple of (data, status_code, message)
        """
        data_response, status_code, message = self.handle_response(
            "ErrorCode",
            "ErrorDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            return None, status_code, message

        lst_account = []
        total = data_response.get("Number", 0)
        result_data = {
            "total": total,
            "data": lst_account,
        }

        acc_details = data_response.get("AcctDetails")
        if acc_details:
            for acc_detail in acc_details:
                lst_account.append(
                    {
                        "premium_number_type_name": acc_detail.get("TENLOAI"),
                        "premium_number_type_code": acc_detail.get("MALOAI"),
                        "fee": acc_detail.get("SOTIEN"),
                    }
                )

        return result_data, status_code, message

    def _get_config_get_loai_details(self, merchant_id):
        """Retrieves the configuration for get loai details.

        Args:
            merchant_id: Unique identifier for the merchant

        Returns:
            Configuration dictionary if found, None otherwise
        """
        return ConfigInfoApiModel().get_config_info_api(
            merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_GET_LOAI_DETAILS
        )

    def _build_request_payload_get_loai_details(
        self, request_id, data_get_loai_details: Dict[str, Any], config: Dict[str, Any]
    ):
        """Builds the request payload for get loai details.

        Args:
            request_id: Unique request identifier
            merchant_id: Merchant identifier
            staff_id: Staff identifier
            action_time: ISO-formatted timestamp of the action
            data_get_loai_details: Data for the get loai details request

        Returns:
            Formatted payload for the QuickSales API
        """
        return {
            "RequestUUID": request_id,
            "ChannelId": config.get(ConstantKeyConfigSendThirdParty.CHANNEL),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime(
                config.get("format_date_time", self.FORMAT_DATE_TIME)
            ),
            "NUM": data_get_loai_details.get("num"),
            "LOAI1": data_get_loai_details.get("loai1"),
            "LOAI2": data_get_loai_details.get("loai2"),
        }

    def get_danh_sach_so_dep(self, merchant_id, data_filter_premium_account: Dict[str, Any]):
        """Sends a request to filter premium account.

        Args:
            merchant_id: Unique identifier for the merchant
            data_filter_premium_account: Data for the filter premium account request

        Returns:
            A tuple containing:
                - data: Response data if successful, None otherwise
                - status_code: HTTP status code indicating success or failure
                - message: Descriptive message about the result or error
                - log_request_id: ID of the logged request for traceability
        """
        request_id = str(uuid.uuid4())
        self.logger.info(
            "Initiating filter premium account request - merchant_id: %s, request_id: %s",
            merchant_id,
            request_id,
        )

        # Step 1: Retrieve and validate configuration
        config = self._get_config_filter_premium_account(merchant_id)
        if not config:
            self.logger.error("Missing configuration for filter premium account - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, "Not config request filter premium account", None

        # Step 2: Prepare request payload and headers
        try:
            payload = self._build_request_payload_filter_premium_account(
                request_id, data_filter_premium_account, config
            )
            headers = self.build_headers(config, payload)
        except Exception as e:
            self.logger.exception("Failed to build filter premium account payload - merchant_id: %s", merchant_id)
            return None, StatusCode.THIRD_PARTY_FAILURE, f"Payload preparation failed: {str(e)}", None

        # Step 3: Send request with proper error handling
        uri = config.get(ConstantKeyConfigSendThirdParty.URI)
        timeout = int(config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10))

        request_meta = {
            "request_type": ThirdPartyType.SEND_REQUEST_QUICK_SALES_GET_DANH_SACH_SO_DEP,
            "config": config,
            "payload": payload,
            "info_request": {"headers": headers, "payload": payload},
            "request_id": request_id,
            "request_id_start": request_id,
        }

        with self.timer() as elapsed:
            response, error = self._execute_api_request_post(uri, headers, payload, timeout)

            if error:
                log_id = self._log_request(**request_meta, response=str(error), time_request=elapsed)
                return None, StatusCode.THIRD_PARTY_FAILURE, str(error), log_id

        # Step 4: Log the complete transaction
        log_id = self._log_request(**request_meta, response=response, time_request=elapsed)

        # Step 5: Process and validate the response
        result = self._process_filter_premium_account_response(config, response)
        return (*result, log_id)

    def _process_filter_premium_account_response(self, config: Dict[str, Any], response: Any):
        """Processes the filter premium account response.

        Args:
            config: API configuration
            response: API response object

        Returns:
            Tuple of (data, status_code, message)
        """
        data_response, status_code, message = self.handle_response(
            "ErrorCode",
            "ErrorDesc",
            response,
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
            config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
        )

        if status_code == StatusCode.THIRD_PARTY_FAILURE:
            return None, status_code, message

        total = data_response.get("Number", 0)
        lst_account = []
        result_data = {
            "total": total,
            "data": lst_account,
        }

        acc_details = data_response.get("AcctDetails", [])
        if acc_details:
            for acc_detail in acc_details:
                lst_account.append(
                    {
                        "premium_account_number": acc_detail.get("STK"),
                        "premium_account_type_code": acc_detail.get("MALOAI"),
                        "premium_account_type_name": acc_detail.get("TENLOAI"),
                        "fee": acc_detail.get("SOTIEN"),
                    }
                )

        return result_data, status_code, message

    def _get_config_filter_premium_account(self, merchant_id):
        """Retrieves the configuration for filter premium account.

        Args:
            merchant_id: Unique identifier for the merchant

        Returns:
            Configuration dictionary if found, None otherwise
        """
        return ConfigInfoApiModel().get_config_info_api(
            merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_GET_DANH_SACH_SO_DEP
        )

    def _build_request_payload_filter_premium_account(
        self, request_id, data_filter_premium_account: Dict[str, Any], config: Dict[str, Any]
    ):
        """Builds the request payload for filter premium account.

        Args:
            request_id: Unique request identifier
            data_filter_premium_account: Data for the filter premium account request
            config: API configuration

        Returns:
            Formatted payload for the QuickSales API
        """
        return {
            "RequestUUID": request_id,
            "ChannelId": config.get(ConstantKeyConfigSendThirdParty.CHANNEL),
            "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime(
                config.get("format_date_time", self.FORMAT_DATE_TIME)
            ),
            "NUM": data_filter_premium_account.get("num"),
            "LOAI": data_filter_premium_account.get("loai"),
            "STKCHON": data_filter_premium_account.get("search", ""),
        }


if __name__ == "__main__":
    merchant_id = "a0d4a74d-b475-4ef3-af7e-2d890ccce00b"
    staff_id = "648b83c5-8df1-417c-9de1-e288d26fdd8c"
    action_time = datetime.datetime.now(datetime.UTC).strftime("%Y-%m-%d %H:%M:%S.%f")
    response_res_edigi_account, status_code_res_edigi_account, message_res_edigi_account, log_request_id = QuickSalesHelper().send_request_res_account_edigi(
        merchant_id,
        staff_id,
        action_time,
        {
            "idNumber": "************",
            "address": "Thon 5, Xuan Quan, Van Giang, Hung Yen",
            "accountNo": "*********",
            "name": "LE PHUONG ANH",
            "username": "**********",
            "mobileOtp": "**********",
            "type": "Y",
            "packageCode": "01",
            "packageType": "01",
            "issueDate": "2022-08-22T00:00:00.000",
            "issuePlace": "Cuc truong cuc CS dk QLCT va Du lieu QG ve dan cu",
            "email": "<EMAIL>",
            "cifCore": "*********",
            "dateOfBirth": "1997-01-04T00:00:00.000",
            "resident": "N",
            "nationality": "Vi\u1ec7t Nam",
            "gender": "F",
            "emailReceived": "<EMAIL>",
            "mobileContact": "**********",
            "branchCodeCif": "1502",
            "posCodeCif": "1502",
            "rank": "P",
            "authenMethod": "",
            "idType": "CCUOC",
            "mainCcy": "VND",
            "userAlias": "lam.tqk",
            "staffCode": "*********",
        },
    )
    print("response_res_edigi_account: %s" % response_res_edigi_account)
    print("status_code_res_edigi_account: %s" % status_code_res_edigi_account)
    print("message_res_edigi_account: %s" % message_res_edigi_account)
    print("log_request_id: %s" % log_request_id)