#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 24/06/2025
"""

import datetime
from src.message_queue.kafka.producer.push_message_to_topic import Producer

if __name__ == "__main__":
    time_now = datetime.datetime.now(datetime.timezone.utc).timestamp()
    message_data = {
        "code": "success",
        "data": {
            "profile_id": "9971b4d0-9190-46c2-871d-f19d1058d9ff",
            "merchant_id": ["57d559c1-39a1-4cee-b024-b953428b5ac8"],
        },
        "data_callback": {
            "event_key": "gui_email_thong_bao_tai_khoan_thanh_cong_1750745570",
            "event_data": {
                "action_time": time_now,
                "tieu_de": "ABC",
                "email_dui_den": "ABC",
                "email_gui_di": "ABC",
                "ten_nguoi_gui": "ABC",
                "trang_thai_gui": "ABC",
                "cif": "ABC",
                "so_tai_khoan": 5000000,
                "ngay_bat_dau_hoat_dong": "2024-01-01",
                "han_muc_giao_dich": 5000000,
            },
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "tracking_code": "",
            "dynamic_event_id": "685a41e25085abcd1d1835b0",
        },
    }

    Producer().send_message_to_topic("profile-event-dynamic-p2", message_data)
