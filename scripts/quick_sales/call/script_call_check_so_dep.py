#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 04/06/2025
"""

import datetime
import sys
import uuid

from src.common import ConstantKeyConfigSendThirdParty, StatusCode, ThirdPartyType
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.helpers.thirdparty.quick_sales.call import QuickSalesHelper
from src.models.mongo.config_info_api_model import ConfigInfoApiModel


def call_check_so_dep(merchant_code):
    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Please provide merchant_code as argument.")
        sys.exit(1)

    config = ConfigInfoApiModel().get_config_info_api(
        merchant_id, ThirdPartyType.SEND_REQUEST_QUICK_SALES_GET_LOAI_DETAILS
    )
    if not config:
        print("Please provide merchant_code as argument.")
        sys.exit(1)

    request_id = str(uuid.uuid4())
    body_request = {
        "RequestUUID": request_id,
        "ChannelId": config.get(ConstantKeyConfigSendThirdParty.CHANNEL),
        "MessageDateTime": datetime.datetime.now(datetime.UTC).strftime(
            config.get("format_date_time", "%Y-%m-%dT%H:%M:%S.000")
        ),
        "FORACID": "**********",
        "CIFID": "",
    }

    try:
        headers = QuickSalesHelper().build_headers(config, body_request)
    except Exception as e:
        print("Failed to build filter premium account payload - merchant_id: %s", merchant_id)
        return None, StatusCode.THIRD_PARTY_FAILURE, f"Payload preparation failed: {str(e)}", None

    # Step 3: Send request with proper error handling
    uri = config.get(ConstantKeyConfigSendThirdParty.URI).replace("GetLoaiDetails", "CheckSoDep")
    timeout = int(config.get(ConstantKeyConfigSendThirdParty.TIMEOUT_API, 10))

    request_meta = {
        "request_type": ThirdPartyType.SEND_REQUEST_QUICK_SALES_CHECK_SO_DEP,
        "config": config,
        "payload": body_request,
        "info_request": {"headers": headers, "payload": body_request},
        "request_id": request_id,
        "request_id_start": request_id,
    }

    with QuickSalesHelper().timer() as elapsed:
        response, error = QuickSalesHelper()._execute_api_request_post(uri, headers, body_request, timeout)

        if error:
            log_id = QuickSalesHelper()._log_request(**request_meta, response=str(error), time_request=elapsed)
            return None, StatusCode.THIRD_PARTY_FAILURE, str(error), log_id

    # Step 4: Log the complete transaction
    log_id = QuickSalesHelper()._log_request(**request_meta, response=response, time_request=elapsed)

    data_response, status_code, message = QuickSalesHelper().handle_response(
        "ErrorCode",
        "ErrorDesc",
        response,
        config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_ERROR),
        config.get(ConstantKeyConfigSendThirdParty.MAPPING_CODE_MESSAGE_SUCCESS),
    )
    return data_response, status_code, message, log_id


if __name__ == "__main__":
    merchant_code = sys.argv[1] if len(sys.argv) > 1 else None
    if not merchant_code:
        print("Please provide merchant_code as argument.")
        sys.exit(1)
    print(call_check_so_dep(merchant_code))
