#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 26/06/2025
"""

from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.message_queue.kafka.consumer.csm_quick_sales_res_edigi_account import (
    CsmResEdigiAccConsumer,
)

if __name__ == "__main__":
    merchant_code = "EIB"
    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)

    if not merchant_id:
        print("Merchant not found")
    payload_send = {
        "profile_name": "Tungdd",
        "profile_cif": "**********",
        "account_number": "**********",
        "date_account_active": "2025-06-26",
        "username_edigi": "tungdd",
        "package_code": "**********",
    }
    path_template_send_email_res_edigi_account = "/home/<USER>/projects/MobileBackend/resources/templates/quick_sales/fail_res_edigi.html"
    email_to = "<EMAIL>"
    log_id = "**********"

    x = CsmResEdigiAccConsumer.send_email_to_profile(
        merchant_id, payload_send, path_template_send_email_res_edigi_account, log_id, email_to
    )
    print(x)
