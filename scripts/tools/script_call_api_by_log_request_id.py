#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 25/02/2025
"""

import base64
import datetime
import hashlib
import sys

import requests
from bson import ObjectId

from src.common import ConstantKeyConfigSendThirdParty
from src.helpers.thirdparty.eib.call import ThirdPartyEIB
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)


def create_signature_api_413(image_data, request_id, timestamp):
    image_liveness = image_data.replace("data:image/png;base64,", "")
    image_liveness = base64.b64decode(image_liveness)
    # 1. Lấy hình liveness và loại bỏ prefix base64

    hash_object = hashlib.sha256(image_liveness)

    # 2. <PERSON><PERSON><PERSON>i mã dữ liệu Base64 từ chuỗi hình ảnh

    sum_sha_256_image = hash_object.hexdigest()

    print("sum_sha_256_image :: ", sum_sha_256_image)
    # 3. Tạo chuỗi ký hiệu (signature data)
    sign_data = request_id + timestamp + sum_sha_256_image  # sb là dữ liệu Chuỗi checksumSHA256 của ảnh
    print("sign_data :: ", sign_data)
    return sign_data


if __name__ == "__main__":
    log_id = sys.argv[1] if len(sys.argv) > 1 else None
    if not log_id:
        print("Error: Log ID is missing.")
        exit(1)

    log_data = LogRequestSendThirdPartyModel().find_one({"_id": ObjectId(log_id)}, {"info_request": 1})
    if not log_data:
        print("Error: Log not found.")
        exit(1)

    info_request = log_data.get("info_request", {})
    # Extract parts
    headers = info_request.get("headers", {})
    # config = info_request.get("config", {})
    merchant_id = "a0d4a74d-b475-4ef3-af7e-2d890ccce00b"
    config = ConfigInfoApiModel().get_config_info_api_send_request_verify_image(merchant_id)

    payload = info_request.get("payload", {})
    jwt_key = config.get(ConstantKeyConfigSendThirdParty.JWT_KEY)
    jwt_secret = config.get(ConstantKeyConfigSendThirdParty.JWT_SECRET)
    jwt_audience = config.get(ConstantKeyConfigSendThirdParty.JWT_AUDIENCE)
    jwt_token = ThirdPartyEIB.create_jwt(jwt_key, jwt_secret, jwt_audience)

    headers["X-Authorization"] = "Bearer " + jwt_token
    sent_time = datetime.datetime.now(datetime.UTC)
    sent_timestamp = sent_time.strftime("%Y-%m-%d %H:%M:%S")
    dataRequest = payload.get("data")
    requestId = payload.get("requestId")

    payload = {
        "requestId": requestId,
        "channel": config.get(ConstantKeyConfigSendThirdParty.CHANNEL),
        "timestamp": sent_timestamp,
        "data": dataRequest,
    }
    plan_text = create_signature_api_413(payload.get("data").get("image"), requestId, payload.get("timestamp"))
    signed_cert_b64 = ThirdPartyEIB.gen_signature_vnpay(config, plan_text)
    print("signed_cert_b64 :: ", signed_cert_b64)
    payload["signature"] = signed_cert_b64

    headers = ThirdPartyEIB.build_headers(config, payload)
    info_request = {"headers": headers, "payload": payload, "config": config}
    # Validate necessary keys
    if not config.get("uri"):
        print("Error: URI is missing in config.")
        exit(1)
    config_uri = config["uri"]
    non_header_keys = [
        "uri",
        "mapping_code_message_error",
        "mapping_code_message_success",
        "timeout_api",
        "application_request",
        "api_key",
    ]

    response = requests.post(config_uri, headers=headers, json=payload)
    print(response.status_code)
    print(response.text)
