#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 28/02/2025
"""

import sys

from bson import ObjectId

from src.helpers.thirdparty.cic.call import ThirdPartyCICHelper
from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)

if __name__ == "__main__":
    log_id = sys.argv[1] if len(sys.argv) > 1 else None
    if not log_id:
        print("Error: Log ID is missing.")
        exit(1)

    log_data = LogRequestSendThirdPartyModel().find_one({"_id": ObjectId(log_id)}, {"info_request": 1, "config": 1})
    if not log_data:
        print("Error: Log not found.")
        exit(1)

    info_request = log_data.get("info_request", {})
    # Extract parts
    headers = info_request.get("headers", {})
    # config = info_request.get("config", {})
    config_api = log_data.get("config", {})

    config_uri = config_api.get("uri", "").replace("https://", "http://")
    import requests
    try:
        response = requests.post(config_uri, headers=headers, json=info_request.get("payload", {}))
        print(response.status_code)
        print(response.text)
    except Exception as e:
        print(e)