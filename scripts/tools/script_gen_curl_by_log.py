#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 14/01/2025
"""
import json
import shlex
import sys

from bson import ObjectId

from src.models.mongo.log_request_send_third_party_model import (
    LogRequestSendThirdPartyModel,
)

# Define which config keys are not headers

if __name__ == "__main__":
    log_id = sys.argv[1] if len(sys.argv) > 1 else None
    if not log_id:
        print("Error: Log ID is missing.")
        exit(1)

    log_data = LogRequestSendThirdPartyModel().find_one({"_id": ObjectId(log_id)}, {"info_request": 1, "config": 1})
    if not log_data:
        print("Error: Log not found.")
        exit(1)

    non_header_keys = [
        "uri",
        "mapping_code_message_error",
        "mapping_code_message_success",
        "timeout_api",
        "application_request",
        "api_key",
    ]

    info_request = log_data.get("info_request", {})
    # Extract parts
    headers = info_request.get("headers", {})
    config = log_data.get("config", {})
    payload = info_request.get("payload", {})

    # Validate necessary keys
    if not config.get("uri"):
        print("Error: URI is missing in config.")
        exit(1)
    config_uri = config["uri"]

    # Combine headers: headers from "headers" section and additional headers from "config"
    all_headers = headers.copy()
    for key, value in config.items():
        if key not in non_header_keys:
            all_headers[key] = value

    # Build curl command with line breaks for readability
    curl_command = f'curl -X POST "{config_uri}" \\\n'
    for key, value in all_headers.items():
        curl_command += f'  -H "{key}: {value}" \\\n'
    # Add payload
    payload_json = json.dumps(payload)
    escaped_payload = shlex.quote(payload_json)
    curl_command += f"  -d {escaped_payload} \\\n"
    # Add timeout
    timeout = config.get("timeout_api", 10)
    curl_command += f" -k"

    print("curl command: ", curl_command)
