"""
python3 ./scripts/script_config_api_info_exchange_rate.py --merchant_id "a0d4a74d-b475-4ef3-af7e-2d890ccce00b" --domain "http://crm-dev.eximbank.com.vn:2006" --client_id "54f2ba79-1ba0-4196-8245-fd3b8932bce1" --auth_name "CRM" --auth_pass "crm@13579" --jwt_key "*********" --jwt_secret "*********" --jwt_audience "http://crm-dev.eximbank.com.vn:2006" --pem_pass_phrase "*********" --certificates_path "/home/<USER>/Desktop/eibmobilebackend/scripts/certificates" --digest_alg "SHA256"
"""

import argparse
import datetime
from src.common import ConstantKeyConfigSendThirdParty, ThirdPartyType
from src.helpers.internal.mobio.admin import InternalAdminHelper
from src.models.mongo.config_info_api_model import ConfigInfoApiModel
import sys

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("-merchant_code", "--merchant_code", help="Merchant code", required=True)
    parser.add_argument("-domain", "--domain", help="Domain", required=True)
    parser.add_argument("-auth_name", "--auth_name", help="Auth name", required=True)
    parser.add_argument("-auth_pass", "--auth_pass", help="Auth name", required=True)
    parser.add_argument("-client_id", "--client_id", help="Client id", required=True)
    parser.add_argument("-jwt_key", "--jwt_key", help="JWT KEY", required=True)
    parser.add_argument("-jwt_secret", "--jwt_secret", help="JWT Secret", required=True)
    parser.add_argument("-jwt_audience", "--jwt_audience", help="JWT Audience", required=True)
    parser.add_argument("-pem_pass_phrase", "--pem_pass_phrase", help="PEM Pass Phrase", required=True)
    parser.add_argument("-certificates_path", "--certificates_path", help="Certificates Path", required=True)
    parser.add_argument("-digest_alg", "--digest_alg", help="Digest Alg", required=True)
    
    args = parser.parse_args()

    merchant_code = args.merchant_code
    merchant_id = InternalAdminHelper().get_merchant_id_by_merchant_code(merchant_code)
    if not merchant_id:
        print("Merchant not found")
        sys.exit(1)
    
    
    domain = args.domain
    auth_name = args.auth_name
    auth_pass = args.auth_pass
    client_id = args.client_id
    jwt_key = args.jwt_key
    jwt_secret = args.jwt_secret
    jwt_audience = args.jwt_audience
    pem_pass_phrase = args.pem_pass_phrase
    certificates_path = args.certificates_path
    digest_alg = args.digest_alg


    data = {
        "merchant_id" : merchant_id,
        "status" : 1,
        "type" : ThirdPartyType.SEND_REQUEST_EXCHANGE_RATE,
        "action_time" : datetime.datetime.now(datetime.UTC),
        "config" : {
            "domain" : domain,
            "uri" : "{}/api/v1/InqExchangeRate_Esale",
            ConstantKeyConfigSendThirdParty.AUTH_NAME : auth_name,
            ConstantKeyConfigSendThirdParty.AUTH_PASS : auth_pass,
            ConstantKeyConfigSendThirdParty.JWT_KEY: jwt_key,
            ConstantKeyConfigSendThirdParty.JWT_SECRET: jwt_secret,
            ConstantKeyConfigSendThirdParty.JWT_AUDIENCE: jwt_audience,
            ConstantKeyConfigSendThirdParty.X_CLIENT_ID: client_id,
            ConstantKeyConfigSendThirdParty.PEM_PASS_PHRASE: pem_pass_phrase,
            ConstantKeyConfigSendThirdParty.CERTIFICATES_PATH: certificates_path,
            ConstantKeyConfigSendThirdParty.DIGEST_ALG: digest_alg,
            "mapping_code_message_error" : {
                "99": "Lỗi hệ thống",
                "-1": "Chưa có bảng tỷ giá ngày hiện tại",
                "1" : "Thông tin input thiếu"
            },
            "mapping_code_message_success" : {
                "0" : "Thành công"
                
            }
        }
    }
    filter_option = {
        "status": data.get("status"),
        "merchant_id": data.get("merchant_id"),
        "type": data.get("type"),
    }

    ConfigInfoApiModel().update_by_set(filter_option, data, upsert=True)
    print("==Done==")