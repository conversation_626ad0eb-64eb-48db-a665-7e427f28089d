def process(value):
    import datetime
    from src.common.utils import utf8_to_ascii

    value = value[0] if isinstance(value, list) else {}
    addr_line1 = value.get("detail") if value.get("detail") else value.get("value_cccd") if value.get("value_cccd") else "."
    AddrDtls = []
    for address_category in ["Mailing", "Home", "Work"]:
        AddrDtls.append(
            {
                "AddrLine1": utf8_to_ascii(utf8_to_ascii(addr_line1)),
                "AddrLine2": ".",
                "AddrLine3": ".",
                "AddrCategory": address_category,
                "City": "00",
                "Country": "VN",
                "HoldMailFlag": "N",
                "HouseNum": ".",
                "PrefAddr": "Y" if address_category == "Mailing" else "N",
                "PrefFormat": "FREE_TEXT_FORMAT",
                "StartDt": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.000"),
                "State": "MIGR",
                "StreetName": ".",
                "StreetNum": ".",
                "PostalCode": "XXXXX",
                "FreeTextLabel": utf8_to_ascii(addr_line1),
            }
        )

    return AddrDtls
