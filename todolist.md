# Kế hoạch nâng cấp Form Khách hàng

## Phân tích hiện trạng
- Form hiện tại có bố cục đơn giản với 2 cột sử dụng flexbox
- Thiếu phân nhóm trường thông tin rõ ràng
- Thiếu validation client-side chi tiết
- Giao diện chưa hiện đại và responsive
- Thiếu khối cấu hình API Endpoint

## Các nhóm trường thông tin
1. **Thông tin cá nhân**
   - Họ và tên
   - Ng<PERSON>y sinh
   - <PERSON><PERSON><PERSON> cấp CCCD
   - Nơi sinh
   - Số CCCD
   - CIF
   - Dân tộc
   - Giới tính

2. **Thông tin gia đình**
   - Tên cha
   - Tên mẹ

3. **Thông tin địa chỉ**
   - Địa chỉ chi tiết
   - Số CMND cũ

4. **Cài đặt xác thực**
   - <PERSON><PERSON><PERSON> thực khuôn mặt
   - X<PERSON><PERSON> thực CMND
   - Kiểm tra thẻ CCCD với CO6
   - <PERSON><PERSON>m tra AML
   - Tồn tại CIF với CMND
   - D<PERSON> liệu mặc định
   - Fail khi tạo CIF
   - Fail khi tạo tài khoản Thanh toán
   - Fail khi tạo tài khoản EDI

## Bảng màu hiện đại
- **Màu chính**: #4361ee (Xanh dương hiện đại)
- **Màu phụ**: #3f37c9 (Xanh dương đậm cho hover)
- **Màu thành công**: #4cc9f0 (Xanh lam sáng)
- **Màu lỗi**: #f72585 (Hồng rực rỡ)
- **Màu nền**: #f8f9fa (Xám nhạt)
- **Màu nền thẻ**: #ffffff (Trắng)
- **Màu chữ**: #333333 (Xám đậm)
- **Màu viền**: #e0e0e0 (Xám nhạt)

## Font chữ
- Font chính: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif

## Các cải tiến chính

### 1. Cấu trúc lại bố cục
- Sử dụng CSS Grid cho bố cục chính
- Bố cục 2 cột trên desktop (min-width: 768px)
- Bố cục 1 cột trên mobile
- Nhóm các trường thông tin có liên quan vào các fieldset với tiêu đề rõ ràng

### 2. Cải thiện giao diện
- Bo góc nhẹ (8px)
- Đổ bóng tinh tế
- Khoảng cách nhất quán
- Cải thiện kiểu dáng input với trạng thái focus
- Nút bấm với thứ bậc trực quan tốt hơn

### 3. Validation client-side
- Kiểm tra các trường bắt buộc
- Kiểm tra định dạng ngày tháng (DD/MM/YYYY)
- Kiểm tra định dạng số CCCD (9 hoặc 12 chữ số)
- Hiển thị thông báo lỗi dưới mỗi trường
- Vô hiệu hóa nút gửi cho đến khi tất cả các trường hợp lệ

### 4. Thêm biểu tượng
- Biểu tượng người dùng cho tên
- Biểu tượng thẻ ID cho số CCCD
- Biểu tượng tài liệu cho CIF
- Biểu tượng lịch cho ngày sinh và ngày cấp
- Biểu tượng vị trí cho nơi sinh và địa chỉ
- Biểu tượng nhóm người dùng cho dân tộc
- Biểu tượng người dùng cho giới tính, tên cha, tên mẹ

### 5. Hiệu ứng chuyển động
- Thời lượng: 0.3s cho hầu hết các phần tử
- Easing: ease-in-out cho chuyển động mượt mà
- Thuộc tính để animate: background-color, border-color, box-shadow, transform

### 6. Cải thiện khả năng tiếp cận
- Đảm bảo mỗi input đều có label liên kết chính xác
- Thêm thuộc tính ARIA:
  - aria-describedby cho thông báo lỗi
  - aria-required cho trường bắt buộc
  - role="region" cho các phần form
  - aria-labelledby cho tiêu đề phần
  - Thứ tự tab hợp lý
  - Chỉ báo focus cho điều hướng bàn phím

### 7. Khối cấu hình API Endpoint
- Khối có thể thu gọn/mở rộng ở đầu form
- Trường input cho domain API
- Nút "Lưu Cấu hình"
- Lưu vào localStorage
- Thông báo toast khi lưu thành công

### 8. Responsive
- Mobile (max-width: 576px): Bố cục 1 cột, input full-width
- Tablet (max-width: 768px): Bố cục 2 cột với cột hẹp hơn
- Desktop (min-width: 769px): Bố cục 2 cột đầy đủ

## Kết quả cuối cùng
Một form khách hàng hiện đại, đáp ứng, dễ sử dụng với đầy đủ tính năng validation và cấu hình API endpoint.