FROM BE_BASE_COMPILE_IMAGE as compile-image

WORKDIR /home/<USER>/projects/MobileBackend
ADD . /home/<USER>/projects/MobileBackend

RUN echo $'[global] \n\
index = http://************:8081/repository/pypi-proxy/pypi \n\
index-url = http://************:8081/repository/pypi-proxy/simple \n\
trusted-host = ************' > /etc/pip.conf

RUN pip3.11 install -r requirements.txt

# build cython (tùy từng module mở ra)
# RUN find src/ -type d -exec sh -c 'if [ ! -f {}/__init__.py ]; then touch {}/__init__.py; fi' \;
# RUN python3.11 setup.py build_ext -j4 --inplace
# RUN find src configs -type f \( -name '*.py' -o -name '*.c' \) -exec rm -f {} \;
# RUN rm -rf ./build

FROM BE_BASE_RUN_IMAGE as run-image

ENV LC_ALL=en_US.UTF-8 \
   MOBILE_BACKEND_HOME=/home/<USER>/projects/MobileBackend \
   MOBILE_BACKEND_FOLDER_NAME=MobileBackend \
   APPLICATION_DATA_DIR=/media/data/resources/ \
   APPLICATION_LOGS_DIR=/media/data/logs/daily/

ENV data_dir=$APPLICATION_DATA_DIR$MOBILE_BACKEND_FOLDER_NAME \
   log_dir=$APPLICATION_LOGS_DIR$MOBILE_BACKEND_FOLDER_NAME \
   monitor_log_dir=$APPLICATION_LOGS_DIR$MOBILE_BACKEND_FOLDER_NAME/monitor_logs/

RUN mkdir -p $data_dir $log_dir $monitor_log_dir

WORKDIR $MOBILE_BACKEND_HOME

COPY --from=compile-image $MOBILE_BACKEND_HOME $MOBILE_BACKEND_HOME

COPY --from=compile-image /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=compile-image /usr/local/bin/uwsgi /usr/local/bin/uwsgi

RUN chmod +x *.sh

CMD tail -f /dev/null