<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo <PERSON>ăn Chỉnh Icon với Text</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        
        .method {
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .method h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .method.old {
            border-color: #f72585;
            background-color: #fdf2f8;
        }
        
        .method.new {
            border-color: #4cc9f0;
            background-color: #f0f9ff;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .input-with-icon {
            position: relative;
            display: block;
        }
        
        /* Phương pháp cũ - căn giữa theo chiều cao */
        .old .input-with-icon i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            width: 18px;
            height: 18px;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
        }
        
        /* Phương pháp mới - căn chỉnh với text */
        .new .input-with-icon i {
            position: absolute;
            left: 12px;
            top: 12px;
            color: #666;
            width: 18px;
            height: 18px;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
        }
        
        .input-with-icon i svg {
            width: 100%;
            height: 100%;
            fill: none;
            stroke: currentColor;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
            display: block;
        }
        
        .input-with-icon input,
        .input-with-icon select {
            width: 100%;
            padding: 12px 12px 12px 42px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            line-height: 1.5;
            transition: border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
            position: relative;
            z-index: 1;
            background-color: #fff;
        }
        
        .input-with-icon input:focus,
        .input-with-icon select:focus {
            outline: none;
            border-color: #4361ee;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }
        
        .input-with-icon:focus-within i {
            color: #4361ee;
        }
        
        /* Highlight để dễ nhìn */
        .highlight-text {
            background: linear-gradient(transparent 40%, rgba(255, 255, 0, 0.3) 40%, rgba(255, 255, 0, 0.3) 60%, transparent 60%);
        }
        
        .status {
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status.bad {
            background-color: #fee2e2;
            color: #dc2626;
        }
        
        .status.good {
            background-color: #dcfce7;
            color: #16a34a;
        }
        
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🎯 Demo Căn Chỉnh Icon với Text</h1>
        <p>So sánh giữa phương pháp căn giữa theo chiều cao (cũ) và căn chỉnh với text (mới)</p>
        
        <div class="comparison">
            <div class="method old">
                <h3>❌ Phương pháp cũ</h3>
                <p><code>top: 50%; transform: translateY(-50%);</code></p>
                
                <div class="form-group">
                    <label>Giới tính:</label>
                    <div class="input-with-icon">
                        <i class="icon-user">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </i>
                        <select>
                            <option class="highlight-text">Chọn giới tính</option>
                            <option>Nam</option>
                            <option>Nữ</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Họ và tên:</label>
                    <div class="input-with-icon">
                        <i class="icon-user">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </i>
                        <input type="text" placeholder="Nhập họ và tên" class="highlight-text">
                    </div>
                </div>
                
                <div class="status bad">
                    Icon không thẳng hàng với text, nằm ở giữa ô input
                </div>
            </div>
            
            <div class="method new">
                <h3>✅ Phương pháp mới</h3>
                <p><code>top: 12px;</code> (bằng với padding-top)</p>
                
                <div class="form-group">
                    <label>Giới tính:</label>
                    <div class="input-with-icon">
                        <i class="icon-user">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </i>
                        <select>
                            <option class="highlight-text">Chọn giới tính</option>
                            <option>Nam</option>
                            <option>Nữ</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Họ và tên:</label>
                    <div class="input-with-icon">
                        <i class="icon-user">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </i>
                        <input type="text" placeholder="Nhập họ và tên" class="highlight-text">
                    </div>
                </div>
                
                <div class="status good">
                    Icon thẳng hàng với text, tạo cảm giác tự nhiên hơn
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
            <h3>📝 Giải thích:</h3>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li><strong>Phương pháp cũ:</strong> Icon được căn giữa theo chiều cao của ô input, khiến nó không thẳng hàng với text</li>
                <li><strong>Phương pháp mới:</strong> Icon được đặt ở vị trí <code>top: 12px</code> (bằng với <code>padding-top</code> của input), làm cho nó thẳng hàng với dòng text đầu tiên</li>
                <li><strong>Kết quả:</strong> Giao diện trông tự nhiên và chuyên nghiệp hơn</li>
            </ul>
        </div>
    </div>
</body>
</html>
